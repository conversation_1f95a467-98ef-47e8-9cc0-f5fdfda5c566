.pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/esp_port/port/src/esp_nimble_mem.c.o: \
 .pio/libdeps/freenove_esp32_s3_wroom/NimBLE-Arduino/src/nimble/esp_port/port/src/esp_nimble_mem.c \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_attr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/qio_opi/include/sdkconfig.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/heap/include/esp_heap_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/heap/include/multi_heap.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_err.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_compiler.h \
 .pio/libdeps/freenove_esp32_s3_wroom/NimBLE-Arduino/src/nimconfig.h \
 .pio/libdeps/freenove_esp32_s3_wroom/NimBLE-Arduino/src/nimconfig_rename.h \
 .pio/libdeps/freenove_esp32_s3_wroom/NimBLE-Arduino/src/nimble/esp_port/port/src/../include/esp_nimble_mem.h

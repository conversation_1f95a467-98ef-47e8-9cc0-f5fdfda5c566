# ESP32设备唯一标识系统

## 概述

本系统通过ESP32的MAC地址为每个设备生成唯一标识，解决了多个相同ESP32设备无法区分的问题。

## 功能特性

### 1. 唯一蓝牙设备名
- **格式**: `ESP32-Stepper-XXXX`
- **示例**: `ESP32-Stepper-A1B2`
- **说明**: 使用MAC地址后2字节生成4位十六进制后缀

### 2. 唯一WiFi主机名
- **格式**: `ESP32-Stepper-XXXX`
- **示例**: `ESP32-Stepper-A1B2`
- **说明**: 与蓝牙设备名使用相同的后缀格式

### 3. 唯一版本响应
- **格式**: `=XXXXXX\r`
- **示例**: `=A1B2C3\r`
- **说明**: 使用MAC地址后3字节生成6位十六进制版本号，保持Skywatcher协议兼容性

## 实现原理

### MAC地址获取
```cpp
uint8_t mac[6];
esp_read_mac(mac, ESP_MAC_WIFI_STA);
```

### 后缀生成算法
- 使用MAC地址的第5和第6字节（mac[4]和mac[5]）
- 转换为大写十六进制字符串
- 格式：`XXYY`（4位字符）

### 版本号生成算法
- 使用MAC地址的第4、第5和第6字节（mac[3]、mac[4]和mac[5]）
- 组合为24位整数：`(mac[3] << 16) | (mac[4] << 8) | mac[5]`
- 转换为6位十六进制字符串

## 使用方法

### 1. 包含头文件
```cpp
#include "ble_manager.h"  // 包含DeviceIdentifier类
```

### 2. 获取设备后缀
```cpp
String suffix = DeviceIdentifier::getDeviceSuffix();
// 返回: "A1B2"
```

### 3. 获取唯一设备名
```cpp
String uniqueName = DeviceIdentifier::getUniqueDeviceName("ESP32-Stepper");
// 返回: "ESP32-Stepper-A1B2"
```

### 4. 获取唯一主机名
```cpp
String hostname = DeviceIdentifier::getUniqueHostname("ESP32-Stepper");
WiFi.setHostname(hostname.c_str());
```

### 5. 获取唯一版本响应
```cpp
String version = DeviceIdentifier::getUniqueVersionResponse();
// 返回: "=A1B2C3\r"
```

## 集成说明

### BLE管理器集成
```cpp
void BLEManager::init() {
    String uniqueName = DeviceIdentifier::getUniqueDeviceName(BLE_DEVICE_BASE_NAME);
    BLEDevice::init(uniqueName.c_str());
    // ... 其他初始化代码
}
```

### WiFi管理器集成
```cpp
void WiFiManager::taskWiFi(void* parameter) {
    WiFi.mode(WIFI_STA);
    String uniqueHostname = DeviceIdentifier::getUniqueHostname("ESP32-Stepper");
    WiFi.setHostname(uniqueHostname.c_str());
    // ... 其他WiFi配置
}
```

### 串口命令集成
```cpp
case 'e': // InquireMotorBoardVersion
    Serial.print(DeviceIdentifier::getUniqueVersionResponse());
    break;
```

## 示例场景

### 场景1：多设备蓝牙连接
假设有3个ESP32设备，MAC地址分别为：
- 设备1: `AA:BB:CC:DD:A1:B2` → 蓝牙名称: `ESP32-Stepper-A1B2`
- 设备2: `AA:BB:CC:DD:C3:D4` → 蓝牙名称: `ESP32-Stepper-C3D4`
- 设备3: `AA:BB:CC:DD:E5:F6` → 蓝牙名称: `ESP32-Stepper-E5F6`

### 场景2：网络设备识别
在同一WiFi网络中：
- 设备1主机名: `ESP32-Stepper-A1B2`
- 设备2主机名: `ESP32-Stepper-C3D4`
- 设备3主机名: `ESP32-Stepper-E5F6`

### 场景3：Skywatcher协议版本查询
```
客户端发送: :e1
设备1响应: =DDA1B2\r
设备2响应: =DDC3D4\r
设备3响应: =DDE5F6\r
```

## 优势

1. **唯一性保证**: 基于硬件MAC地址，确保每个设备标识唯一
2. **协议兼容**: 保持与现有Skywatcher协议的完全兼容性
3. **易于识别**: 简短的4位后缀便于用户识别和记忆
4. **自动生成**: 无需手动配置，系统自动生成唯一标识
5. **持久稳定**: MAC地址固化在硬件中，标识永不改变

## 注意事项

1. **MAC地址获取**: 确保在WiFi初始化后获取MAC地址
2. **内存管理**: DeviceIdentifier使用静态变量缓存结果，避免重复计算
3. **字符串格式**: 所有十六进制字符串均为大写格式
4. **协议兼容**: 版本响应格式严格遵循Skywatcher协议规范

## 测试验证

运行测试程序 `examples/device_identification_test.cpp` 来验证功能：

```cpp
// 编译并上传测试程序
// 通过串口监视器查看输出结果
```

测试输出示例：
```
=== ESP32设备唯一标识测试 ===
原始MAC地址: AA:BB:CC:DD:A1:B2

=== 设备唯一标识信息 ===
设备后缀: A1B2
唯一蓝牙设备名: ESP32-Stepper-A1B2
唯一WiFi主机名: ESP32-Stepper-A1B2
唯一版本响应: =DDA1B2\r
```

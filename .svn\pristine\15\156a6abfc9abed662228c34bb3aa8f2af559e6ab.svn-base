#include "serial_command.h"
#include "preferences_manager.h"
#include <Arduino.h>

PreferencesManager prefsManager;

void SerialCommand::setup() {
    Serial.begin(38400);
    Serial.println("Commands: p[val] i[val] d[val] c (calibrate)");
}

void SerialCommand::process(PIDController& pid, Encoder& encoder, MotorControl& motor) {
    if(Serial.available()) {
        char cmd = Serial.read();
        switch(cmd) {
            case 'p':
            case 'i':
            case 'd': {
                float value = Serial.parseFloat();
                handlePIDCommand(cmd, value, pid);
                break;
            }
            case 'c':
                handleCalibrateCommand(encoder, motor);
                break;
        }
    }
}

void SerialCommand::handlePIDCommand(char cmd, float value, PIDController& pid) {
    prefsManager.begin();
    switch(cmd) {
        case 'p':
            prefsManager.putFloat("kp", value);
            pid.setKp(value);
            break;
        case 'i':
            prefsManager.putFloat("ki", value);
            pid.setKi(value);
            break;
        case 'd':
            prefsManager.putFloat("kd", value);
            pid.setKd(value);
            break;
    }
    prefsManager.end();
    Serial.print("Set "); Serial.print(cmd); 
    Serial.print(" to: "); Serial.println(value);
}

void SerialCommand::handleCalibrateCommand(Encoder& encoder, MotorControl& motor) {
    encoder.calibrate(motor);
}

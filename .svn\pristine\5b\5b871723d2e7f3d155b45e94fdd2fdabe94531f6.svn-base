#include "pid_controller.h"
#include <Arduino.h>
PIDController::PIDController(float kp, float ki, float kd) 
    : kp(kp), ki(ki), kd(kd), integral(0), prev_error(0), target(0) {
}

float PIDController::calculate(float error) {
    integral += error;
    
    // 抗积分饱和
    const float MAX_INTEGRAL = 100.0f; // 根据实际需求调整
    // if (integral > MAX_INTEGRAL) {
    //     integral = MAX_INTEGRAL;
    // } else if (integral < -MAX_INTEGRAL) {
    //     integral = -MAX_INTEGRAL;
    // }
    integral = constrain(integral, -MAX_INTEGRAL, MAX_INTEGRAL); 
    
    float derivative = error - prev_error;
    prev_error = error;
    
    float output = kp * error + ki * integral + kd * derivative;
    output = constrain(output, -40.0f, 40.0f); // 限制输出在±50%范围内
    return output;
}

void PIDController::reset() {
    integral = 0;
    prev_error = 0;
}

void PIDController::setKp(float kp) {
    this->kp = kp;
}

void PIDController::setKi(float ki) {
    this->ki = ki;
}

void PIDController::setKd(float kd) {
    this->kd = kd;
}

float PIDController::getTarget() const {
    return target;
}

#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSerifItalic12pt7bBitmaps[] PROGMEM = {
    0x0C, 0x31, 0xC6, 0x18, 0x43, 0x0C, 0x20, 0x84, 0x10, 0x03, 0x0C, 0x30,
    0x66, 0xCD, 0x12, 0x24, 0x51, 0x00, 0x03, 0x10, 0x11, 0x80, 0x8C, 0x0C,
    0x40, 0x46, 0x1F, 0xFC, 0x21, 0x01, 0x18, 0x18, 0x80, 0x84, 0x3F, 0xF8,
    0x62, 0x02, 0x30, 0x31, 0x01, 0x08, 0x08, 0xC0, 0x00, 0x40, 0x08, 0x07,
    0xC0, 0xCA, 0x18, 0xA1, 0x92, 0x19, 0x01, 0xD0, 0x0F, 0x00, 0x78, 0x03,
    0xC0, 0x2E, 0x02, 0x64, 0x46, 0x44, 0x64, 0x46, 0x64, 0xC1, 0xF0, 0x08,
    0x00, 0x80, 0x00, 0x08, 0x0F, 0x0C, 0x0C, 0x7C, 0x0C, 0x22, 0x06, 0x12,
    0x06, 0x09, 0x03, 0x09, 0x01, 0x84, 0x80, 0xC4, 0x8F, 0x3C, 0x4C, 0x40,
    0x4C, 0x20, 0x4E, 0x10, 0x26, 0x08, 0x23, 0x08, 0x11, 0x84, 0x10, 0xC4,
    0x08, 0x3C, 0x00, 0x00, 0xE0, 0x02, 0x60, 0x0C, 0xC0, 0x19, 0x80, 0x36,
    0x00, 0x70, 0x00, 0xC0, 0x07, 0x9F, 0x33, 0x08, 0xC3, 0x13, 0x06, 0x46,
    0x0D, 0x0C, 0x0C, 0x18, 0x1C, 0x1C, 0x5C, 0x9F, 0x1E, 0xFA, 0xA0, 0x02,
    0x08, 0x20, 0xC3, 0x06, 0x18, 0x30, 0xE1, 0x83, 0x06, 0x0C, 0x18, 0x30,
    0x60, 0x40, 0x80, 0x81, 0x00, 0x08, 0x10, 0x10, 0x20, 0x40, 0xC1, 0x83,
    0x06, 0x0C, 0x18, 0x70, 0xC1, 0x83, 0x0C, 0x10, 0x41, 0x04, 0x00, 0x18,
    0x18, 0x18, 0x93, 0x74, 0x38, 0xD7, 0x93, 0x18, 0x18, 0x04, 0x00, 0x80,
    0x10, 0x02, 0x00, 0x41, 0xFF, 0xC1, 0x00, 0x20, 0x04, 0x00, 0x80, 0x10,
    0x00, 0x6C, 0x95, 0x00, 0xF8, 0xFC, 0x00, 0x40, 0x18, 0x02, 0x00, 0xC0,
    0x30, 0x06, 0x01, 0x80, 0x20, 0x0C, 0x01, 0x00, 0x60, 0x18, 0x03, 0x00,
    0xC0, 0x10, 0x06, 0x00, 0x07, 0x81, 0x98, 0x61, 0x18, 0x33, 0x06, 0xC0,
    0xD8, 0x1B, 0x03, 0xE0, 0xF8, 0x1F, 0x03, 0x60, 0x6C, 0x19, 0x83, 0x10,
    0xC3, 0x30, 0x3C, 0x00, 0x01, 0x87, 0xC0, 0xC0, 0x60, 0x30, 0x18, 0x18,
    0x0C, 0x06, 0x07, 0x03, 0x01, 0x80, 0xC0, 0xC0, 0x60, 0x30, 0xFE, 0x00,
    0x0F, 0x0C, 0x64, 0x0C, 0x03, 0x00, 0xC0, 0x20, 0x18, 0x0C, 0x02, 0x01,
    0x00, 0x80, 0x40, 0x20, 0x10, 0x2F, 0xF0, 0x07, 0x86, 0x30, 0x0C, 0x03,
    0x01, 0x81, 0x81, 0xF0, 0x1E, 0x03, 0x80, 0x60, 0x18, 0x06, 0x01, 0x00,
    0xCC, 0x63, 0xE0, 0x00, 0x20, 0x0C, 0x03, 0x80, 0xA0, 0x2C, 0x09, 0x82,
    0x30, 0x84, 0x31, 0x8C, 0x33, 0x06, 0x7F, 0xE0, 0x30, 0x06, 0x00, 0x80,
    0x30, 0x03, 0xE1, 0x80, 0x20, 0x06, 0x00, 0xF0, 0x0F, 0x00, 0x60, 0x06,
    0x00, 0xC0, 0x18, 0x03, 0x00, 0x40, 0x18, 0x02, 0x30, 0x87, 0xE0, 0x00,
    0x70, 0x3C, 0x07, 0x00, 0xE0, 0x1C, 0x03, 0x80, 0x7F, 0x07, 0x18, 0x60,
    0xCE, 0x0C, 0xC0, 0xCC, 0x0C, 0xC0, 0xCC, 0x18, 0x41, 0x86, 0x30, 0x3E,
    0x00, 0x7F, 0xF0, 0x18, 0x03, 0x00, 0xC0, 0x10, 0x06, 0x01, 0x80, 0x30,
    0x0C, 0x01, 0x00, 0x60, 0x08, 0x03, 0x00, 0xC0, 0x10, 0x06, 0x00, 0x0F,
    0x83, 0x18, 0xC1, 0x98, 0x33, 0x06, 0x71, 0x87, 0x60, 0x70, 0x17, 0x0C,
    0x71, 0x07, 0x60, 0x6C, 0x0D, 0x81, 0xB0, 0x63, 0x1C, 0x3E, 0x00, 0x07,
    0x83, 0x18, 0xC1, 0x18, 0x36, 0x06, 0xC0, 0xD8, 0x1B, 0x07, 0x60, 0xE6,
    0x38, 0x7F, 0x00, 0xC0, 0x30, 0x0C, 0x07, 0x03, 0xC0, 0xC0, 0x00, 0x33,
    0x30, 0x00, 0x00, 0xCC, 0xC0, 0x18, 0xC6, 0x00, 0x00, 0x00, 0x03, 0x18,
    0x44, 0x40, 0x00, 0x00, 0x03, 0x00, 0xF0, 0x38, 0x1E, 0x07, 0x80, 0xE0,
    0x0F, 0x00, 0x1C, 0x00, 0x78, 0x01, 0xE0, 0x07, 0x00, 0x10, 0xFF, 0xF0,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0x00, 0x0C, 0x00, 0xF0, 0x01,
    0xC0, 0x07, 0x80, 0x1E, 0x00, 0x70, 0x0F, 0x03, 0xC1, 0xE0, 0x78, 0x0E,
    0x00, 0x80, 0x00, 0x3E, 0x21, 0x90, 0x60, 0x30, 0x38, 0x38, 0x30, 0x30,
    0x20, 0x20, 0x10, 0x00, 0x00, 0x06, 0x03, 0x01, 0x80, 0x07, 0xE0, 0x1C,
    0x18, 0x30, 0x04, 0x60, 0x02, 0x61, 0xDA, 0xC3, 0x31, 0xC6, 0x31, 0xC4,
    0x31, 0xCC, 0x31, 0xCC, 0x21, 0xCC, 0x62, 0x6C, 0xE4, 0x67, 0x38, 0x30,
    0x00, 0x1C, 0x08, 0x07, 0xF0, 0x00, 0x20, 0x00, 0xC0, 0x03, 0x80, 0x0B,
    0x00, 0x16, 0x00, 0x4E, 0x00, 0x9C, 0x02, 0x18, 0x08, 0x30, 0x1F, 0xE0,
    0x40, 0xC1, 0x81, 0xC2, 0x03, 0x8C, 0x07, 0x3C, 0x1F, 0x80, 0x1F, 0xF0,
    0x1C, 0x60, 0x60, 0xC1, 0x83, 0x06, 0x0C, 0x38, 0x60, 0xC3, 0x03, 0xF0,
    0x1C, 0x30, 0x60, 0x61, 0x81, 0x86, 0x06, 0x38, 0x18, 0xC0, 0xC3, 0x06,
    0x3F, 0xF0, 0x01, 0xF9, 0x06, 0x0F, 0x1C, 0x06, 0x38, 0x02, 0x30, 0x02,
    0x60, 0x00, 0x60, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00,
    0xC0, 0x00, 0xC0, 0x08, 0x60, 0x10, 0x30, 0x60, 0x1F, 0x80, 0x1F, 0xF0,
    0x07, 0x0C, 0x06, 0x06, 0x06, 0x06, 0x06, 0x03, 0x0E, 0x03, 0x0C, 0x03,
    0x0C, 0x03, 0x1C, 0x03, 0x1C, 0x07, 0x18, 0x06, 0x18, 0x06, 0x38, 0x0C,
    0x30, 0x18, 0x30, 0x70, 0xFF, 0x80, 0x1F, 0xFF, 0x07, 0x07, 0x06, 0x02,
    0x06, 0x02, 0x06, 0x00, 0x0E, 0x10, 0x0C, 0x30, 0x0F, 0xF0, 0x1C, 0x20,
    0x18, 0x20, 0x18, 0x00, 0x18, 0x00, 0x38, 0x04, 0x30, 0x08, 0x30, 0x38,
    0xFF, 0xF8, 0x1F, 0xFF, 0x07, 0x07, 0x07, 0x02, 0x06, 0x02, 0x06, 0x00,
    0x0E, 0x10, 0x0C, 0x30, 0x0F, 0xF0, 0x1C, 0x20, 0x1C, 0x20, 0x18, 0x00,
    0x18, 0x00, 0x38, 0x00, 0x30, 0x00, 0x30, 0x00, 0xFC, 0x00, 0x01, 0xF1,
    0x06, 0x0F, 0x18, 0x07, 0x38, 0x02, 0x30, 0x02, 0x60, 0x00, 0x60, 0x00,
    0xE0, 0x00, 0xC0, 0x7F, 0xC0, 0x1C, 0xC0, 0x1C, 0xC0, 0x18, 0xC0, 0x18,
    0x60, 0x18, 0x30, 0x38, 0x0F, 0xC0, 0x1F, 0xC7, 0xE0, 0xE0, 0x70, 0x18,
    0x0E, 0x03, 0x01, 0x80, 0x60, 0x30, 0x1C, 0x0E, 0x03, 0x01, 0x80, 0x7F,
    0xF0, 0x1C, 0x06, 0x03, 0x01, 0xC0, 0x60, 0x30, 0x0C, 0x06, 0x03, 0x81,
    0xC0, 0x60, 0x38, 0x0C, 0x06, 0x07, 0xE3, 0xF0, 0x1F, 0x83, 0x81, 0x80,
    0xC0, 0x60, 0x70, 0x30, 0x18, 0x1C, 0x0C, 0x06, 0x03, 0x03, 0x81, 0x80,
    0xC1, 0xF8, 0x03, 0xF0, 0x0C, 0x00, 0xC0, 0x1C, 0x01, 0x80, 0x18, 0x03,
    0x80, 0x30, 0x03, 0x00, 0x30, 0x07, 0x00, 0x60, 0x06, 0x0C, 0xE0, 0xCC,
    0x07, 0x80, 0x1F, 0xCF, 0x83, 0x83, 0x81, 0x81, 0x00, 0xC3, 0x00, 0x62,
    0x00, 0x72, 0x00, 0x36, 0x00, 0x1E, 0x00, 0x1D, 0x80, 0x0C, 0xE0, 0x06,
    0x30, 0x03, 0x1C, 0x03, 0x87, 0x01, 0x81, 0x80, 0xC0, 0xE1, 0xF9, 0xFC,
    0x1F, 0xC0, 0x1C, 0x00, 0x60, 0x01, 0x80, 0x06, 0x00, 0x38, 0x00, 0xC0,
    0x03, 0x00, 0x1C, 0x00, 0x60, 0x01, 0x80, 0x06, 0x00, 0x38, 0x0C, 0xC0,
    0x23, 0x03, 0xBF, 0xFE, 0x0F, 0x00, 0x78, 0x38, 0x07, 0x81, 0xC0, 0x38,
    0x0E, 0x02, 0xC0, 0x70, 0x3E, 0x05, 0xC1, 0x70, 0x2E, 0x13, 0x01, 0x31,
    0x98, 0x11, 0x89, 0xC0, 0x8C, 0x8C, 0x04, 0x6C, 0x60, 0x23, 0x43, 0x02,
    0x1C, 0x38, 0x10, 0xE1, 0x81, 0x86, 0x1C, 0x1F, 0x23, 0xF8, 0x1E, 0x07,
    0xC1, 0xC0, 0x60, 0x70, 0x10, 0x1C, 0x0C, 0x05, 0x82, 0x02, 0x60, 0x80,
    0x9C, 0x60, 0x23, 0x10, 0x10, 0xC4, 0x04, 0x19, 0x01, 0x06, 0xC0, 0x40,
    0xE0, 0x20, 0x38, 0x08, 0x0E, 0x06, 0x01, 0x03, 0xE0, 0x40, 0x01, 0xF0,
    0x0C, 0x10, 0x30, 0x10, 0xC0, 0x33, 0x00, 0x6E, 0x00, 0xD8, 0x01, 0xF0,
    0x03, 0xC0, 0x0D, 0x80, 0x1B, 0x00, 0x76, 0x00, 0xCC, 0x03, 0x08, 0x0C,
    0x18, 0x70, 0x0F, 0x80, 0x1F, 0xF0, 0x1C, 0x60, 0x60, 0xC1, 0x83, 0x06,
    0x0C, 0x38, 0x30, 0xC1, 0x83, 0x0E, 0x1F, 0xE0, 0x60, 0x01, 0x80, 0x06,
    0x00, 0x38, 0x00, 0xC0, 0x03, 0x00, 0x3F, 0x00, 0x01, 0xF0, 0x06, 0x10,
    0x30, 0x30, 0xC0, 0x33, 0x00, 0x66, 0x00, 0xD8, 0x01, 0xB0, 0x03, 0xE0,
    0x0F, 0x80, 0x1B, 0x00, 0x36, 0x00, 0xCC, 0x03, 0x98, 0x06, 0x18, 0x18,
    0x18, 0xC0, 0x0E, 0x00, 0x20, 0x01, 0xF8, 0x36, 0x7F, 0x80, 0x1F, 0xF0,
    0x1C, 0x60, 0x60, 0xC1, 0x83, 0x06, 0x0C, 0x38, 0x70, 0xC3, 0x83, 0xF8,
    0x1D, 0xC0, 0x63, 0x01, 0x8C, 0x06, 0x18, 0x38, 0x60, 0xC1, 0xC3, 0x03,
    0x3F, 0x0F, 0x07, 0x90, 0xC7, 0x18, 0x21, 0x82, 0x18, 0x01, 0xC0, 0x0E,
    0x00, 0x70, 0x03, 0x80, 0x1C, 0x00, 0xC4, 0x0C, 0x40, 0xC6, 0x08, 0xE1,
    0x89, 0xE0, 0x7F, 0xFE, 0xC7, 0x1D, 0x0C, 0x14, 0x18, 0x20, 0x70, 0x00,
    0xE0, 0x01, 0x80, 0x03, 0x00, 0x0E, 0x00, 0x18, 0x00, 0x30, 0x00, 0x60,
    0x01, 0xC0, 0x03, 0x00, 0x0E, 0x00, 0x7F, 0x80, 0x7E, 0x1F, 0x38, 0x0C,
    0x38, 0x0C, 0x30, 0x08, 0x30, 0x08, 0x70, 0x08, 0x70, 0x10, 0x60, 0x10,
    0x60, 0x10, 0xE0, 0x10, 0xC0, 0x20, 0xC0, 0x20, 0xC0, 0x60, 0xC0, 0x40,
    0x61, 0x80, 0x3F, 0x00, 0xFC, 0x3E, 0xE0, 0x18, 0xC0, 0x21, 0x80, 0xC3,
    0x81, 0x07, 0x04, 0x0E, 0x08, 0x0C, 0x20, 0x18, 0x80, 0x31, 0x00, 0x64,
    0x00, 0xF0, 0x01, 0xE0, 0x01, 0x80, 0x02, 0x00, 0x04, 0x00, 0xFD, 0xF8,
    0xF7, 0x07, 0x06, 0x30, 0x60, 0x63, 0x07, 0x04, 0x30, 0x70, 0x83, 0x8F,
    0x08, 0x38, 0xB1, 0x03, 0x93, 0x10, 0x19, 0x32, 0x01, 0xA3, 0x20, 0x1A,
    0x34, 0x01, 0xC3, 0x40, 0x1C, 0x38, 0x01, 0x83, 0x00, 0x18, 0x30, 0x01,
    0x02, 0x00, 0x1F, 0x9F, 0x0E, 0x06, 0x06, 0x04, 0x07, 0x08, 0x03, 0x10,
    0x03, 0x20, 0x03, 0xC0, 0x01, 0x80, 0x01, 0xC0, 0x03, 0xC0, 0x06, 0xE0,
    0x0C, 0x60, 0x18, 0x60, 0x30, 0x70, 0x70, 0x78, 0xF8, 0xFC, 0xFC, 0xFB,
    0x81, 0x8C, 0x08, 0x60, 0x83, 0x8C, 0x0C, 0xC0, 0x64, 0x03, 0xC0, 0x0C,
    0x00, 0xE0, 0x07, 0x00, 0x30, 0x01, 0x80, 0x1C, 0x00, 0xC0, 0x1F, 0xC0,
    0x1F, 0xFE, 0x30, 0x38, 0xC0, 0xF1, 0x01, 0xC0, 0x07, 0x00, 0x1C, 0x00,
    0x70, 0x01, 0xE0, 0x03, 0x80, 0x0E, 0x00, 0x38, 0x00, 0xE0, 0x01, 0xC0,
    0x47, 0x01, 0x1C, 0x06, 0x7F, 0xF8, 0x07, 0x04, 0x08, 0x08, 0x08, 0x18,
    0x10, 0x10, 0x10, 0x20, 0x20, 0x20, 0x20, 0x40, 0x40, 0x40, 0x80, 0x80,
    0x80, 0xE0, 0xC0, 0xC0, 0x40, 0x60, 0x20, 0x30, 0x30, 0x18, 0x18, 0x08,
    0x0C, 0x04, 0x06, 0x06, 0x03, 0x03, 0x0E, 0x04, 0x08, 0x10, 0x60, 0x81,
    0x02, 0x04, 0x18, 0x20, 0x40, 0x81, 0x02, 0x08, 0x10, 0x20, 0x47, 0x80,
    0x0C, 0x03, 0x81, 0xE0, 0x4C, 0x33, 0x08, 0x66, 0x19, 0x03, 0xC0, 0xC0,
    0xFF, 0xF0, 0xCE, 0x63, 0x07, 0xA0, 0xCE, 0x18, 0x63, 0x04, 0x60, 0xC6,
    0x0C, 0xC0, 0xCC, 0x18, 0xC3, 0x8C, 0x5A, 0x79, 0xC0, 0x38, 0x06, 0x01,
    0x80, 0x40, 0x30, 0x0C, 0xE3, 0xCC, 0xC3, 0x70, 0xD8, 0x36, 0x19, 0x06,
    0xC3, 0x30, 0x8C, 0xC3, 0xE0, 0x0F, 0x0C, 0xCC, 0x6C, 0x06, 0x06, 0x03,
    0x01, 0x80, 0xC0, 0x73, 0x1E, 0x00, 0x00, 0x70, 0x01, 0x80, 0x0C, 0x00,
    0x60, 0x02, 0x03, 0xF0, 0x31, 0x83, 0x08, 0x30, 0xC3, 0x06, 0x18, 0x31,
    0x81, 0x8C, 0x18, 0x61, 0xCB, 0x16, 0x8F, 0x38, 0x07, 0x19, 0x31, 0x63,
    0x62, 0xEC, 0xD0, 0xC0, 0xC0, 0xE6, 0x78, 0x00, 0x38, 0x01, 0x30, 0x0C,
    0x00, 0x20, 0x01, 0x80, 0x06, 0x00, 0xFE, 0x00, 0x40, 0x03, 0x00, 0x0C,
    0x00, 0x30, 0x00, 0x80, 0x06, 0x00, 0x18, 0x00, 0x60, 0x01, 0x80, 0x04,
    0x00, 0x30, 0x00, 0xC0, 0x02, 0x00, 0x90, 0x03, 0x80, 0x00, 0x07, 0xC0,
    0xC7, 0x18, 0x61, 0x86, 0x18, 0xE1, 0x8C, 0x07, 0x80, 0x80, 0x1C, 0x00,
    0xF0, 0x33, 0x84, 0x18, 0x80, 0x88, 0x08, 0x61, 0x03, 0xE0, 0x1C, 0x00,
    0xC0, 0x0C, 0x00, 0xC0, 0x18, 0x01, 0x8E, 0x1B, 0x61, 0xC6, 0x38, 0x63,
    0x8C, 0x30, 0xC3, 0x0C, 0x60, 0xC6, 0x1A, 0x61, 0xA4, 0x1C, 0x18, 0xC6,
    0x00, 0x0B, 0xC6, 0x23, 0x18, 0x8C, 0x63, 0x5C, 0x01, 0x80, 0xC0, 0x60,
    0x00, 0x00, 0x0C, 0x1E, 0x02, 0x03, 0x01, 0x80, 0xC0, 0x40, 0x60, 0x30,
    0x18, 0x08, 0x0C, 0x06, 0x02, 0x1B, 0x0F, 0x00, 0x1C, 0x01, 0x80, 0x30,
    0x06, 0x01, 0x80, 0x33, 0xC6, 0x30, 0x88, 0x32, 0x06, 0x80, 0xF0, 0x1B,
    0x06, 0x60, 0xC4, 0x18, 0xD2, 0x0C, 0x3C, 0x61, 0x86, 0x18, 0xC3, 0x0C,
    0x21, 0x86, 0x18, 0x43, 0x2D, 0x38, 0x78, 0xE7, 0x0D, 0xB5, 0x8D, 0x1C,
    0xC7, 0x0C, 0x63, 0x8E, 0x31, 0x86, 0x30, 0xC3, 0x18, 0xC1, 0x0C, 0x61,
    0x84, 0xB0, 0xC6, 0xB0, 0x63, 0x80, 0x78, 0xE1, 0xB6, 0x14, 0x63, 0x84,
    0x38, 0xC3, 0x0C, 0x70, 0x86, 0x18, 0x61, 0x96, 0x1A, 0xC1, 0xC0, 0x0F,
    0x06, 0x63, 0x0D, 0x83, 0x60, 0xF0, 0x3C, 0x1B, 0x06, 0xC3, 0x39, 0x87,
    0x80, 0x1E, 0xF0, 0x39, 0xC1, 0x86, 0x0C, 0x30, 0xC1, 0x86, 0x0C, 0x30,
    0xC3, 0x06, 0x18, 0x60, 0xC6, 0x07, 0xC0, 0x60, 0x03, 0x00, 0x18, 0x00,
    0xC0, 0x1F, 0x00, 0x07, 0x81, 0x9C, 0x63, 0x98, 0x76, 0x0C, 0xC1, 0xB0,
    0x76, 0x0E, 0xC3, 0x98, 0xB1, 0xE6, 0x00, 0x80, 0x30, 0x06, 0x00, 0xC0,
    0xFC, 0x79, 0x8F, 0xC5, 0x07, 0x03, 0x01, 0x80, 0xC0, 0xC0, 0x60, 0x30,
    0x10, 0x00, 0x1E, 0x98, 0xCC, 0x27, 0x11, 0x80, 0xE0, 0x39, 0x0C, 0x86,
    0x62, 0x2E, 0x00, 0x08, 0x67, 0xCC, 0x30, 0xC6, 0x18, 0x61, 0x8C, 0x34,
    0xE0, 0xF0, 0xCC, 0x19, 0x83, 0x30, 0xC6, 0x18, 0x87, 0x31, 0x66, 0x3C,
    0xCB, 0x1A, 0x6B, 0x8E, 0x00, 0x70, 0xCC, 0x33, 0x04, 0xC2, 0x18, 0x86,
    0x41, 0x90, 0x68, 0x1C, 0x06, 0x01, 0x00, 0x61, 0x0F, 0x84, 0x36, 0x30,
    0xDC, 0xC1, 0x35, 0x08, 0xD4, 0x23, 0x91, 0x0E, 0x48, 0x30, 0xE0, 0xC3,
    0x02, 0x08, 0x00, 0x0C, 0x63, 0x4A, 0x07, 0x00, 0x70, 0x06, 0x00, 0x20,
    0x07, 0x00, 0xB0, 0x0B, 0x21, 0x14, 0xE1, 0x80, 0x38, 0x63, 0x0C, 0x30,
    0x86, 0x10, 0xC4, 0x0C, 0x81, 0xA0, 0x34, 0x07, 0x00, 0x60, 0x08, 0x02,
    0x00, 0x40, 0x10, 0x04, 0x07, 0x00, 0x1F, 0x90, 0x80, 0x80, 0xC0, 0xC0,
    0x40, 0x60, 0x60, 0x60, 0x38, 0x3E, 0x03, 0xA0, 0x60, 0x00, 0x83, 0x81,
    0x01, 0x80, 0xC0, 0x40, 0x60, 0x30, 0x10, 0x10, 0x1C, 0x06, 0x03, 0x03,
    0x01, 0x80, 0xC0, 0x40, 0x60, 0x30, 0x18, 0x07, 0x00, 0xFF, 0xFF, 0x07,
    0x00, 0xC0, 0x60, 0x30, 0x10, 0x18, 0x0C, 0x06, 0x06, 0x03, 0x01, 0x80,
    0x60, 0x40, 0x60, 0x30, 0x10, 0x18, 0x0C, 0x06, 0x06, 0x06, 0x00, 0x78,
    0x18, 0x8C, 0x0F, 0x00};

const GFXglyph FreeSerifItalic12pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 6, 0, 1},          // 0x20 ' '
    {0, 6, 16, 8, 1, -15},       // 0x21 '!'
    {12, 7, 6, 8, 3, -15},       // 0x22 '"'
    {18, 13, 16, 12, 0, -15},    // 0x23 '#'
    {44, 12, 20, 12, 0, -17},    // 0x24 '$'
    {74, 17, 17, 20, 2, -16},    // 0x25 '%'
    {111, 15, 16, 19, 2, -15},   // 0x26 '&'
    {141, 2, 6, 5, 4, -15},      // 0x27 '''
    {143, 7, 20, 8, 1, -15},     // 0x28 '('
    {161, 7, 20, 8, 0, -15},     // 0x29 ')'
    {179, 8, 10, 12, 4, -15},    // 0x2A '*'
    {189, 11, 11, 16, 2, -10},   // 0x2B '+'
    {205, 3, 6, 6, 0, -2},       // 0x2C ','
    {208, 5, 1, 8, 1, -5},       // 0x2D '-'
    {209, 2, 3, 6, 1, -2},       // 0x2E '.'
    {210, 11, 16, 7, 0, -15},    // 0x2F '/'
    {232, 11, 17, 12, 1, -16},   // 0x30 '0'
    {256, 9, 17, 12, 1, -16},    // 0x31 '1'
    {276, 10, 15, 12, 1, -14},   // 0x32 '2'
    {295, 10, 16, 12, 1, -15},   // 0x33 '3'
    {315, 11, 16, 12, 0, -15},   // 0x34 '4'
    {337, 11, 16, 12, 0, -15},   // 0x35 '5'
    {359, 12, 17, 12, 1, -16},   // 0x36 '6'
    {385, 11, 16, 12, 2, -15},   // 0x37 '7'
    {407, 11, 17, 12, 1, -16},   // 0x38 '8'
    {431, 11, 17, 12, 1, -16},   // 0x39 '9'
    {455, 4, 11, 6, 1, -10},     // 0x3A ':'
    {461, 5, 14, 6, 0, -10},     // 0x3B ';'
    {470, 12, 13, 14, 1, -12},   // 0x3C '<'
    {490, 12, 6, 16, 2, -8},     // 0x3D '='
    {499, 12, 13, 14, 2, -12},   // 0x3E '>'
    {519, 9, 16, 11, 3, -15},    // 0x3F '?'
    {537, 16, 16, 19, 2, -15},   // 0x40 '@'
    {569, 15, 15, 16, 0, -14},   // 0x41 'A'
    {598, 14, 16, 14, 0, -15},   // 0x42 'B'
    {626, 16, 16, 15, 1, -15},   // 0x43 'C'
    {658, 16, 16, 17, 0, -15},   // 0x44 'D'
    {690, 16, 16, 14, 0, -15},   // 0x45 'E'
    {722, 16, 16, 14, 0, -15},   // 0x46 'F'
    {754, 16, 16, 17, 1, -15},   // 0x47 'G'
    {786, 19, 16, 17, 0, -15},   // 0x48 'H'
    {824, 9, 16, 8, 0, -15},     // 0x49 'I'
    {842, 12, 16, 10, 0, -15},   // 0x4A 'J'
    {866, 17, 16, 15, 0, -15},   // 0x4B 'K'
    {900, 14, 16, 14, 0, -15},   // 0x4C 'L'
    {928, 21, 16, 20, 0, -15},   // 0x4D 'M'
    {970, 18, 16, 16, 0, -15},   // 0x4E 'N'
    {1006, 15, 16, 16, 1, -15},  // 0x4F 'O'
    {1036, 14, 16, 14, 0, -15},  // 0x50 'P'
    {1064, 15, 20, 16, 1, -15},  // 0x51 'Q'
    {1102, 14, 16, 15, 0, -15},  // 0x52 'R'
    {1130, 12, 16, 11, 0, -15},  // 0x53 'S'
    {1154, 15, 16, 14, 2, -15},  // 0x54 'T'
    {1184, 16, 16, 17, 3, -15},  // 0x55 'U'
    {1216, 15, 16, 16, 3, -15},  // 0x56 'V'
    {1246, 20, 16, 21, 3, -15},  // 0x57 'W'
    {1286, 16, 16, 16, 0, -15},  // 0x58 'X'
    {1318, 13, 16, 14, 3, -15},  // 0x59 'Y'
    {1344, 15, 16, 14, 0, -15},  // 0x5A 'Z'
    {1374, 8, 20, 9, 1, -15},    // 0x5B '['
    {1394, 8, 16, 12, 3, -15},   // 0x5C '\'
    {1410, 7, 20, 9, 1, -15},    // 0x5D ']'
    {1428, 10, 9, 10, 0, -15},   // 0x5E '^'
    {1440, 12, 1, 12, 0, 3},     // 0x5F '_'
    {1442, 4, 4, 6, 3, -15},     // 0x60 '`'
    {1444, 12, 11, 12, 0, -10},  // 0x61 'a'
    {1461, 10, 16, 11, 1, -15},  // 0x62 'b'
    {1481, 9, 11, 10, 1, -10},   // 0x63 'c'
    {1494, 13, 16, 12, 0, -15},  // 0x64 'd'
    {1520, 8, 11, 10, 1, -10},   // 0x65 'e'
    {1531, 14, 22, 10, -2, -16}, // 0x66 'f'
    {1570, 12, 16, 11, -1, -10}, // 0x67 'g'
    {1594, 12, 16, 12, 0, -15},  // 0x68 'h'
    {1618, 5, 16, 6, 1, -15},    // 0x69 'i'
    {1628, 9, 21, 7, -2, -15},   // 0x6A 'j'
    {1652, 11, 16, 11, 0, -15},  // 0x6B 'k'
    {1674, 6, 16, 6, 1, -15},    // 0x6C 'l'
    {1686, 17, 11, 17, 0, -10},  // 0x6D 'm'
    {1710, 12, 11, 12, 0, -10},  // 0x6E 'n'
    {1727, 10, 11, 11, 1, -10},  // 0x6F 'o'
    {1741, 13, 16, 11, -2, -10}, // 0x70 'p'
    {1767, 11, 16, 12, 0, -10},  // 0x71 'q'
    {1789, 9, 11, 9, 0, -10},    // 0x72 'r'
    {1802, 9, 11, 8, 0, -10},    // 0x73 's'
    {1815, 6, 13, 6, 1, -12},    // 0x74 't'
    {1825, 11, 11, 12, 1, -10},  // 0x75 'u'
    {1841, 10, 11, 11, 1, -10},  // 0x76 'v'
    {1855, 14, 11, 16, 2, -10},  // 0x77 'w'
    {1875, 12, 11, 10, -1, -10}, // 0x78 'x'
    {1892, 11, 16, 11, 0, -10},  // 0x79 'y'
    {1914, 9, 13, 9, 0, -10},    // 0x7A 'z'
    {1929, 9, 21, 10, 1, -16},   // 0x7B '{'
    {1953, 1, 16, 7, 3, -15},    // 0x7C '|'
    {1955, 9, 21, 10, 0, -16},   // 0x7D '}'
    {1979, 11, 3, 13, 1, -6}};   // 0x7E '~'

const GFXfont FreeSerifItalic12pt7b PROGMEM = {
    (uint8_t *)FreeSerifItalic12pt7bBitmaps,
    (GFXglyph *)FreeSerifItalic12pt7bGlyphs, 0x20, 0x7E, 29};

// Approx. 2656 bytes

#include "wifi_manager.h"
#include "ble_manager.h"  // 包含DeviceIdentifier
#include <Arduino.h>
#include <stdio.h>

WiFiManager::WiFiManager(const char* ssid, const char* password) 
    : _ssid(ssid), _password(password) {}

// void WiFiManager::begin() {
//     xTaskCreatePinnedToCore(
//         [](void* param) { ((WiFiManager*)param)->taskWiFi(nullptr); },
//         "taskWiFi",
//         4096,  // Increased stack size to prevent crashes
//         this,
//         2,
//         nullptr,
//         0
//     );
// }

void WiFiManager::taskWiFi(void* parameter) {
    // ESP32-S3 specific WiFi configuration
    WiFi.mode(WIFI_STA);
    WiFi.setTxPower(WIFI_POWER_19_5dBm);
    WiFi.setAutoReconnect(true);
    WiFi.persistent(true);

    // 设置基于MAC地址的唯一主机名
    String uniqueHostname = DeviceIdentifier::getUniqueHostname("ESP32-Stepper");
    WiFi.setHostname(uniqueHostname.c_str());

    // First scan available networks
    //printf("Scanning WiFi networks...");
    int n = WiFi.scanNetworks();
    //printf("Found %d networks\n", n);
    for(int i=0; i<n; i++){
        //printf("%d: %s (%ddBm) %s\n", 
            // i+1, 
            // WiFi.SSID(i).c_str(), 
            // WiFi.RSSI(i),
            // (WiFi.encryptionType(i) == WIFI_AUTH_OPEN)?"open":"encrypted");
    }

    // Try to connect
    //printf("\nConnecting to %s\n", _ssid);
    WiFi.begin(_ssid, _password);

    unsigned long startTime = millis();
    while (WiFi.status() != WL_CONNECTED && millis()-startTime < 20000) {
        delay(500);
        Serial.print(".");
    }

    if(WiFi.status() == WL_CONNECTED){
        //printf("\nwifi Connected!");
        //printf("SSID: %s\n", WiFi.SSID().c_str());
        //printf("Channel: %d\n", WiFi.channel());
        //printf("IP address: %s\n", WiFi.localIP().toString().c_str());
    } else {
        //printf("\nFailed to connect!");
    }

    while(1){
        if(WiFi.status() != WL_CONNECTED){
            //printf("WiFi disconnected，Reconnecting...");
            WiFi.disconnect();
            WiFi.begin(_ssid, _password);
            vTaskDelay(10000); // Wait 10s before retry
        }
        vTaskDelay(1000);
    }
}

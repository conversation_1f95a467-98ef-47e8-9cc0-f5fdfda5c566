# ESP32步进电机控制系统函数文档

## 项目概述

这是一个基于ESP32-S3的步进电机控制系统，主要用于天文望远镜的赤道仪控制。系统集成了多种传感器和通信接口，支持精确的位置控制和速度控制。

## 主要功能模块

### 1. 电机控制模块 (motor_control.h/cpp)
- **MotorControl类**: 提供步进电机的速度控制和位置控制
- **支持模式**: 速度模式和位置模式
- **PID控制**: 集成PID控制器实现精确控制
- **硬件接口**: 使用GPIO控制步进电机驱动器

### 2. PID控制器模块 (pid_controller.h/cpp)
- **PIDController类**: 实现标准PID控制算法
- **抗积分饱和**: 防止积分项过大导致系统不稳定
- **输出限制**: 限制控制输出在合理范围内
- **参数可调**: 支持运行时调整PID参数

### 3. 编码器模块 (encoder.h/cpp)
- **Encoder类**: 基于TLE5012B磁性角度传感器的编码器系统
- **高精度**: 结合PCNT计数器实现高精度位置检测
- **自动校准**: 支持自动校准功能
- **数据持久化**: 校准数据可保存到Flash

### 4. 串口命令模块 (serial_command.h/cpp)
- **SerialCommand类**: 处理各种串口控制命令
- **Skywatcher协议**: 支持Skywatcher赤道仪协议
- **PID参数设置**: 支持通过串口调整PID参数
- **状态查询**: 提供系统状态查询功能

### 5. BLE通信模块 (ble_manager.h/cpp)
- **BLEManager类**: 蓝牙低功耗通信管理
- **单例模式**: 确保全局唯一的BLE管理器实例
- **双向通信**: 支持接收控制命令和发送状态数据
- **连接管理**: 自动处理连接和断开事件

### 6. WiFi管理模块 (wifi_manager.h/cpp)
- **WiFiManager类**: WiFi连接管理
- **自动连接**: 自动连接到指定的WiFi网络
- **状态监控**: 监控WiFi连接状态

### 7. OLED显示模块 (oled.h/cpp)
- **显示驱动**: 基于SSD1306控制器的128x64 OLED显示
- **图形功能**: 支持点、线、矩形、圆形等图形绘制
- **文字显示**: 支持英文和中文字符显示
- **多模式界面**: 支持多种工作模式的界面显示

### 8. 加速度计模块 (qma6100p.h/cpp)
- **QMA6100P驱动**: 三轴加速度计驱动程序
- **I2C通信**: 基于I2C接口的数据读取
- **多量程**: 支持±2G到±32G多种量程设置
- **定时读取**: 支持定时器定期读取数据

### 9. 按键处理模块 (key.h/cpp)
- **按键扫描**: 独立任务处理按键输入
- **模式切换**: 支持通过按键切换工作模式
- **防抖处理**: 内置按键防抖功能

### 10. 参数管理模块 (preferences_manager.h/cpp)
- **PreferencesManager类**: 基于NVS的参数持久化存储
- **配置保存**: 系统配置参数的保存和读取
- **类型支持**: 支持多种数据类型的存储

## 主要函数说明

### main.cpp中的核心函数

#### setup()
系统初始化函数，执行以下操作：
- 串口命令系统初始化
- 电机控制系统初始化
- 传感器初始化
- 创建FreeRTOS任务

#### loop()
主循环函数，负责：
- BLE任务管理
- 电机模式控制
- 串口通信

#### oledTask()
OLED显示任务，功能包括：
- SPI和OLED初始化
- 实时显示系统状态
- 模式切换界面更新

#### commandTask()
命令处理任务：
- 处理串口命令
- 更新电机控制状态

#### uart1_receive()
UART1数据接收处理：
- 解析控制命令
- 设置电机参数
- 错误处理

## 系统架构

```
ESP32-S3主控
├── 电机控制 (GPIO控制步进电机驱动器)
├── 编码器 (TLE5012B + PCNT)
├── 显示屏 (SSD1306 OLED)
├── 加速度计 (QMA6100P)
├── 按键输入
├── 串口通信 (UART0/UART1/UART2)
├── BLE通信
└── WiFi通信
```

## 通信协议

### UART命令格式
- **速度控制**: `Tup:速度值` / `Tdown:速度值`
- **位置控制**: `up:步数 speed:速度` / `down:步数 speed:速度`
- **停止命令**: `stop_ud` / `stop_Ble`

### Skywatcher协议
支持标准的Skywatcher赤道仪协议，包括：
- 版本查询
- 位置读取
- 速度设置
- 状态查询

## 开发环境

- **平台**: PlatformIO
- **框架**: Arduino for ESP32
- **芯片**: ESP32-S3
- **编译器**: GCC

## 注意事项

1. **内存管理**: 系统使用FreeRTOS任务，注意堆栈大小设置
2. **中断处理**: 编码器和按键使用中断，注意中断优先级
3. **SPI冲突**: OLED和TLE5012B共用SPI，注意时序协调
4. **电源管理**: 注意电机驱动器的电源需求
5. **校准精度**: 编码器校准影响系统精度，需仔细执行

## 版本信息

- **版本**: v1.0
- **日期**: 2024年
- **作者**: ESP32步进电机控制系统开发团队

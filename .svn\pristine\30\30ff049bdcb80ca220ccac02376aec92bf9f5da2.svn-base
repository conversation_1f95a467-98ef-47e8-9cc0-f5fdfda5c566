#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeMonoBold9pt7bBitmaps[] PROGMEM = {
    0xFF, 0xFF, 0xD2, 0x1F, 0x80, 0xEC, 0x89, 0x12, 0x24, 0x40, 0x36, 0x36,
    0x36, 0x7F, 0x7F, 0x36, 0xFF, 0xFF, 0x3C, 0x3C, 0x3C, 0x00, 0x18, 0xFF,
    0xFE, 0x3C, 0x1F, 0x1F, 0x83, 0x46, 0x8D, 0xF0, 0xC1, 0x83, 0x00, 0x61,
    0x22, 0x44, 0x86, 0x67, 0x37, 0x11, 0x22, 0x4C, 0x70, 0x3C, 0x7E, 0x60,
    0x60, 0x30, 0x7B, 0xDF, 0xCE, 0xFF, 0x7F, 0xC9, 0x24, 0x37, 0x66, 0xCC,
    0xCC, 0xCC, 0x66, 0x31, 0xCE, 0x66, 0x33, 0x33, 0x33, 0x66, 0xC8, 0x18,
    0x18, 0xFF, 0xFF, 0x3C, 0x3C, 0x66, 0x18, 0x18, 0x18, 0xFF, 0xFF, 0x18,
    0x18, 0x18, 0x18, 0x6B, 0x48, 0xFF, 0xFF, 0xC0, 0xF0, 0x02, 0x0C, 0x18,
    0x60, 0xC3, 0x06, 0x0C, 0x30, 0x61, 0x83, 0x0C, 0x18, 0x20, 0x00, 0x38,
    0xFB, 0xBE, 0x3C, 0x78, 0xF1, 0xE3, 0xC7, 0xDD, 0xF1, 0xC0, 0x38, 0xF3,
    0x60, 0xC1, 0x83, 0x06, 0x0C, 0x18, 0xFD, 0xF8, 0x3C, 0xFE, 0xC7, 0x03,
    0x03, 0x06, 0x0C, 0x18, 0x70, 0xE3, 0xFF, 0xFF, 0x7C, 0xFE, 0x03, 0x03,
    0x03, 0x1E, 0x1E, 0x07, 0x03, 0x03, 0xFE, 0x7C, 0x1C, 0x38, 0xB1, 0x64,
    0xD9, 0xBF, 0xFF, 0x3E, 0x7C, 0x7E, 0x3F, 0x18, 0x0F, 0xC7, 0xF3, 0x1C,
    0x06, 0x03, 0xC3, 0xFF, 0x9F, 0x80, 0x0F, 0x3F, 0x30, 0x60, 0x60, 0xDC,
    0xFE, 0xE3, 0xC3, 0x63, 0x7E, 0x3C, 0xFF, 0xFF, 0xC3, 0x03, 0x06, 0x06,
    0x06, 0x0C, 0x0C, 0x0C, 0x18, 0x38, 0xFB, 0x1E, 0x3C, 0x6F, 0x9F, 0x63,
    0xC7, 0x8F, 0xF1, 0xC0, 0x3C, 0x7E, 0xE6, 0xC3, 0xC3, 0xE7, 0x7F, 0x3B,
    0x06, 0x0E, 0xFC, 0xF0, 0xF0, 0x0F, 0x6C, 0x00, 0x1A, 0xD2, 0x00, 0x01,
    0x83, 0x87, 0x0E, 0x0F, 0x80, 0xE0, 0x1C, 0x03, 0xFF, 0xFF, 0xC0, 0x00,
    0x0F, 0xFF, 0xFC, 0xC0, 0x78, 0x0F, 0x01, 0xE0, 0xF9, 0xE3, 0xC1, 0x80,
    0x7C, 0xFE, 0xC7, 0x03, 0x0E, 0x1C, 0x00, 0x00, 0x00, 0x30, 0x30, 0x1E,
    0x1F, 0x1C, 0xDC, 0x6C, 0x76, 0x7B, 0x6D, 0xB6, 0xDB, 0x6F, 0xF3, 0xFC,
    0x06, 0x33, 0xF8, 0x78, 0x3C, 0x07, 0xC0, 0x38, 0x05, 0x81, 0xB0, 0x36,
    0x0F, 0xE1, 0xFC, 0x71, 0xDF, 0x7F, 0xEF, 0x80, 0xFF, 0x3F, 0xE6, 0x19,
    0x86, 0x7F, 0x1F, 0xE6, 0x1D, 0x83, 0x60, 0xFF, 0xFF, 0xF0, 0x1F, 0xBF,
    0xD8, 0xF8, 0x3C, 0x06, 0x03, 0x01, 0x80, 0x61, 0xBF, 0xC7, 0xC0, 0xFE,
    0x3F, 0xE6, 0x19, 0x83, 0x60, 0xD8, 0x36, 0x0D, 0x83, 0x61, 0xBF, 0xEF,
    0xE0, 0xFF, 0xFF, 0xD8, 0x6D, 0xB7, 0xC3, 0xE1, 0xB0, 0xC3, 0x61, 0xFF,
    0xFF, 0xE0, 0xFF, 0xFF, 0xD8, 0x6D, 0xB7, 0xC3, 0xE1, 0xB0, 0xC0, 0x60,
    0x7C, 0x3E, 0x00, 0x1F, 0x9F, 0xE6, 0x1B, 0x06, 0xC0, 0x30, 0x0C, 0x7F,
    0x1F, 0xE1, 0x9F, 0xE3, 0xF0, 0xF7, 0xFB, 0xD8, 0xCC, 0x66, 0x33, 0xF9,
    0xFC, 0xC6, 0x63, 0x7B, 0xFD, 0xE0, 0xFF, 0xF3, 0x0C, 0x30, 0xC3, 0x0C,
    0x33, 0xFF, 0xC0, 0x1F, 0xC7, 0xF0, 0x30, 0x0C, 0x03, 0x00, 0xCC, 0x33,
    0x0C, 0xC7, 0x3F, 0x87, 0xC0, 0xF7, 0xBD, 0xE6, 0x61, 0xB0, 0x78, 0x1F,
    0x06, 0xE1, 0x98, 0x63, 0x3C, 0xFF, 0x3C, 0xFC, 0x7E, 0x0C, 0x06, 0x03,
    0x01, 0x80, 0xC6, 0x63, 0x31, 0xFF, 0xFF, 0xE0, 0xE0, 0xFE, 0x3D, 0xC7,
    0x3D, 0xE7, 0xBC, 0xD7, 0x9B, 0xB3, 0x76, 0x60, 0xDE, 0x3F, 0xC7, 0x80,
    0xE1, 0xFE, 0x3D, 0xE3, 0x3C, 0x66, 0xCC, 0xDD, 0x99, 0xB3, 0x1E, 0x63,
    0xDE, 0x3B, 0xC3, 0x00, 0x1F, 0x07, 0xF1, 0xC7, 0x70, 0x7C, 0x07, 0x80,
    0xF0, 0x1F, 0x07, 0x71, 0xC7, 0xF0, 0x7C, 0x00, 0xFE, 0x7F, 0x98, 0x6C,
    0x36, 0x1B, 0xF9, 0xF8, 0xC0, 0x60, 0x7C, 0x3E, 0x00, 0x1F, 0x07, 0xF1,
    0xC7, 0x70, 0x7C, 0x07, 0x80, 0xF0, 0x1F, 0x07, 0x71, 0xC7, 0xF0, 0x7C,
    0x0C, 0x33, 0xFE, 0x7F, 0x80, 0xFC, 0x7F, 0x18, 0xCC, 0x66, 0x73, 0xF1,
    0xF0, 0xCC, 0x63, 0x7D, 0xFE, 0x60, 0x3F, 0xBF, 0xF0, 0x78, 0x0F, 0x03,
    0xF8, 0x3F, 0x83, 0xC3, 0xFF, 0xBF, 0x80, 0xFF, 0xFF, 0xF6, 0x7B, 0x3D,
    0x98, 0xC0, 0x60, 0x30, 0x18, 0x3F, 0x1F, 0x80, 0xF1, 0xFE, 0x3D, 0x83,
    0x30, 0x66, 0x0C, 0xC1, 0x98, 0x33, 0x06, 0x60, 0xC7, 0xF0, 0x7C, 0x00,
    0xFB, 0xFF, 0x7D, 0xC3, 0x18, 0xC3, 0x18, 0x36, 0x06, 0xC0, 0x50, 0x0E,
    0x01, 0xC0, 0x10, 0x00, 0xFB, 0xFE, 0xF6, 0x0D, 0x93, 0x6E, 0xDB, 0xB7,
    0xAD, 0xEE, 0x7B, 0x8E, 0xE3, 0x18, 0xF3, 0xFC, 0xF7, 0x38, 0xFC, 0x1E,
    0x03, 0x01, 0xE0, 0xCC, 0x73, 0xBC, 0xFF, 0x3C, 0xF3, 0xFC, 0xF7, 0x38,
    0xCC, 0x1E, 0x07, 0x80, 0xC0, 0x30, 0x0C, 0x0F, 0xC3, 0xF0, 0xFE, 0xFE,
    0xC6, 0xCC, 0x18, 0x18, 0x30, 0x63, 0xC3, 0xFF, 0xFF, 0xFF, 0xCC, 0xCC,
    0xCC, 0xCC, 0xCC, 0xFF, 0x01, 0x03, 0x06, 0x06, 0x0C, 0x0C, 0x18, 0x18,
    0x30, 0x30, 0x60, 0x60, 0xC0, 0x80, 0xFF, 0x33, 0x33, 0x33, 0x33, 0x33,
    0xFF, 0x10, 0x71, 0xE3, 0x6C, 0x70, 0x40, 0xFF, 0xFF, 0xFC, 0x88, 0x80,
    0x7E, 0x3F, 0x8F, 0xCF, 0xEE, 0x36, 0x1B, 0xFE, 0xFF, 0xE0, 0x38, 0x06,
    0x01, 0xBC, 0x7F, 0x9C, 0x76, 0x0D, 0x83, 0x71, 0xFF, 0xEE, 0xF0, 0x3F,
    0xBF, 0xF8, 0x78, 0x3C, 0x07, 0x05, 0xFE, 0x7E, 0x03, 0x80, 0xE0, 0x18,
    0xF6, 0x7F, 0xB8, 0xEC, 0x1B, 0x06, 0xE3, 0x9F, 0xF3, 0xFC, 0x3E, 0x3F,
    0xB0, 0xFF, 0xFF, 0xFE, 0x01, 0xFE, 0x7E, 0x1F, 0x3F, 0x30, 0x7E, 0x7E,
    0x30, 0x30, 0x30, 0x30, 0xFE, 0xFE, 0x3F, 0xBF, 0xF9, 0xD8, 0x6C, 0x37,
    0x39, 0xFC, 0x76, 0x03, 0x01, 0x8F, 0xC7, 0xC0, 0xE0, 0x70, 0x18, 0x0D,
    0xC7, 0xF3, 0x99, 0x8C, 0xC6, 0x63, 0x7B, 0xFD, 0xE0, 0x18, 0x18, 0x00,
    0x78, 0x78, 0x18, 0x18, 0x18, 0x18, 0xFF, 0xFF, 0x18, 0x60, 0x3F, 0xFC,
    0x30, 0xC3, 0x0C, 0x30, 0xC3, 0x0F, 0xFF, 0x80, 0xE0, 0x70, 0x18, 0x0D,
    0xE6, 0xF3, 0xE1, 0xE0, 0xF8, 0x6E, 0x73, 0xF9, 0xE0, 0x78, 0x78, 0x18,
    0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0xFF, 0xFF, 0xFD, 0x9F, 0xF9, 0x9B,
    0x33, 0x66, 0x6C, 0xCD, 0xBD, 0xFF, 0xBF, 0xEE, 0x7F, 0x98, 0xCC, 0x66,
    0x33, 0x1B, 0xDF, 0xEF, 0x3E, 0x3F, 0xB8, 0xF8, 0x3C, 0x1F, 0x1D, 0xFC,
    0x7C, 0xEF, 0x1F, 0xF9, 0xC3, 0xB0, 0x36, 0x06, 0xE1, 0xDF, 0xF3, 0x78,
    0x60, 0x0C, 0x03, 0xE0, 0x7C, 0x00, 0x1E, 0xEF, 0xFF, 0x87, 0x60, 0x6C,
    0x0D, 0xC3, 0x9F, 0xF0, 0xF6, 0x00, 0xC0, 0x18, 0x0F, 0x81, 0xF0, 0x77,
    0xBF, 0xCF, 0x06, 0x03, 0x01, 0x83, 0xF9, 0xFC, 0x3F, 0xFF, 0xC3, 0xFC,
    0x3F, 0xC3, 0xFF, 0xFC, 0x60, 0x60, 0x60, 0xFE, 0xFE, 0x60, 0x60, 0x60,
    0x61, 0x7F, 0x3E, 0xE7, 0x73, 0x98, 0xCC, 0x66, 0x33, 0x19, 0xFE, 0x7F,
    0xFB, 0xFF, 0x7C, 0xC6, 0x18, 0xC1, 0xB0, 0x36, 0x03, 0x80, 0x70, 0xF1,
    0xFE, 0x3D, 0xBB, 0x37, 0x63, 0xF8, 0x77, 0x0E, 0xE1, 0x8C, 0xF7, 0xFB,
    0xCD, 0x83, 0x83, 0xC3, 0xBB, 0xDF, 0xEF, 0xF3, 0xFC, 0xF6, 0x18, 0xCC,
    0x33, 0x07, 0x81, 0xE0, 0x30, 0x0C, 0x06, 0x0F, 0xC3, 0xF0, 0xFF, 0xFF,
    0x30, 0xC3, 0x0C, 0x7F, 0xFF, 0x37, 0x66, 0x66, 0xCC, 0x66, 0x66, 0x73,
    0xFF, 0xFF, 0xFF, 0xF0, 0xCE, 0x66, 0x66, 0x33, 0x66, 0x66, 0xEC, 0x70,
    0x7C, 0xF3, 0xC0, 0xC0};

const GFXglyph FreeMonoBold9pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 11, 0, 1},       // 0x20 ' '
    {0, 3, 11, 11, 4, -10},    // 0x21 '!'
    {5, 7, 5, 11, 2, -10},     // 0x22 '"'
    {10, 8, 12, 11, 1, -10},   // 0x23 '#'
    {22, 7, 14, 11, 2, -11},   // 0x24 '$'
    {35, 7, 11, 11, 2, -10},   // 0x25 '%'
    {45, 8, 10, 11, 1, -9},    // 0x26 '&'
    {55, 3, 5, 11, 4, -10},    // 0x27 '''
    {57, 4, 14, 11, 5, -10},   // 0x28 '('
    {64, 4, 14, 11, 2, -10},   // 0x29 ')'
    {71, 8, 7, 11, 2, -10},    // 0x2A '*'
    {78, 8, 9, 11, 2, -8},     // 0x2B '+'
    {87, 3, 5, 11, 3, -1},     // 0x2C ','
    {89, 9, 2, 11, 1, -5},     // 0x2D '-'
    {92, 2, 2, 11, 4, -1},     // 0x2E '.'
    {93, 7, 15, 11, 2, -12},   // 0x2F '/'
    {107, 7, 12, 11, 2, -11},  // 0x30 '0'
    {118, 7, 11, 11, 2, -10},  // 0x31 '1'
    {128, 8, 12, 11, 1, -11},  // 0x32 '2'
    {140, 8, 12, 11, 2, -11},  // 0x33 '3'
    {152, 7, 10, 11, 2, -9},   // 0x34 '4'
    {161, 9, 11, 11, 1, -10},  // 0x35 '5'
    {174, 8, 12, 11, 2, -11},  // 0x36 '6'
    {186, 8, 11, 11, 1, -10},  // 0x37 '7'
    {197, 7, 12, 11, 2, -11},  // 0x38 '8'
    {208, 8, 12, 11, 2, -11},  // 0x39 '9'
    {220, 2, 8, 11, 4, -7},    // 0x3A ':'
    {222, 3, 11, 11, 3, -7},   // 0x3B ';'
    {227, 9, 8, 11, 1, -8},    // 0x3C '<'
    {236, 9, 6, 11, 1, -7},    // 0x3D '='
    {243, 9, 8, 11, 1, -8},    // 0x3E '>'
    {252, 8, 11, 11, 2, -10},  // 0x3F '?'
    {263, 9, 15, 11, 1, -11},  // 0x40 '@'
    {280, 11, 11, 11, 0, -10}, // 0x41 'A'
    {296, 10, 11, 11, 1, -10}, // 0x42 'B'
    {310, 9, 11, 11, 1, -10},  // 0x43 'C'
    {323, 10, 11, 11, 0, -10}, // 0x44 'D'
    {337, 9, 11, 11, 1, -10},  // 0x45 'E'
    {350, 9, 11, 11, 1, -10},  // 0x46 'F'
    {363, 10, 11, 11, 1, -10}, // 0x47 'G'
    {377, 9, 11, 11, 1, -10},  // 0x48 'H'
    {390, 6, 11, 11, 3, -10},  // 0x49 'I'
    {399, 10, 11, 11, 1, -10}, // 0x4A 'J'
    {413, 10, 11, 11, 1, -10}, // 0x4B 'K'
    {427, 9, 11, 11, 1, -10},  // 0x4C 'L'
    {440, 11, 11, 11, 0, -10}, // 0x4D 'M'
    {456, 11, 11, 11, 0, -10}, // 0x4E 'N'
    {472, 11, 11, 11, 0, -10}, // 0x4F 'O'
    {488, 9, 11, 11, 1, -10},  // 0x50 'P'
    {501, 11, 14, 11, 0, -10}, // 0x51 'Q'
    {521, 9, 11, 11, 1, -10},  // 0x52 'R'
    {534, 9, 11, 11, 1, -10},  // 0x53 'S'
    {547, 9, 11, 11, 1, -10},  // 0x54 'T'
    {560, 11, 11, 11, 0, -10}, // 0x55 'U'
    {576, 11, 11, 11, 0, -10}, // 0x56 'V'
    {592, 10, 11, 11, 0, -10}, // 0x57 'W'
    {606, 10, 11, 11, 0, -10}, // 0x58 'X'
    {620, 10, 11, 11, 0, -10}, // 0x59 'Y'
    {634, 8, 11, 11, 2, -10},  // 0x5A 'Z'
    {645, 4, 14, 11, 5, -10},  // 0x5B '['
    {652, 7, 15, 11, 2, -12},  // 0x5C '\'
    {666, 4, 14, 11, 2, -10},  // 0x5D ']'
    {673, 7, 6, 11, 2, -11},   // 0x5E '^'
    {679, 11, 2, 11, 0, 3},    // 0x5F '_'
    {682, 3, 3, 11, 3, -11},   // 0x60 '`'
    {684, 9, 8, 11, 1, -7},    // 0x61 'a'
    {693, 10, 11, 11, 0, -10}, // 0x62 'b'
    {707, 9, 8, 11, 1, -7},    // 0x63 'c'
    {716, 10, 11, 11, 1, -10}, // 0x64 'd'
    {730, 9, 8, 11, 1, -7},    // 0x65 'e'
    {739, 8, 11, 11, 2, -10},  // 0x66 'f'
    {750, 9, 12, 11, 1, -7},   // 0x67 'g'
    {764, 9, 11, 11, 1, -10},  // 0x68 'h'
    {777, 8, 11, 11, 2, -10},  // 0x69 'i'
    {788, 6, 15, 11, 2, -10},  // 0x6A 'j'
    {800, 9, 11, 11, 1, -10},  // 0x6B 'k'
    {813, 8, 11, 11, 2, -10},  // 0x6C 'l'
    {824, 11, 8, 11, 0, -7},   // 0x6D 'm'
    {835, 9, 8, 11, 1, -7},    // 0x6E 'n'
    {844, 9, 8, 11, 1, -7},    // 0x6F 'o'
    {853, 11, 12, 11, 0, -7},  // 0x70 'p'
    {870, 11, 12, 11, 0, -7},  // 0x71 'q'
    {887, 9, 8, 11, 1, -7},    // 0x72 'r'
    {896, 8, 8, 11, 2, -7},    // 0x73 's'
    {904, 8, 11, 11, 1, -10},  // 0x74 't'
    {915, 9, 8, 11, 1, -7},    // 0x75 'u'
    {924, 11, 8, 11, 0, -7},   // 0x76 'v'
    {935, 11, 8, 11, 0, -7},   // 0x77 'w'
    {946, 9, 8, 11, 1, -7},    // 0x78 'x'
    {955, 10, 12, 11, 0, -7},  // 0x79 'y'
    {970, 7, 8, 11, 2, -7},    // 0x7A 'z'
    {977, 4, 14, 11, 3, -10},  // 0x7B '{'
    {984, 2, 14, 11, 5, -10},  // 0x7C '|'
    {988, 4, 14, 11, 4, -10},  // 0x7D '}'
    {995, 9, 4, 11, 1, -6}};   // 0x7E '~'

const GFXfont FreeMonoBold9pt7b PROGMEM = {(uint8_t *)FreeMonoBold9pt7bBitmaps,
                                           (GFXglyph *)FreeMonoBold9pt7bGlyphs,
                                           0x20, 0x7E, 18};

// Approx. 1672 bytes

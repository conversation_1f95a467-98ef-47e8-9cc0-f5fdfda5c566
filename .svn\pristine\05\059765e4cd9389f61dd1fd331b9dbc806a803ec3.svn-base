#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSansBoldOblique12pt7bBitmaps[] PROGMEM = {
    0x1C, 0x3C, 0x78, 0xE1, 0xC3, 0x8F, 0x1C, 0x38, 0x70, 0xC1, 0x83, 0x00,
    0x1C, 0x78, 0xF0, 0x71, 0xFC, 0xFE, 0x3B, 0x8E, 0xC3, 0x30, 0xC0, 0x01,
    0x8C, 0x07, 0x38, 0x0C, 0x61, 0xFF, 0xF3, 0xFF, 0xE7, 0xFF, 0x83, 0x9C,
    0x0E, 0x70, 0x1C, 0xE1, 0xFF, 0xF3, 0xFF, 0xC7, 0xFF, 0x83, 0x18, 0x0E,
    0x70, 0x18, 0xC0, 0x73, 0x80, 0x00, 0x40, 0x07, 0xF0, 0x3F, 0xF0, 0xFF,
    0xF3, 0xC9, 0xE7, 0xB3, 0xCF, 0x60, 0x1F, 0xC0, 0x3F, 0xC0, 0x3F, 0xE0,
    0x1F, 0xE0, 0x1B, 0xE0, 0x33, 0xDE, 0x47, 0xBC, 0x8F, 0x7F, 0x7C, 0x7F,
    0xF0, 0x7F, 0x80, 0x18, 0x00, 0x20, 0x00, 0xC0, 0x00, 0x00, 0x01, 0x87,
    0x80, 0xC3, 0xF0, 0x61, 0xFE, 0x10, 0xE1, 0x8C, 0x30, 0x66, 0x0C, 0x3B,
    0x03, 0xFC, 0x80, 0x7E, 0x60, 0x0F, 0x30, 0x00, 0x18, 0x70, 0x0C, 0x7E,
    0x03, 0x1F, 0xC1, 0x8E, 0x30, 0xC3, 0x1C, 0x60, 0xFE, 0x18, 0x1F, 0x8C,
    0x07, 0x80, 0x01, 0xE0, 0x07, 0xF0, 0x1F, 0xE0, 0x79, 0xC0, 0xF3, 0x81,
    0xEE, 0x01, 0xF8, 0x01, 0xE0, 0x1F, 0xC6, 0x7B, 0xDD, 0xE3, 0xF7, 0x87,
    0xEF, 0x07, 0x9F, 0x1F, 0x3F, 0xFF, 0x3F, 0xDE, 0x3F, 0x1C, 0x7F, 0xEE,
    0xCC, 0x03, 0x83, 0x81, 0x81, 0xC1, 0xC0, 0xE0, 0xE0, 0x70, 0x70, 0x38,
    0x3C, 0x1C, 0x0E, 0x07, 0x03, 0x81, 0xC0, 0xE0, 0x70, 0x18, 0x0E, 0x07,
    0x01, 0x80, 0x06, 0x03, 0x81, 0xC0, 0x60, 0x38, 0x1C, 0x0E, 0x07, 0x03,
    0x81, 0xC0, 0xE0, 0xE0, 0x70, 0x38, 0x38, 0x1C, 0x1C, 0x0E, 0x0E, 0x06,
    0x07, 0x07, 0x00, 0x0C, 0x0C, 0x4F, 0xFF, 0x1C, 0x3C, 0x6C, 0x44, 0x03,
    0x80, 0x38, 0x07, 0x00, 0x70, 0x7F, 0xFF, 0xFF, 0xFF, 0xF0, 0xE0, 0x0E,
    0x00, 0xE0, 0x0C, 0x00, 0x7B, 0xDC, 0x23, 0x33, 0x00, 0x7F, 0xFF, 0xF0,
    0x7F, 0xE0, 0x00, 0xC0, 0x30, 0x18, 0x04, 0x03, 0x00, 0x80, 0x60, 0x10,
    0x0C, 0x02, 0x01, 0x80, 0x40, 0x30, 0x08, 0x06, 0x01, 0x00, 0xC0, 0x00,
    0x03, 0xC0, 0x7F, 0x87, 0xFC, 0x78, 0xF3, 0xC7, 0xBC, 0x3D, 0xE1, 0xEF,
    0x0F, 0xF0, 0x7F, 0x87, 0xBC, 0x3D, 0xE1, 0xEF, 0x1E, 0x78, 0xF3, 0xFF,
    0x0F, 0xF0, 0x3E, 0x00, 0x03, 0x83, 0x83, 0xCF, 0xEF, 0xF0, 0x78, 0x38,
    0x1C, 0x0E, 0x0F, 0x07, 0x03, 0x81, 0xC1, 0xE0, 0xF0, 0x70, 0x38, 0x00,
    0x03, 0xF0, 0x0F, 0xF8, 0x7F, 0xF8, 0xF1, 0xF3, 0xC1, 0xE7, 0x83, 0xC0,
    0x07, 0x80, 0x1E, 0x00, 0x78, 0x03, 0xE0, 0x0F, 0x00, 0x7C, 0x01, 0xE0,
    0x07, 0x00, 0x1F, 0xFC, 0x3F, 0xF8, 0xFF, 0xF0, 0x07, 0xE0, 0xFF, 0x8F,
    0xFE, 0xF8, 0xF7, 0x87, 0x80, 0x78, 0x0F, 0x80, 0xFC, 0x07, 0xE0, 0x0F,
    0x80, 0x3C, 0x01, 0xEF, 0x0F, 0x78, 0xF3, 0xFF, 0x8F, 0xF8, 0x3F, 0x00,
    0x00, 0x78, 0x07, 0xC0, 0x7E, 0x03, 0xF0, 0x37, 0x03, 0x38, 0x31, 0xC3,
    0x9E, 0x38, 0xF1, 0x87, 0x1F, 0xFE, 0xFF, 0xF7, 0xFF, 0x80, 0xF0, 0x07,
    0x00, 0x38, 0x03, 0xC0, 0x07, 0xFC, 0x1F, 0xF0, 0xFF, 0xC3, 0x00, 0x1C,
    0x00, 0x7F, 0x81, 0xFF, 0x0F, 0xFE, 0x38, 0xF8, 0x01, 0xE0, 0x07, 0x80,
    0x1E, 0xF0, 0xF3, 0xC7, 0xCF, 0xFE, 0x1F, 0xF0, 0x3F, 0x00, 0x03, 0xE0,
    0x7F, 0x87, 0xFE, 0x78, 0xF3, 0xC0, 0x3D, 0xE1, 0xFF, 0x8F, 0xFE, 0xF8,
    0xF7, 0xC7, 0xBC, 0x3D, 0xE1, 0xEF, 0x1E, 0x7C, 0xF3, 0xFF, 0x0F, 0xF0,
    0x1F, 0x00, 0x7F, 0xFB, 0xFF, 0xDF, 0xFE, 0x00, 0xE0, 0x0E, 0x00, 0xE0,
    0x0E, 0x00, 0xE0, 0x0F, 0x00, 0x70, 0x07, 0x00, 0x78, 0x03, 0x80, 0x3C,
    0x01, 0xC0, 0x0E, 0x00, 0xF0, 0x00, 0x03, 0xF0, 0x1F, 0xE0, 0xFF, 0xC7,
    0x8F, 0x1C, 0x3C, 0x71, 0xE0, 0xFF, 0x03, 0xF8, 0x3F, 0xF1, 0xF1, 0xE7,
    0x87, 0xBC, 0x1E, 0xF0, 0x7B, 0xE3, 0xCF, 0xFF, 0x1F, 0xF8, 0x1F, 0x80,
    0x03, 0xE0, 0x3F, 0xE1, 0xFF, 0x8F, 0x9F, 0x3C, 0x3D, 0xE0, 0xF7, 0x83,
    0xDE, 0x1F, 0x78, 0xFD, 0xFF, 0xE3, 0xFF, 0x87, 0xDE, 0x00, 0xF3, 0xC7,
    0x8F, 0xFE, 0x1F, 0xF0, 0x3F, 0x00, 0x1C, 0xF3, 0x80, 0x00, 0x00, 0x00,
    0x01, 0xCF, 0x38, 0x0E, 0x3C, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF1,
    0xE3, 0x81, 0x06, 0x18, 0x60, 0x00, 0x00, 0x01, 0xC0, 0x7E, 0x1F, 0xE7,
    0xF8, 0x7E, 0x03, 0xE0, 0x1F, 0xE0, 0x3F, 0xC0, 0x7F, 0x00, 0x78, 0x00,
    0xC0, 0x3F, 0xFC, 0xFF, 0xF3, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x00, 0x07,
    0xFF, 0x9F, 0xFC, 0x7F, 0xF0, 0x30, 0x01, 0xE0, 0x0F, 0xE0, 0x3F, 0xC0,
    0x7F, 0x80, 0x7C, 0x07, 0xE1, 0xFE, 0x7F, 0x87, 0xE0, 0x38, 0x00, 0x00,
    0x00, 0x0F, 0xC1, 0xFF, 0x8F, 0xFC, 0xF1, 0xFF, 0x07, 0xF0, 0x3C, 0x01,
    0xE0, 0x1E, 0x01, 0xE0, 0x3E, 0x03, 0xE0, 0x1C, 0x01, 0xC0, 0x0E, 0x00,
    0x00, 0x07, 0x80, 0x3C, 0x01, 0xC0, 0x00, 0x00, 0x3F, 0x80, 0x03, 0xFF,
    0x80, 0x3C, 0x0F, 0x01, 0xC0, 0x0E, 0x0E, 0x00, 0x1C, 0x70, 0xF7, 0x73,
    0x87, 0xF8, 0xCC, 0x31, 0xE3, 0x61, 0x87, 0x0D, 0x8C, 0x1C, 0x3C, 0x30,
    0x61, 0xB1, 0x81, 0x86, 0xC6, 0x0C, 0x3B, 0x18, 0x71, 0xCC, 0x63, 0xCE,
    0x31, 0xFB, 0xF0, 0xE3, 0xCF, 0x01, 0xC0, 0x00, 0x03, 0xC0, 0xC0, 0x07,
    0xFF, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x3E, 0x00, 0x3F, 0x00, 0x1F, 0x80,
    0x1F, 0xC0, 0x0F, 0xE0, 0x0F, 0xF0, 0x07, 0x7C, 0x07, 0x1E, 0x03, 0x8F,
    0x03, 0x87, 0x83, 0xC3, 0xC1, 0xFF, 0xE1, 0xFF, 0xF0, 0xFF, 0xFC, 0xF0,
    0x1E, 0x70, 0x0F, 0x78, 0x07, 0xB8, 0x03, 0xC0, 0x0F, 0xFE, 0x0F, 0xFF,
    0x87, 0xFF, 0xE3, 0xC0, 0xF1, 0xC0, 0x78, 0xE0, 0x3C, 0xF0, 0x3C, 0x7F,
    0xFC, 0x3F, 0xFC, 0x1F, 0xFF, 0x0E, 0x07, 0xCF, 0x01, 0xE7, 0x80, 0xF3,
    0x80, 0x79, 0xC0, 0x79, 0xFF, 0xF8, 0xFF, 0xFC, 0x7F, 0xF8, 0x00, 0x01,
    0xF8, 0x03, 0xFF, 0x03, 0xFF, 0xC3, 0xE1, 0xF3, 0xC0, 0x79, 0xE0, 0x3D,
    0xE0, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x78, 0x00, 0x3C, 0x00, 0x1E, 0x00,
    0x0F, 0x00, 0xE7, 0x80, 0xF3, 0xE0, 0xF0, 0xFF, 0xF8, 0x3F, 0xF0, 0x07,
    0xE0, 0x00, 0x1F, 0xFC, 0x0F, 0xFF, 0x87, 0xFF, 0xC3, 0x81, 0xF1, 0xC0,
    0x79, 0xE0, 0x3C, 0xF0, 0x1E, 0x78, 0x0F, 0x38, 0x07, 0x9C, 0x03, 0xDE,
    0x03, 0xCF, 0x01, 0xE7, 0x81, 0xF3, 0x80, 0xF1, 0xC1, 0xF1, 0xFF, 0xF0,
    0xFF, 0xF0, 0x7F, 0xE0, 0x00, 0x0F, 0xFF, 0x1F, 0xFF, 0x1F, 0xFF, 0x1C,
    0x00, 0x1C, 0x00, 0x3C, 0x00, 0x3C, 0x00, 0x3F, 0xFC, 0x3F, 0xFC, 0x3F,
    0xFC, 0x78, 0x00, 0x78, 0x00, 0x78, 0x00, 0x70, 0x00, 0x70, 0x00, 0xFF,
    0xF8, 0xFF, 0xF8, 0xFF, 0xF8, 0x1F, 0xFF, 0x1F, 0xFE, 0x1F, 0xFE, 0x1C,
    0x00, 0x1C, 0x00, 0x3C, 0x00, 0x3C, 0x00, 0x3F, 0xF8, 0x3F, 0xF8, 0x3F,
    0xF8, 0x78, 0x00, 0x78, 0x00, 0x78, 0x00, 0x70, 0x00, 0xF0, 0x00, 0xF0,
    0x00, 0xF0, 0x00, 0xE0, 0x00, 0x01, 0xFC, 0x03, 0xFF, 0x03, 0xFF, 0xC3,
    0xE0, 0xF3, 0xC0, 0x39, 0xC0, 0x01, 0xE0, 0x00, 0xF0, 0x00, 0xF0, 0x7F,
    0x78, 0x3F, 0xBC, 0x1F, 0xDE, 0x01, 0xCF, 0x00, 0xE7, 0xC0, 0xF1, 0xF0,
    0xF8, 0xFF, 0xFC, 0x3F, 0xEC, 0x07, 0xE6, 0x00, 0x1E, 0x03, 0x8F, 0x01,
    0xC7, 0x01, 0xE3, 0x80, 0xF3, 0xC0, 0x79, 0xE0, 0x38, 0xF0, 0x1C, 0x7F,
    0xFE, 0x3F, 0xFF, 0x3F, 0xFF, 0x9E, 0x03, 0x8F, 0x01, 0xC7, 0x01, 0xE3,
    0x80, 0xF3, 0xC0, 0x71, 0xE0, 0x38, 0xF0, 0x3C, 0x70, 0x1E, 0x00, 0x1E,
    0x3C, 0x78, 0xE1, 0xC7, 0x8F, 0x1E, 0x38, 0x71, 0xE3, 0xC7, 0x8E, 0x1C,
    0x78, 0xF1, 0xE0, 0x00, 0x1C, 0x00, 0xF0, 0x03, 0xC0, 0x0F, 0x00, 0x38,
    0x00, 0xE0, 0x07, 0x80, 0x1E, 0x00, 0x78, 0x01, 0xC0, 0x07, 0x3C, 0x3C,
    0xF0, 0xF3, 0xC3, 0x8F, 0x1E, 0x3F, 0xF8, 0x7F, 0xC0, 0xFC, 0x00, 0x1E,
    0x07, 0xC7, 0x83, 0xE1, 0xE1, 0xE0, 0x70, 0xF0, 0x1C, 0x78, 0x0F, 0x3C,
    0x03, 0xDE, 0x00, 0xFF, 0x00, 0x3F, 0xC0, 0x0F, 0xF0, 0x07, 0xDE, 0x01,
    0xE7, 0xC0, 0x78, 0xF0, 0x1C, 0x3E, 0x0F, 0x07, 0x83, 0xC0, 0xF0, 0xF0,
    0x3C, 0x38, 0x07, 0x80, 0x0E, 0x00, 0xF0, 0x07, 0x80, 0x3C, 0x01, 0xC0,
    0x0E, 0x00, 0xF0, 0x07, 0x80, 0x38, 0x01, 0xC0, 0x1E, 0x00, 0xF0, 0x07,
    0x80, 0x38, 0x01, 0xC0, 0x1F, 0xFE, 0xFF, 0xF7, 0xFF, 0x80, 0x1F, 0x03,
    0xF1, 0xF0, 0x3F, 0x1F, 0x07, 0xF1, 0xF0, 0x7F, 0x3F, 0x0F, 0xE3, 0xF0,
    0xEE, 0x3B, 0x1E, 0xE3, 0xB1, 0xDE, 0x3B, 0x1D, 0xE7, 0xB3, 0x9C, 0x7B,
    0x39, 0xC7, 0x37, 0x9C, 0x73, 0x73, 0xCF, 0x3F, 0x3C, 0xF3, 0xE3, 0x8F,
    0x3E, 0x38, 0xE3, 0xC3, 0x8E, 0x3C, 0x78, 0x1E, 0x03, 0x87, 0xC0, 0xE1,
    0xF0, 0x38, 0x7C, 0x1E, 0x1F, 0x87, 0x8F, 0xE1, 0xC3, 0xB8, 0x70, 0xEF,
    0x1C, 0x39, 0xCF, 0x1E, 0x73, 0xC7, 0x8E, 0xE1, 0xC3, 0xB8, 0x70, 0xEE,
    0x1C, 0x1F, 0x8F, 0x07, 0xE3, 0xC1, 0xF0, 0xE0, 0x3C, 0x38, 0x0F, 0x00,
    0x01, 0xF8, 0x03, 0xFF, 0x03, 0xFF, 0xC3, 0xE3, 0xE3, 0xC0, 0xF9, 0xE0,
    0x3D, 0xE0, 0x1E, 0xF0, 0x0F, 0xF0, 0x07, 0xF8, 0x03, 0xFC, 0x03, 0xDE,
    0x01, 0xEF, 0x00, 0xF7, 0xC0, 0xF1, 0xF0, 0xF0, 0xFF, 0xF0, 0x3F, 0xF0,
    0x07, 0xE0, 0x00, 0x1F, 0xFC, 0x1F, 0xFE, 0x1F, 0xFF, 0x1C, 0x1F, 0x1C,
    0x0F, 0x3C, 0x0F, 0x3C, 0x0F, 0x3C, 0x1E, 0x3F, 0xFC, 0x3F, 0xFC, 0x7F,
    0xF0, 0x78, 0x00, 0x78, 0x00, 0x70, 0x00, 0x70, 0x00, 0xF0, 0x00, 0xF0,
    0x00, 0xF0, 0x00, 0x01, 0xF8, 0x03, 0xFF, 0x03, 0xFF, 0xC3, 0xE3, 0xE3,
    0xC0, 0xF9, 0xC0, 0x3D, 0xE0, 0x1E, 0xF0, 0x0F, 0xF0, 0x07, 0xF8, 0x03,
    0xFC, 0x03, 0xDE, 0x09, 0xEF, 0x0E, 0xE7, 0xC7, 0xF1, 0xF1, 0xF0, 0xFF,
    0xF8, 0x3F, 0xFE, 0x07, 0xE6, 0x00, 0x02, 0x00, 0x0F, 0xFE, 0x0F, 0xFF,
    0x87, 0xFF, 0xE3, 0x81, 0xF1, 0xC0, 0x78, 0xE0, 0x3C, 0xF0, 0x1C, 0x78,
    0x1E, 0x3F, 0xFC, 0x1F, 0xFC, 0x1F, 0xFF, 0x8F, 0x03, 0xC7, 0x81, 0xE3,
    0x80, 0xF1, 0xC0, 0xF1, 0xE0, 0x78, 0xF0, 0x3C, 0x78, 0x1F, 0x00, 0x03,
    0xF8, 0x0F, 0xFE, 0x1F, 0xFF, 0x1E, 0x1F, 0x3C, 0x0F, 0x3C, 0x0F, 0x3C,
    0x00, 0x3F, 0x00, 0x1F, 0xF0, 0x0F, 0xFC, 0x01, 0xFE, 0x00, 0x3E, 0xF0,
    0x1E, 0xF0, 0x1E, 0xF8, 0x3C, 0x7F, 0xF8, 0x7F, 0xF0, 0x1F, 0xC0, 0x7F,
    0xFE, 0xFF, 0xFD, 0xFF, 0xF8, 0x1C, 0x00, 0x78, 0x00, 0xF0, 0x01, 0xE0,
    0x03, 0x80, 0x07, 0x00, 0x1E, 0x00, 0x3C, 0x00, 0x78, 0x00, 0xE0, 0x01,
    0xC0, 0x07, 0x80, 0x0F, 0x00, 0x1E, 0x00, 0x38, 0x00, 0x1E, 0x07, 0x1C,
    0x0F, 0x3C, 0x0F, 0x3C, 0x0F, 0x3C, 0x0E, 0x38, 0x0E, 0x78, 0x1E, 0x78,
    0x1E, 0x78, 0x1E, 0x78, 0x1C, 0x70, 0x1C, 0xF0, 0x3C, 0xF0, 0x3C, 0xF0,
    0x38, 0xF8, 0x78, 0xFF, 0xF0, 0x7F, 0xE0, 0x1F, 0x80, 0xF0, 0x1F, 0xE0,
    0x39, 0xC0, 0xF3, 0x81, 0xC7, 0x07, 0x8E, 0x0E, 0x1C, 0x3C, 0x3C, 0x70,
    0x79, 0xE0, 0xF3, 0x80, 0xEF, 0x01, 0xDC, 0x03, 0xB8, 0x07, 0xE0, 0x0F,
    0x80, 0x1F, 0x00, 0x3C, 0x00, 0x78, 0x00, 0xF0, 0x70, 0x7F, 0x87, 0x83,
    0xFC, 0x3C, 0x3D, 0xE1, 0xE1, 0xEF, 0x1F, 0x0E, 0x78, 0xD8, 0xF3, 0xC6,
    0xC7, 0x0E, 0x76, 0x78, 0x73, 0x33, 0x83, 0xB9, 0x9C, 0x1D, 0xCD, 0xC0,
    0xEC, 0x6E, 0x07, 0xE3, 0xE0, 0x3E, 0x1F, 0x01, 0xF0, 0xF0, 0x0F, 0x87,
    0x80, 0x78, 0x38, 0x03, 0xC1, 0xC0, 0x00, 0x0F, 0x03, 0xC3, 0xC1, 0xE0,
    0xF8, 0xF0, 0x1E, 0x78, 0x07, 0x9E, 0x00, 0xFF, 0x00, 0x3F, 0x80, 0x0F,
    0xC0, 0x01, 0xE0, 0x00, 0xF8, 0x00, 0x3F, 0x00, 0x1F, 0xC0, 0x0F, 0xF0,
    0x07, 0x9E, 0x03, 0xC7, 0x80, 0xF0, 0xF0, 0x78, 0x3C, 0x3C, 0x0F, 0x80,
    0x78, 0x1E, 0xF0, 0x79, 0xE0, 0xF3, 0xC3, 0xC3, 0xCF, 0x07, 0x9E, 0x0F,
    0x78, 0x0F, 0xE0, 0x1F, 0x80, 0x3F, 0x00, 0x3C, 0x00, 0x70, 0x00, 0xE0,
    0x03, 0xC0, 0x07, 0x80, 0x0F, 0x00, 0x1C, 0x00, 0x38, 0x00, 0x1F, 0xFF,
    0x0F, 0xFF, 0x87, 0xFF, 0xC0, 0x03, 0xC0, 0x03, 0xE0, 0x03, 0xE0, 0x03,
    0xE0, 0x03, 0xE0, 0x01, 0xE0, 0x01, 0xE0, 0x01, 0xE0, 0x01, 0xE0, 0x01,
    0xE0, 0x01, 0xE0, 0x01, 0xE0, 0x01, 0xFF, 0xF0, 0xFF, 0xF8, 0x7F, 0xFC,
    0x00, 0x0F, 0xC3, 0xF0, 0xFC, 0x38, 0x1E, 0x07, 0x01, 0xC0, 0x70, 0x1C,
    0x0F, 0x03, 0x80, 0xE0, 0x38, 0x0E, 0x07, 0x01, 0xC0, 0x70, 0x1C, 0x0F,
    0x03, 0x80, 0xFC, 0x3F, 0x0F, 0xC0, 0x08, 0x88, 0xC4, 0x44, 0x66, 0x66,
    0x66, 0x62, 0x22, 0x33, 0x33, 0x30, 0x0F, 0xC3, 0xF0, 0xFC, 0x07, 0x03,
    0xC0, 0xE0, 0x38, 0x0E, 0x03, 0x81, 0xC0, 0x70, 0x1C, 0x07, 0x03, 0xC0,
    0xE0, 0x38, 0x0E, 0x03, 0x81, 0xE0, 0x70, 0xFC, 0x3F, 0x0F, 0xC0, 0x03,
    0x80, 0xF0, 0x1E, 0x07, 0xE1, 0xDC, 0x3B, 0x8E, 0x71, 0x86, 0x70, 0xFC,
    0x1F, 0x83, 0x80, 0x7F, 0xFE, 0xFF, 0xFC, 0xE6, 0x30, 0x07, 0xE0, 0xFF,
    0x8F, 0xFE, 0x70, 0xE0, 0x07, 0x03, 0xF8, 0xFF, 0xCF, 0x9E, 0xF0, 0xF7,
    0x8F, 0x3F, 0xF8, 0xFF, 0xC3, 0xDF, 0x00, 0x0E, 0x00, 0x1C, 0x00, 0x38,
    0x00, 0xF0, 0x01, 0xE0, 0x03, 0x9F, 0x07, 0xFF, 0x0F, 0xFF, 0x3E, 0x3E,
    0x78, 0x3C, 0xF0, 0x79, 0xC0, 0xF3, 0x81, 0xEF, 0x07, 0x9F, 0x1F, 0x3F,
    0xFC, 0x7F, 0xF0, 0xEF, 0x80, 0x07, 0xC0, 0xFF, 0x8F, 0xFE, 0xF8, 0xF7,
    0x87, 0xB8, 0x03, 0xC0, 0x1E, 0x00, 0xF0, 0xF7, 0x8F, 0x1F, 0xF8, 0xFF,
    0x81, 0xF0, 0x00, 0x00, 0x1E, 0x00, 0x38, 0x00, 0x70, 0x00, 0xE0, 0x03,
    0xC0, 0xF7, 0x87, 0xFE, 0x1F, 0xFC, 0x7C, 0x78, 0xF0, 0x73, 0xC0, 0xE7,
    0x81, 0x8F, 0x07, 0x1E, 0x0E, 0x3E, 0x3C, 0x7F, 0xF8, 0x7F, 0xE0, 0x7D,
    0xC0, 0x07, 0xC0, 0xFF, 0x8F, 0xFE, 0xF0, 0xF7, 0x87, 0xFF, 0xFF, 0xFF,
    0xFE, 0x00, 0xF0, 0x07, 0xC7, 0x9F, 0xF8, 0xFF, 0x81, 0xF0, 0x00, 0x07,
    0x87, 0xC7, 0xE3, 0xC1, 0xC3, 0xF9, 0xFC, 0x78, 0x3C, 0x1C, 0x0E, 0x07,
    0x07, 0x83, 0x81, 0xC0, 0xE0, 0xF0, 0x78, 0x00, 0x03, 0xDE, 0x1F, 0xF8,
    0x7F, 0xF1, 0xF1, 0xE3, 0xC1, 0xCF, 0x03, 0x9E, 0x06, 0x3C, 0x0C, 0x78,
    0x38, 0xF8, 0xF1, 0xFF, 0xC1, 0xFF, 0x81, 0xF7, 0x00, 0x0E, 0x3C, 0x3C,
    0x78, 0xF0, 0x7F, 0xC0, 0x7E, 0x00, 0x1E, 0x00, 0x70, 0x01, 0xC0, 0x07,
    0x00, 0x3C, 0x00, 0xF7, 0xC3, 0xBF, 0x8F, 0xFF, 0x3C, 0x3D, 0xE0, 0xE7,
    0x83, 0x9C, 0x0E, 0x70, 0x79, 0xC1, 0xEF, 0x07, 0x3C, 0x1C, 0xE0, 0x73,
    0x83, 0xC0, 0x0E, 0x3C, 0x70, 0x00, 0x03, 0x8F, 0x1E, 0x38, 0x71, 0xE3,
    0xC7, 0x0E, 0x1C, 0x78, 0xF1, 0xC0, 0x03, 0xC0, 0xE0, 0x38, 0x00, 0x00,
    0x01, 0xE0, 0x70, 0x1C, 0x07, 0x03, 0xC0, 0xF0, 0x38, 0x0E, 0x03, 0x81,
    0xE0, 0x70, 0x1C, 0x07, 0x03, 0xC0, 0xF0, 0xF8, 0x3E, 0x0F, 0x00, 0x0E,
    0x00, 0x1C, 0x00, 0x38, 0x00, 0xF0, 0x01, 0xE0, 0x03, 0x87, 0x87, 0x1E,
    0x0E, 0x78, 0x3D, 0xE0, 0x7F, 0x80, 0xFE, 0x01, 0xFE, 0x03, 0xFC, 0x0F,
    0x38, 0x1E, 0x78, 0x38, 0xF0, 0x70, 0xF0, 0xE1, 0xE0, 0x0E, 0x3C, 0x78,
    0xE1, 0xC3, 0x8F, 0x1E, 0x38, 0x71, 0xE3, 0xC7, 0x0E, 0x1C, 0x78, 0xF1,
    0xC0, 0x1C, 0xF1, 0xE0, 0xEF, 0xDF, 0x87, 0xFF, 0xFE, 0x7C, 0x78, 0xF3,
    0xC3, 0x87, 0x9C, 0x1C, 0x38, 0xE1, 0xE1, 0xC7, 0x0E, 0x0E, 0x78, 0x70,
    0xF3, 0xC3, 0x87, 0x9C, 0x3C, 0x38, 0xE1, 0xE1, 0xC7, 0x0E, 0x0E, 0x00,
    0x3D, 0xF0, 0xEF, 0xE3, 0xFF, 0xCF, 0x0F, 0x78, 0x39, 0xC0, 0xE7, 0x03,
    0x9C, 0x1E, 0xF0, 0x7B, 0xC1, 0xCE, 0x07, 0x38, 0x1C, 0xE0, 0xF0, 0x07,
    0xE0, 0x7F, 0xE3, 0xFF, 0x9F, 0x1F, 0x78, 0x3F, 0xC0, 0xFF, 0x03, 0xFC,
    0x1F, 0xF0, 0x7B, 0xE3, 0xE7, 0xFF, 0x1F, 0xF8, 0x1F, 0x80, 0x0E, 0x7C,
    0x0F, 0xFE, 0x0F, 0xFF, 0x1F, 0x1F, 0x1E, 0x0F, 0x1E, 0x0F, 0x1C, 0x0F,
    0x1C, 0x0F, 0x3C, 0x1E, 0x3E, 0x3E, 0x3F, 0xFC, 0x3F, 0xF8, 0x7B, 0xE0,
    0x78, 0x00, 0x70, 0x00, 0x70, 0x00, 0x70, 0x00, 0xF0, 0x00, 0x07, 0xBC,
    0x7F, 0xF3, 0xFF, 0x9F, 0x1E, 0x78, 0x3B, 0xC0, 0xEF, 0x03, 0x3C, 0x0C,
    0xF0, 0x73, 0xE3, 0xCF, 0xFF, 0x1F, 0xF8, 0x3C, 0xE0, 0x03, 0x80, 0x1E,
    0x00, 0x78, 0x01, 0xC0, 0x07, 0x00, 0x3D, 0xCE, 0xE3, 0xF8, 0xF0, 0x78,
    0x1E, 0x07, 0x01, 0xC0, 0xF0, 0x3C, 0x0E, 0x03, 0x80, 0xE0, 0x00, 0x1F,
    0xC3, 0xFE, 0x7F, 0xFF, 0x0F, 0xF0, 0x0F, 0xE0, 0x7F, 0xC1, 0xFE, 0x03,
    0xEE, 0x1E, 0xFF, 0xC7, 0xFC, 0x3F, 0x00, 0x1E, 0x1E, 0x1C, 0x7F, 0xFF,
    0x3C, 0x38, 0x38, 0x38, 0x78, 0x78, 0x70, 0x7C, 0xF8, 0x78, 0x38, 0x3C,
    0xE0, 0xE3, 0x83, 0x9E, 0x0E, 0x70, 0x79, 0xC1, 0xE7, 0x07, 0x3C, 0x1C,
    0xF0, 0xF3, 0xE7, 0xCF, 0xFF, 0x1F, 0xF8, 0x3C, 0xE0, 0xF0, 0x77, 0x87,
    0xBC, 0x38, 0xE3, 0xC7, 0x1C, 0x39, 0xE1, 0xCE, 0x0E, 0xE0, 0x77, 0x03,
    0xF0, 0x0F, 0x80, 0x78, 0x03, 0xC0, 0x00, 0xF1, 0xC3, 0xF8, 0xE3, 0xFC,
    0xF1, 0xDE, 0x79, 0xEF, 0x3C, 0xE7, 0xB6, 0x73, 0xDB, 0x70, 0xED, 0xB8,
    0x7C, 0xF8, 0x3E, 0x7C, 0x1F, 0x3C, 0x0F, 0x1E, 0x07, 0x8E, 0x00, 0x0F,
    0x1E, 0x0F, 0x3C, 0x0F, 0x38, 0x07, 0x70, 0x07, 0xF0, 0x03, 0xE0, 0x03,
    0xC0, 0x07, 0xC0, 0x0F, 0xE0, 0x1E, 0xE0, 0x3C, 0xF0, 0x3C, 0xF0, 0x78,
    0x78, 0x3C, 0x1C, 0x78, 0x78, 0xF0, 0xE1, 0xE3, 0xC1, 0xC7, 0x03, 0x9E,
    0x07, 0x38, 0x0E, 0xE0, 0x1D, 0xC0, 0x3F, 0x00, 0x7E, 0x00, 0x78, 0x00,
    0xF0, 0x01, 0xC0, 0x07, 0x00, 0x7E, 0x00, 0xF8, 0x01, 0xE0, 0x00, 0x1F,
    0xF9, 0xFF, 0xCF, 0xFC, 0x01, 0xE0, 0x3E, 0x03, 0xC0, 0x3C, 0x03, 0xC0,
    0x3C, 0x03, 0xC0, 0x3F, 0xF9, 0xFF, 0xCF, 0xFC, 0x00, 0x07, 0x87, 0xC3,
    0xE3, 0xC1, 0xC0, 0xE0, 0x70, 0x38, 0x3C, 0x1C, 0x0E, 0x1E, 0x0F, 0x03,
    0x81, 0xC0, 0xE0, 0x70, 0x78, 0x38, 0x1C, 0x0F, 0x87, 0xC1, 0xC0, 0x0C,
    0x30, 0x86, 0x18, 0x61, 0x8C, 0x30, 0xC3, 0x0C, 0x61, 0x86, 0x18, 0x63,
    0x0C, 0x30, 0xC2, 0x00, 0x00, 0x07, 0x07, 0xC3, 0xE0, 0x70, 0x38, 0x3C,
    0x1C, 0x0E, 0x07, 0x03, 0x81, 0xE0, 0xF0, 0xE0, 0x70, 0x78, 0x38, 0x1C,
    0x0E, 0x07, 0x07, 0x8F, 0x87, 0xC3, 0xC0, 0x3C, 0x07, 0xE0, 0xC7, 0x30,
    0x7E, 0x01, 0xC0};

const GFXglyph FreeSansBoldOblique12pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 7, 0, 1},         // 0x20 ' '
    {0, 7, 17, 8, 3, -16},      // 0x21 '!'
    {15, 10, 6, 11, 4, -17},    // 0x22 '"'
    {23, 15, 16, 13, 1, -15},   // 0x23 '#'
    {53, 15, 21, 13, 1, -17},   // 0x24 '$'
    {93, 18, 18, 21, 3, -17},   // 0x25 '%'
    {134, 15, 17, 17, 2, -16},  // 0x26 '&'
    {166, 4, 6, 6, 4, -17},     // 0x27 '''
    {169, 9, 22, 8, 2, -17},    // 0x28 '('
    {194, 9, 22, 8, -1, -16},   // 0x29 ')'
    {219, 8, 8, 9, 3, -17},     // 0x2A '*'
    {227, 12, 11, 14, 2, -10},  // 0x2B '+'
    {244, 5, 7, 7, 1, -2},      // 0x2C ','
    {249, 7, 3, 8, 2, -7},      // 0x2D '-'
    {252, 4, 3, 7, 2, -2},      // 0x2E '.'
    {254, 10, 17, 7, 0, -16},   // 0x2F '/'
    {276, 13, 17, 13, 2, -16},  // 0x30 '0'
    {304, 9, 17, 13, 4, -16},   // 0x31 '1'
    {324, 15, 17, 13, 1, -16},  // 0x32 '2'
    {356, 13, 17, 13, 2, -16},  // 0x33 '3'
    {384, 13, 17, 13, 1, -16},  // 0x34 '4'
    {412, 14, 17, 13, 1, -16},  // 0x35 '5'
    {442, 13, 17, 13, 2, -16},  // 0x36 '6'
    {470, 13, 17, 13, 3, -16},  // 0x37 '7'
    {498, 14, 17, 13, 1, -16},  // 0x38 '8'
    {528, 14, 17, 13, 2, -16},  // 0x39 '9'
    {558, 6, 12, 8, 3, -11},    // 0x3A ':'
    {567, 7, 16, 8, 2, -11},    // 0x3B ';'
    {581, 13, 12, 14, 2, -11},  // 0x3C '<'
    {601, 14, 9, 14, 1, -9},    // 0x3D '='
    {617, 13, 12, 14, 1, -10},  // 0x3E '>'
    {637, 13, 18, 15, 4, -17},  // 0x3F '?'
    {667, 22, 21, 23, 2, -17},  // 0x40 '@'
    {725, 17, 18, 17, 0, -17},  // 0x41 'A'
    {764, 17, 18, 17, 2, -17},  // 0x42 'B'
    {803, 17, 18, 17, 3, -17},  // 0x43 'C'
    {842, 17, 18, 17, 2, -17},  // 0x44 'D'
    {881, 16, 18, 16, 2, -17},  // 0x45 'E'
    {917, 16, 18, 15, 2, -17},  // 0x46 'F'
    {953, 17, 18, 19, 3, -17},  // 0x47 'G'
    {992, 17, 18, 17, 2, -17},  // 0x48 'H'
    {1031, 7, 18, 7, 2, -17},   // 0x49 'I'
    {1047, 14, 18, 13, 1, -17}, // 0x4A 'J'
    {1079, 18, 18, 17, 2, -17}, // 0x4B 'K'
    {1120, 13, 18, 15, 2, -17}, // 0x4C 'L'
    {1150, 20, 18, 20, 2, -17}, // 0x4D 'M'
    {1195, 18, 18, 17, 2, -17}, // 0x4E 'N'
    {1236, 17, 18, 19, 3, -17}, // 0x4F 'O'
    {1275, 16, 18, 16, 2, -17}, // 0x50 'P'
    {1311, 17, 19, 19, 3, -17}, // 0x51 'Q'
    {1352, 17, 18, 17, 2, -17}, // 0x52 'R'
    {1391, 16, 18, 16, 2, -17}, // 0x53 'S'
    {1427, 15, 18, 15, 3, -17}, // 0x54 'T'
    {1461, 16, 18, 17, 3, -17}, // 0x55 'U'
    {1497, 15, 18, 16, 4, -17}, // 0x56 'V'
    {1531, 21, 18, 23, 4, -17}, // 0x57 'W'
    {1579, 18, 18, 16, 1, -17}, // 0x58 'X'
    {1620, 15, 18, 16, 4, -17}, // 0x59 'Y'
    {1654, 17, 18, 15, 1, -17}, // 0x5A 'Z'
    {1693, 10, 23, 8, 1, -17},  // 0x5B '['
    {1722, 4, 23, 7, 3, -22},   // 0x5C '\'
    {1734, 10, 23, 8, 0, -17},  // 0x5D ']'
    {1763, 11, 11, 14, 3, -16}, // 0x5E '^'
    {1779, 15, 2, 13, -2, 4},   // 0x5F '_'
    {1783, 4, 3, 8, 4, -17},    // 0x60 '`'
    {1785, 13, 13, 13, 1, -12}, // 0x61 'a'
    {1807, 15, 18, 15, 1, -17}, // 0x62 'b'
    {1841, 13, 13, 13, 2, -12}, // 0x63 'c'
    {1863, 15, 18, 15, 2, -17}, // 0x64 'd'
    {1897, 13, 13, 13, 2, -12}, // 0x65 'e'
    {1919, 9, 18, 8, 2, -17},   // 0x66 'f'
    {1940, 15, 18, 15, 1, -12}, // 0x67 'g'
    {1974, 14, 18, 15, 2, -17}, // 0x68 'h'
    {2006, 7, 18, 7, 2, -17},   // 0x69 'i'
    {2022, 10, 23, 7, -1, -17}, // 0x6A 'j'
    {2051, 15, 18, 13, 1, -17}, // 0x6B 'k'
    {2085, 7, 18, 7, 2, -17},   // 0x6C 'l'
    {2101, 21, 13, 21, 1, -12}, // 0x6D 'm'
    {2136, 14, 13, 15, 2, -12}, // 0x6E 'n'
    {2159, 14, 13, 15, 2, -12}, // 0x6F 'o'
    {2182, 16, 18, 15, 0, -12}, // 0x70 'p'
    {2218, 14, 18, 15, 2, -12}, // 0x71 'q'
    {2250, 10, 13, 9, 2, -12},  // 0x72 'r'
    {2267, 12, 13, 13, 3, -12}, // 0x73 's'
    {2287, 8, 15, 8, 2, -14},   // 0x74 't'
    {2302, 14, 13, 15, 2, -12}, // 0x75 'u'
    {2325, 13, 13, 13, 3, -12}, // 0x76 'v'
    {2347, 17, 13, 19, 3, -12}, // 0x77 'w'
    {2375, 16, 13, 13, 0, -12}, // 0x78 'x'
    {2401, 15, 18, 13, 1, -12}, // 0x79 'y'
    {2435, 13, 13, 12, 1, -12}, // 0x7A 'z'
    {2457, 9, 23, 9, 3, -17},   // 0x7B '{'
    {2483, 6, 23, 7, 1, -17},   // 0x7C '|'
    {2501, 9, 23, 9, 0, -17},   // 0x7D '}'
    {2527, 12, 5, 14, 2, -7}};  // 0x7E '~'

const GFXfont FreeSansBoldOblique12pt7b PROGMEM = {
    (uint8_t *)FreeSansBoldOblique12pt7bBitmaps,
    (GFXglyph *)FreeSansBoldOblique12pt7bGlyphs, 0x20, 0x7E, 29};

// Approx. 3207 bytes

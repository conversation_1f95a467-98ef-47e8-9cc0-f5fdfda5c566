#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeMonoOblique9pt7bBitmaps[] PROGMEM = {
    0x11, 0x22, 0x24, 0x40, 0x00, 0xC0, 0xDE, 0xE5, 0x29, 0x00, 0x09, 0x05,
    0x02, 0x82, 0x47, 0xF8, 0xA0, 0x51, 0xFE, 0x28, 0x14, 0x0A, 0x09, 0x00,
    0x08, 0x1D, 0x23, 0x40, 0x70, 0x1C, 0x02, 0x82, 0x84, 0x78, 0x20, 0x20,
    0x1C, 0x11, 0x08, 0x83, 0x80, 0x18, 0x71, 0xC0, 0x1C, 0x11, 0x08, 0x83,
    0x80, 0x1E, 0x60, 0x81, 0x03, 0x0A, 0x65, 0x46, 0x88, 0xE8, 0xFA, 0x80,
    0x12, 0x24, 0x48, 0x88, 0x88, 0x88, 0x80, 0x01, 0x11, 0x11, 0x11, 0x22,
    0x44, 0x80, 0x10, 0x22, 0x5B, 0xC3, 0x0A, 0x22, 0x00, 0x04, 0x02, 0x02,
    0x1F, 0xF0, 0x80, 0x40, 0x20, 0x00, 0x36, 0x4C, 0x80, 0xFF, 0x80, 0xF0,
    0x00, 0x80, 0x80, 0x40, 0x40, 0x40, 0x20, 0x20, 0x20, 0x10, 0x10, 0x10,
    0x08, 0x08, 0x00, 0x1C, 0x45, 0x0A, 0x18, 0x30, 0x61, 0x42, 0x85, 0x11,
    0xC0, 0x04, 0x38, 0x90, 0x20, 0x81, 0x02, 0x04, 0x08, 0x23, 0xF8, 0x07,
    0x04, 0xC4, 0x20, 0x10, 0x10, 0x30, 0x20, 0x20, 0x60, 0x40, 0x3F, 0x80,
    0x0F, 0x00, 0x40, 0x20, 0x20, 0x60, 0x18, 0x04, 0x02, 0x01, 0x43, 0x1E,
    0x00, 0x03, 0x05, 0x0A, 0x12, 0x22, 0x22, 0x42, 0x7F, 0x04, 0x04, 0x1E,
    0x1F, 0x88, 0x08, 0x05, 0xC3, 0x30, 0x08, 0x04, 0x02, 0x02, 0x42, 0x1E,
    0x00, 0x07, 0x18, 0x20, 0x40, 0x5C, 0xA6, 0xC2, 0x82, 0x82, 0xC4, 0x78,
    0xFF, 0x04, 0x10, 0x20, 0x82, 0x04, 0x10, 0x20, 0x81, 0x00, 0x1E, 0x23,
    0x41, 0x41, 0x62, 0x1C, 0x66, 0x82, 0x82, 0x84, 0x78, 0x1E, 0x23, 0x41,
    0x41, 0x43, 0x65, 0x3A, 0x02, 0x04, 0x18, 0xE0, 0x6C, 0x00, 0x36, 0x18,
    0xC0, 0x00, 0x19, 0x8C, 0xC4, 0x00, 0x01, 0x83, 0x06, 0x0C, 0x06, 0x00,
    0x80, 0x30, 0x04, 0xFF, 0x80, 0x00, 0x1F, 0xF0, 0x20, 0x0C, 0x01, 0x00,
    0x60, 0x20, 0x60, 0xC1, 0x80, 0x3D, 0x8E, 0x08, 0x10, 0xC6, 0x08, 0x00,
    0x01, 0x80, 0x1C, 0x45, 0x0A, 0x79, 0x34, 0x69, 0x4E, 0x81, 0x03, 0x03,
    0xC0, 0x0F, 0x00, 0x60, 0x12, 0x02, 0x40, 0x88, 0x21, 0x07, 0xE1, 0x04,
    0x20, 0x5E, 0x3C, 0x3F, 0x84, 0x11, 0x04, 0x82, 0x3F, 0x88, 0x32, 0x04,
    0x81, 0x60, 0xBF, 0xC0, 0x1E, 0x98, 0xD0, 0x28, 0x08, 0x04, 0x02, 0x01,
    0x00, 0x41, 0x1F, 0x00, 0x3F, 0x0C, 0x22, 0x04, 0x81, 0x20, 0x48, 0x12,
    0x09, 0x02, 0x43, 0x3F, 0x00, 0x3F, 0xC4, 0x11, 0x00, 0x88, 0x3E, 0x08,
    0x82, 0x00, 0x82, 0x60, 0xBF, 0xE0, 0x3F, 0xE2, 0x08, 0x40, 0x11, 0x03,
    0xE0, 0x44, 0x08, 0x01, 0x00, 0x60, 0x1F, 0x00, 0x1E, 0x98, 0xD0, 0x08,
    0x08, 0x04, 0x7A, 0x05, 0x02, 0x41, 0x1F, 0x00, 0x3D, 0xE2, 0x18, 0x42,
    0x08, 0x43, 0xF8, 0x41, 0x08, 0x21, 0x08, 0x21, 0x1E, 0xF0, 0x3F, 0x82,
    0x02, 0x01, 0x00, 0x80, 0x40, 0x20, 0x20, 0x10, 0x7F, 0x00, 0x0F, 0xE0,
    0x20, 0x04, 0x00, 0x80, 0x10, 0x02, 0x20, 0x84, 0x10, 0x84, 0x0F, 0x00,
    0x3C, 0xE2, 0x10, 0x44, 0x11, 0x02, 0xC0, 0x64, 0x08, 0x81, 0x08, 0x61,
    0x1E, 0x38, 0x3E, 0x02, 0x00, 0x80, 0x20, 0x10, 0x04, 0x01, 0x04, 0x42,
    0x10, 0xBF, 0xE0, 0x38, 0x38, 0xC3, 0x05, 0x28, 0x29, 0x42, 0x52, 0x13,
    0x10, 0x99, 0x84, 0x08, 0x20, 0x47, 0x8F, 0x00, 0x70, 0xE6, 0x08, 0xA1,
    0x14, 0x22, 0x48, 0x49, 0x11, 0x22, 0x14, 0x43, 0x1E, 0x20, 0x1E, 0x18,
    0x90, 0x28, 0x18, 0x0C, 0x06, 0x05, 0x02, 0x46, 0x1E, 0x00, 0x3F, 0x84,
    0x31, 0x04, 0x81, 0x20, 0x8F, 0xC2, 0x00, 0x80, 0x60, 0x3E, 0x00, 0x1E,
    0x18, 0x90, 0x28, 0x18, 0x0C, 0x06, 0x05, 0x02, 0x46, 0x1E, 0x08, 0x0F,
    0x44, 0x60, 0x3F, 0x84, 0x31, 0x04, 0x81, 0x20, 0x8F, 0xC2, 0x10, 0x84,
    0x60, 0xBC, 0x10, 0x0F, 0x88, 0xC8, 0x24, 0x01, 0x80, 0x38, 0x05, 0x02,
    0xC2, 0x5E, 0x00, 0xFF, 0xC4, 0x44, 0x02, 0x01, 0x00, 0x80, 0x40, 0x60,
    0x20, 0x7E, 0x00, 0xF1, 0xD0, 0x24, 0x09, 0x02, 0x41, 0xA0, 0x48, 0x12,
    0x04, 0xC6, 0x1F, 0x00, 0xF1, 0xE8, 0x11, 0x02, 0x20, 0x82, 0x20, 0x44,
    0x09, 0x01, 0x40, 0x28, 0x02, 0x00, 0xF1, 0xE8, 0x09, 0x12, 0x26, 0x45,
    0x48, 0xAA, 0x29, 0x45, 0x28, 0xC6, 0x18, 0xC0, 0x38, 0xE2, 0x08, 0x26,
    0x05, 0x00, 0x40, 0x18, 0x04, 0x81, 0x08, 0x41, 0x1C, 0x70, 0xE3, 0xA0,
    0x90, 0x84, 0x81, 0x80, 0x80, 0x40, 0x20, 0x20, 0x7E, 0x00, 0x3F, 0x90,
    0x88, 0x80, 0x80, 0x80, 0x80, 0x80, 0x82, 0x82, 0x7F, 0x00, 0x39, 0x08,
    0x44, 0x21, 0x08, 0x42, 0x21, 0x0E, 0x00, 0x88, 0x44, 0x44, 0x22, 0x22,
    0x11, 0x11, 0x38, 0x42, 0x11, 0x08, 0x42, 0x10, 0x84, 0x2E, 0x00, 0x08,
    0x28, 0x92, 0x18, 0x20, 0xFF, 0xC0, 0xA4, 0x3E, 0x00, 0x80, 0x47, 0xA4,
    0x34, 0x12, 0x18, 0xF7, 0x38, 0x01, 0x00, 0x40, 0x09, 0xE1, 0xC6, 0x20,
    0x44, 0x09, 0x01, 0x30, 0x46, 0x13, 0xBC, 0x00, 0x1F, 0x48, 0x74, 0x0A,
    0x00, 0x80, 0x20, 0x0C, 0x18, 0xF8, 0x01, 0x80, 0x40, 0x23, 0x96, 0x32,
    0x0A, 0x05, 0x02, 0x81, 0x61, 0x1F, 0xE0, 0x1F, 0x30, 0xD0, 0x3F, 0xF8,
    0x04, 0x01, 0x00, 0x7C, 0x07, 0xC3, 0x00, 0x80, 0xFE, 0x10, 0x04, 0x01,
    0x00, 0x40, 0x10, 0x08, 0x0F, 0xE0, 0x1D, 0xD8, 0xC4, 0x12, 0x04, 0x82,
    0x20, 0x8C, 0x61, 0xE8, 0x02, 0x01, 0x07, 0x80, 0x30, 0x04, 0x01, 0x00,
    0x5C, 0x38, 0x88, 0x22, 0x08, 0x82, 0x21, 0x18, 0x4F, 0x3C, 0x04, 0x04,
    0x00, 0x38, 0x08, 0x08, 0x08, 0x08, 0x10, 0x10, 0xFF, 0x01, 0x00, 0x80,
    0x03, 0xF0, 0x10, 0x08, 0x04, 0x02, 0x02, 0x01, 0x00, 0x80, 0x40, 0x47,
    0xC0, 0x38, 0x08, 0x04, 0x02, 0x71, 0x20, 0xA0, 0xA0, 0x68, 0x24, 0x11,
    0x38, 0xE0, 0x3C, 0x04, 0x04, 0x08, 0x08, 0x08, 0x08, 0x08, 0x10, 0x10,
    0xFF, 0x3E, 0xE2, 0x64, 0x88, 0x91, 0x12, 0x24, 0x48, 0x91, 0x17, 0x33,
    0x37, 0x14, 0x4C, 0x24, 0x12, 0x09, 0x08, 0x85, 0xE3, 0x1E, 0x10, 0x90,
    0x30, 0x18, 0x0C, 0x0B, 0x08, 0x78, 0x33, 0xC3, 0x8C, 0x40, 0x88, 0x12,
    0x02, 0x60, 0x8C, 0x31, 0x78, 0x20, 0x08, 0x03, 0xE0, 0x00, 0x1C, 0xD8,
    0xC4, 0x12, 0x04, 0x81, 0x20, 0x4C, 0x21, 0xF8, 0x02, 0x00, 0x81, 0xF0,
    0x73, 0x8E, 0x04, 0x04, 0x02, 0x01, 0x00, 0x81, 0xFC, 0x1F, 0x61, 0x40,
    0x3C, 0x03, 0x81, 0x82, 0xFC, 0x10, 0x63, 0xF9, 0x02, 0x04, 0x10, 0x20,
    0x40, 0x7C, 0xE3, 0x10, 0x90, 0x48, 0x24, 0x22, 0x11, 0x18, 0xF6, 0xF3,
    0xD0, 0x44, 0x10, 0x88, 0x24, 0x09, 0x02, 0x80, 0x40, 0xE1, 0xD0, 0x24,
    0x91, 0x24, 0x55, 0x19, 0x86, 0x61, 0x10, 0x39, 0xC4, 0x20, 0xB0, 0x30,
    0x0C, 0x04, 0x86, 0x13, 0x8E, 0x3C, 0x71, 0x04, 0x10, 0x40, 0x88, 0x09,
    0x00, 0xA0, 0x06, 0x00, 0x40, 0x08, 0x01, 0x00, 0xFC, 0x00, 0x7F, 0x42,
    0x04, 0x08, 0x10, 0x20, 0x42, 0xFE, 0x0C, 0x41, 0x04, 0x30, 0x8C, 0x08,
    0x21, 0x04, 0x10, 0x60, 0x24, 0x94, 0x92, 0x52, 0x40, 0x18, 0x20, 0x82,
    0x10, 0x40, 0xC4, 0x10, 0x82, 0x08, 0xC0, 0x61, 0x24, 0x30};

const GFXglyph FreeMonoOblique9pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 11, 0, 1},       // 0x20 ' '
    {0, 4, 11, 11, 4, -10},    // 0x21 '!'
    {6, 5, 5, 11, 4, -10},     // 0x22 '"'
    {10, 9, 12, 11, 2, -10},   // 0x23 '#'
    {24, 8, 12, 11, 3, -10},   // 0x24 '$'
    {36, 9, 11, 11, 2, -10},   // 0x25 '%'
    {49, 7, 10, 11, 2, -9},    // 0x26 '&'
    {58, 2, 5, 11, 6, -10},    // 0x27 '''
    {60, 4, 13, 11, 6, -10},   // 0x28 '('
    {67, 4, 13, 11, 3, -10},   // 0x29 ')'
    {74, 7, 7, 11, 4, -10},    // 0x2A '*'
    {81, 9, 8, 11, 2, -8},     // 0x2B '+'
    {90, 4, 5, 11, 2, -1},     // 0x2C ','
    {93, 9, 1, 11, 2, -5},     // 0x2D '-'
    {95, 2, 2, 11, 4, -1},     // 0x2E '.'
    {96, 9, 13, 11, 2, -11},   // 0x2F '/'
    {111, 7, 11, 11, 3, -10},  // 0x30 '0'
    {121, 7, 11, 11, 2, -10},  // 0x31 '1'
    {131, 9, 11, 11, 2, -10},  // 0x32 '2'
    {144, 9, 11, 11, 2, -10},  // 0x33 '3'
    {157, 8, 11, 11, 2, -10},  // 0x34 '4'
    {168, 9, 11, 11, 2, -10},  // 0x35 '5'
    {181, 8, 11, 11, 3, -10},  // 0x36 '6'
    {192, 7, 11, 11, 4, -10},  // 0x37 '7'
    {202, 8, 11, 11, 3, -10},  // 0x38 '8'
    {213, 8, 11, 11, 3, -10},  // 0x39 '9'
    {224, 3, 8, 11, 4, -7},    // 0x3A ':'
    {227, 5, 11, 11, 2, -7},   // 0x3B ';'
    {234, 9, 8, 11, 2, -8},    // 0x3C '<'
    {243, 9, 4, 11, 2, -6},    // 0x3D '='
    {248, 9, 8, 11, 2, -8},    // 0x3E '>'
    {257, 7, 10, 11, 4, -9},   // 0x3F '?'
    {266, 7, 12, 11, 3, -10},  // 0x40 '@'
    {277, 11, 10, 11, 0, -9},  // 0x41 'A'
    {291, 10, 10, 11, 1, -9},  // 0x42 'B'
    {304, 9, 10, 11, 2, -9},   // 0x43 'C'
    {316, 10, 10, 11, 1, -9},  // 0x44 'D'
    {329, 10, 10, 11, 1, -9},  // 0x45 'E'
    {342, 11, 10, 11, 1, -9},  // 0x46 'F'
    {356, 9, 10, 11, 2, -9},   // 0x47 'G'
    {368, 11, 10, 11, 1, -9},  // 0x48 'H'
    {382, 9, 10, 11, 2, -9},   // 0x49 'I'
    {394, 11, 10, 11, 2, -9},  // 0x4A 'J'
    {408, 11, 10, 11, 1, -9},  // 0x4B 'K'
    {422, 10, 10, 11, 1, -9},  // 0x4C 'L'
    {435, 13, 10, 11, 0, -9},  // 0x4D 'M'
    {452, 11, 10, 11, 1, -9},  // 0x4E 'N'
    {466, 9, 10, 11, 2, -9},   // 0x4F 'O'
    {478, 10, 10, 11, 1, -9},  // 0x50 'P'
    {491, 9, 13, 11, 2, -9},   // 0x51 'Q'
    {506, 10, 10, 11, 1, -9},  // 0x52 'R'
    {519, 9, 10, 11, 2, -9},   // 0x53 'S'
    {531, 9, 10, 11, 3, -9},   // 0x54 'T'
    {543, 10, 10, 11, 2, -9},  // 0x55 'U'
    {556, 11, 10, 11, 2, -9},  // 0x56 'V'
    {570, 11, 10, 11, 2, -9},  // 0x57 'W'
    {584, 11, 10, 11, 1, -9},  // 0x58 'X'
    {598, 9, 10, 11, 3, -9},   // 0x59 'Y'
    {610, 9, 10, 11, 2, -9},   // 0x5A 'Z'
    {622, 5, 13, 11, 5, -10},  // 0x5B '['
    {631, 4, 14, 11, 4, -11},  // 0x5C '\'
    {638, 5, 13, 11, 2, -10},  // 0x5D ']'
    {647, 7, 5, 11, 3, -10},   // 0x5E '^'
    {652, 11, 1, 11, 0, 2},    // 0x5F '_'
    {654, 2, 3, 11, 5, -11},   // 0x60 '`'
    {655, 9, 8, 11, 2, -7},    // 0x61 'a'
    {664, 11, 11, 11, 0, -10}, // 0x62 'b'
    {680, 10, 8, 11, 2, -7},   // 0x63 'c'
    {690, 9, 11, 11, 2, -10},  // 0x64 'd'
    {703, 9, 8, 11, 2, -7},    // 0x65 'e'
    {712, 10, 11, 11, 2, -10}, // 0x66 'f'
    {726, 10, 11, 11, 2, -7},  // 0x67 'g'
    {740, 10, 11, 11, 1, -10}, // 0x68 'h'
    {754, 8, 11, 11, 2, -10},  // 0x69 'i'
    {765, 9, 14, 11, 1, -10},  // 0x6A 'j'
    {781, 9, 11, 11, 1, -10},  // 0x6B 'k'
    {794, 8, 11, 11, 2, -10},  // 0x6C 'l'
    {805, 11, 8, 11, 0, -7},   // 0x6D 'm'
    {816, 9, 8, 11, 1, -7},    // 0x6E 'n'
    {825, 9, 8, 11, 2, -7},    // 0x6F 'o'
    {834, 11, 11, 11, 0, -7},  // 0x70 'p'
    {850, 10, 11, 11, 2, -7},  // 0x71 'q'
    {864, 9, 8, 11, 2, -7},    // 0x72 'r'
    {873, 8, 8, 11, 2, -7},    // 0x73 's'
    {881, 7, 10, 11, 2, -9},   // 0x74 't'
    {890, 9, 8, 11, 2, -7},    // 0x75 'u'
    {899, 10, 8, 11, 2, -7},   // 0x76 'v'
    {909, 10, 8, 11, 2, -7},   // 0x77 'w'
    {919, 10, 8, 11, 1, -7},   // 0x78 'x'
    {929, 12, 11, 11, 0, -7},  // 0x79 'y'
    {946, 8, 8, 11, 2, -7},    // 0x7A 'z'
    {954, 6, 13, 11, 4, -10},  // 0x7B '{'
    {964, 3, 12, 11, 5, -9},   // 0x7C '|'
    {969, 6, 13, 11, 3, -10},  // 0x7D '}'
    {979, 7, 3, 11, 3, -6}};   // 0x7E '~'

const GFXfont FreeMonoOblique9pt7b PROGMEM = {
    (uint8_t *)FreeMonoOblique9pt7bBitmaps,
    (GFXglyph *)FreeMonoOblique9pt7bGlyphs, 0x20, 0x7E, 18};

// Approx. 1654 bytes

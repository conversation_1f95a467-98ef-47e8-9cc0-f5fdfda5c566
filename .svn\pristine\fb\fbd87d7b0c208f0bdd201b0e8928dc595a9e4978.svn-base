#ifndef PID_CONTROLLER_H
#define PID_CONTROLLER_H

/**
 * @brief PID控制器类
 * @details 实现比例-积分-微分(PID)控制算法，用于电机位置和速度的精确控制
 *          包含抗积分饱和和输出限制功能
 */
class PIDController {
public:
    /**
     * @brief PID控制器构造函数
     * @param kp 比例增益系数
     * @param ki 积分增益系数
     * @param kd 微分增益系数
     */
    PIDController(float kp, float ki, float kd);

    /**
     * @brief 计算PID控制输出
     * @param error 误差值（目标值-当前值）
     * @return PID控制器输出值
     * @details 实现PID算法：output = Kp*error + Ki*∫error + Kd*d(error)/dt
     *          包含积分饱和限制和输出限制
     */
    float calculate(float error);

    /**
     * @brief 重置PID控制器
     * @details 清零积分项和上次误差值，用于重新开始控制
     */
    void reset();

    /**
     * @brief 设置比例增益
     * @param kp 新的比例增益值
     */
    void setKp(float kp);

    /**
     * @brief 设置积分增益
     * @param ki 新的积分增益值
     */
    void setKi(float ki);

    /**
     * @brief 设置微分增益
     * @param kd 新的微分增益值
     */
    void setKd(float kd);

    /**
     * @brief 获取目标值
     * @return 当前设定的目标值
     */
    float getTarget() const;

private:
    float kp;           ///< 比例增益系数
    float ki;           ///< 积分增益系数
    float kd;           ///< 微分增益系数
    float integral;     ///< 积分累积值
    float prev_error;   ///< 上次误差值
    float target;       ///< 目标值
};

#endif

# RELEASE NOTES

20 April 2022 - Apache NimBLE v1.5.0

For full release notes, please visit the
[Apache Mynewt Wiki](https://cwiki.apache.org/confluence/display/MYNEWT/Release+Notes).

Apache NimBLE is an open-source Bluetooth 5.3 stack (both Host & Controller) that completely
replaces the proprietary SoftDevice on Nordic chipsets.

New features in this version of NimBLE include:

* Fake dual-mode option for controller
* LLCP tracing via HCI events
* Code size optimization for disabled GAP roles
* Support for PA/LNA
* LE Secure Connections Only mode
* Support for Bluetooth Core Specification 5.3
* Connection subrating
* BabbleSim support
* Various bugfixes and improvements

If working on next-generation RTOS and Bluetooth protocol stack
sounds exciting to you, get in touch, by sending a mail to the Apache Mynewt
Developer's list, <EMAIL>.

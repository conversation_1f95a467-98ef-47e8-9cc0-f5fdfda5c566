/**
 * @file pid_controller.cpp
 * @brief PID控制器实现文件
 */
#include "pid_controller.h"
#include <Arduino.h>

/**
 * @brief PID控制器构造函数实现
 */
PIDController::PIDController(float kp, float ki, float kd)
    : kp(kp), ki(ki), kd(kd), integral(0), prev_error(0), target(0) {
}

/**
 * @brief PID控制算法实现
 * @details 实现标准PID控制算法，包含抗积分饱和和输出限制
 */
float PIDController::calculate(float error) {
    integral += error;

    // 抗积分饱和
    const float MAX_INTEGRAL = 100.0f; // 根据实际需求调整
    // if (integral > MAX_INTEGRAL) {
    //     integral = MAX_INTEGRAL;
    // } else if (integral < -MAX_INTEGRAL) {
    //     integral = -MAX_INTEGRAL;
    // }
    integral = constrain(integral, -MAX_INTEGRAL, MAX_INTEGRAL);

    float derivative = error - prev_error;
    prev_error = error;

    float output = kp * error + ki * integral + kd * derivative;
    output = constrain(output, -40.0f, 40.0f); // 限制输出在±40范围内
    return output;
}

/**
 * @brief 重置PID控制器状态
 */
void PIDController::reset() {
    integral = 0;
    prev_error = 0;
}

void PIDController::setKp(float kp) {
    this->kp = kp;
}

void PIDController::setKi(float ki) {
    this->ki = ki;
}

void PIDController::setKd(float kd) {
    this->kd = kd;
}

float PIDController::getTarget() const {
    return target;
}

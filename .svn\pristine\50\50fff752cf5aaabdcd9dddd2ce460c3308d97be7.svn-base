#include <Arduino.h>
#include "driver/gpio.h"
#include "driver/uart.h"
#include "wifi_manager.h"
#include "ble_manager.h"
#include "motor_control.h"
#include "pid_controller.h"
#include "encoder.h"
#include "spi_config.h"
#include "spi_tle5012b.h"
#include "preferences_manager.h"
#include "serial_command.h"
#include <Wire.h>  // I2C库
#include <oled.h>
#include "key.h"
#include "qma6100p.h"

// #define BLE_DEVICE_NAME "ESP32-Stepper"
const char *WIFI_SSID = "OnePlus 10 Pro";
const char *WIFI_PASSWORD = "123456789";
uint8_t uart_256mocro_data[] = {0x05, 0x00, 0x80, 0x00, 0x00, 0x01, 0xC1, 0x7F};
uint8_t uart_256mocro_data1[] = {0x05, 0x00, 0xec, 0x10, 0x01, 0x00, 0x53, 0x97};
uint8_t uart_256mocro_data2[] = {0x05, 0x01, 0x80, 0x00, 0x00, 0x01, 0xC1, 0x93};
uint8_t uart_256mocro_data3[] = {0x05, 0x01, 0xec, 0x10, 0x01, 0x00, 0x53, 0x7B};
uint8_t uart_256mocro_data4[] = {0x05, 0x02, 0x80, 0x00, 0x00, 0x01, 0xC1, 0x09};
uint8_t uart_256mocro_data5[] = {0x05, 0x02, 0xec, 0x10, 0x01, 0x00, 0x53, 0xE1};
uint8_t uart_256mocro_data6[] = {0x05, 0x03, 0x80, 0x00, 0x00, 0x01, 0xC1, 0xE5};
uint8_t uart_256mocro_data7[] = {0x05, 0x03, 0xec, 0x10, 0x01, 0x00, 0x53, 0x0D};

extern uint16_t tle_angle;

MotorControl motor;
Encoder encoder;
PIDController pid(0.8, 0.005, 0.05);
TaskHandle_t bleTaskHandle = NULL;
SerialCommand command;
extern int mode_show;
extern bool motormode;
extern float motor_speed;
extern int motor_steps;
HardwareSerial MySerial_2(2); // UART2
HardwareSerial MySerial_1(1); // UART1
HardwareSerial MySerial_0(0); // UART0
// 接收缓冲区配置
#define MAX_RX_LEN 64
char rxBuffer[MAX_RX_LEN];
uint8_t bufferIndex = 0;
int stop_Bit = 1; 

// extern mode_slect mode_show;
void send_256sub_command();
void uart0_int();
void uart0_send();
void uart1_receive();
void uart1_int();
void oledTask(void *pvParameters);
void commandTask(void* pvParameters);

static int last_mode = -1;
void oledTask(void *pvParameters) 
{
  vTaskDelay(1000 / portTICK_PERIOD_MS); // 延迟1秒给WiFi/BLE初始化
  spi_master_init(SPI3_HOST,SPI_DMA_CH_AUTO,4092,SPI2_DEF_PIN_NUM_MISO,SPI2_DEF_PIN_NUM_MOSI,SPI2_DEF_PIN_NUM_CLK);
  OLED_Init_GPIO();
  spi_tle5012b_init(SPI3_HOST,1000000,TLE5012B_SOFT_CS0);
  OLED_Init();
  OLED_Clear(0x00);  
    mode_show = 1;
    while(true) {
      if(mode_show != last_mode) { // 仅模式变化时刷新
        last_mode = mode_show;
        switch (mode_show)
        {
          case 1: show_start(); break;
          case 2: GPIO.out_w1tc = (1 << 10); show_stellar_mode(); OLED_ShowNum(60,40,0,10,8,1); break;
          case 3: GPIO.out_w1tc = (1 << 10); show_sun_mode(); OLED_ShowNum(60,40,0,10,8,1); break;  
          case 4: GPIO.out_w1tc = (1 << 10); show_moon_mode(); OLED_ShowNum(60,40,0,10,8,1); break; 
        default:  show_stop_mode(); OLED_ShowNum(60,40,0,10,8,1); motor.setspeedmode(); break;
        }
      }
      if(mode_show == 2 | mode_show == 3 | mode_show == 4 | mode_show == 5) {
        OLED_ShowNum(60,40,motor_speed,10,8,1);
        OLED_Display();
      }
      if (qma6100_x > 0) {OLED_Set_Flip(1);} // 倒过来显示
      else {OLED_Set_Flip(0);} // 正常显示
       tle_angle = tle5012b_read_angle(TLE5012B_SPI, TLE5012B_SOFT_CS0); 
       vTaskDelay(50 / portTICK_PERIOD_MS); // 确保SPI操作完成
       vTaskDelay(1);
    }    
}

void commandTask(void* pvParameters) {
  while (1) {
    command.process(pid, encoder, motor);
    motor.updatePositionControl();
    vTaskDelay(pdMS_TO_TICKS(50)); // 缩短为50ms间隔
  }
}

void uart1_manage(void* pvParameters)
{
    while (1) {
    uart1_receive();
    vTaskDelay(pdMS_TO_TICKS(50)); // 缩短为50ms间隔
  }
}

void startBLETask() {
    if(bleTaskHandle == NULL && (stop_Bit==1)) {
        xTaskCreatePinnedToCore(
            bleTask,
            "BLE_Task",
            10000,
            NULL,
            3,
            &bleTaskHandle,
            0
        );
    }
}

void stopBLETask() {
    if(bleTaskHandle != NULL) {
        vTaskDelete(bleTaskHandle);
        bleTaskHandle = NULL;
    }
}


void setup() {
    command.setup();
    //Serial.begin(115200);
    send_256sub_command();
    uart0_int();
    uart1_int();

    vTaskDelay(1000 / portTICK_PERIOD_MS); // Send every second
    fflush(stdout);
    setvbuf(stdout, NULL, _IONBF, 0);
    motor.init();
    esp_err_t ret = qma6100p_init();
    if (ret != ESP_OK) {
        //Serial.print("QMA6100P initialization failed");
        // while(1);
    }
    
    xTaskCreatePinnedToCore(oledTask, "oled_task", 8192, NULL, 2, NULL, 1);
    xTaskCreatePinnedToCore(key_task, "key_task", 4096, NULL, 1, NULL, 0);
    xTaskCreatePinnedToCore(commandTask, "CommandTask", 4096, NULL, 4, NULL, 1);
    xTaskCreatePinnedToCore(uart1_manage, "uart1_manage", 4096, NULL, 3, NULL, 1);
    startBLETask();

}

void loop() {
    if(stop_Bit==0) {
        stopBLETask();
    }

    if(motormode)
    {
      motor.setpositionmode();
      motor.setPosition(motor_steps);
      motor.updatePositionControl();
    }
    else{
      motor.setspeedmode();
      motor.setOutput(motor_speed);
    }

    // uart1_receive();
    if(stop_Bit==1){uart0_send();}
 
    vTaskDelay(150 / portTICK_PERIOD_MS);
}



 
void send_256sub_command() {
    MySerial_2.begin(115200, SERIAL_8N1, -1, 46);
    delay(500);
    for(int i=0;i<8;i++) {
        if(i==0) MySerial_2.write(uart_256mocro_data,sizeof(uart_256mocro_data));
        else if(i==1) MySerial_2.write(uart_256mocro_data1,sizeof(uart_256mocro_data1));
        else if(i==2) MySerial_2.write(uart_256mocro_data2,sizeof(uart_256mocro_data2));
        else if(i==3) MySerial_2.write(uart_256mocro_data3,sizeof(uart_256mocro_data3));
        else if(i==4) MySerial_2.write(uart_256mocro_data4,sizeof(uart_256mocro_data4));
        else if(i==5) MySerial_2.write(uart_256mocro_data5,sizeof(uart_256mocro_data5));
        else if(i==6) MySerial_2.write(uart_256mocro_data6,sizeof(uart_256mocro_data6));
        else if(i==7) MySerial_2.write(uart_256mocro_data7,sizeof(uart_256mocro_data7));
        delay(100);
    }
}

void uart0_int() {
    MySerial_0.begin(38400, SERIAL_8N1, 43, 44);
}

void uart1_int() {
    MySerial_1.begin(38400, SERIAL_8N1, 18, 17);
}

void uart0_send() {
    MySerial_0.println("stop_Ble\n");
}

void uart1_receive() {
    static String inputBuffer;
    
    while (MySerial_1.available() > 0) {
        char c = MySerial_1.read();
        
        if (c == '\n') {
            inputBuffer.trim();
            if (inputBuffer.length() > 0) {
                std::string value(inputBuffer.c_str());
                //Serial.printf("[UART1] Received: %s\n", value.c_str());

                if (value.find("stop_Ble") == 0) {
                    stop_Bit = 0;
                } 
                else if (value.find("Tup:") == 0 && value.length() > 4) {
                    const char* speedStr = value.substr(4).c_str();
                    float speed = atof(speedStr);
                    motor_speed = speed;
                    motormode = false;
                    //Serial.printf("[UART1] Set speed: %.8f\n", speed);
                }
                else if (value.find("Tdown:") == 0 && value.length() > 6) {
                    const char* speedStr = value.substr(6).c_str();
                    float speed = atof(speedStr);
                    motor_speed = -speed;
                    motormode = false;
                    //Serial.printf("[UART1] Set speed: %.8f\n", speed);
                }
                else if (value == "stop_ud") {
                    motor_speed = 0;
                    motormode = false;
                    //Serial.println("[UART1] Stop UD command");
                }
                else if (value.find("up:") == 0) {
                    size_t colon_pos = value.find(":");
                    size_t space_pos = value.find(" ");
                    if (colon_pos != std::string::npos && space_pos != std::string::npos) {
                        int steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
                        float speed = atof(value.substr(space_pos + 7).c_str());
                        motor_speed = speed;
                        motor_steps = steps;
                        motormode = true;
                        //Serial.printf("[UART1] Up steps:%d speed:%.8f\n", steps, speed);
                    }
                }
                else if (value.find("down:") == 0) {
                    size_t colon_pos = value.find(":");
                    size_t space_pos = value.find(" ");
                    if (colon_pos != std::string::npos && space_pos != std::string::npos) {
                        int steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
                        float speed = atof(value.substr(space_pos + 7).c_str());
                        motor_speed = -speed;
                        motor_steps = steps;
                        motormode = true;
                        //Serial.printf("[UART1] Down steps:%d speed:%.8f\n", steps, speed);
                    }
                }
                else {
                    //Serial.printf("[UART1] Unknown command: %s\n", value.c_str());
                }
            }
            inputBuffer = "";
        } else {
            inputBuffer += c;
        }
    }
    
    // Check for UART errors
    if (MySerial_1.availableForWrite() == 0) {
        //Serial.println("[UART1] Error: Buffer full");
        MySerial_1.flush();
    }
}
// void uart1_receive() {
//     if (MySerial_1.available() > 0) {
//         String input = MySerial_1.readStringUntil('\n');
//         input.trim(); // Remove any whitespace/newlines
//         std::string value(input.c_str());

//         if (value.find("stop_Ble") == 0) {
//             stop_Bit = 0;
//         } 
//         else if (value.find("Tup:") == 0 && value.length() > 6) {
//             const char* speedStr = value.substr(6).c_str();
//             float speed = atof(speedStr);
//             motor_speed = speed;
//             motormode = false;
//         }
//         else if (value.find("Tdown:") == 0 && value.length() > 6) {
//             const char* speedStr = value.substr(6).c_str();
//             float speed = atof(speedStr);
//             motor_speed = -speed;
//             motormode = false;
//         }
//         else if (value == "stop_ud") {
//             motor_speed = 0;
//             motormode = false;
//         }
//         else if (value.find("up:") == 0) {
//             size_t colon_pos = value.find(":");
//             size_t space_pos = value.find(" ");
//             if (colon_pos != std::string::npos && space_pos != std::string::npos) {
//                 int steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
//                 float speed = atof(value.substr(space_pos + 7).c_str()); // "speed:" is 6 chars
//                 motor_speed = speed;
//                 motor_steps = steps;
//                 motormode = true;
//             } else {
//             }
//         }
//         else if (value.find("down:") == 0) {
//             size_t colon_pos = value.find(":");
//             size_t space_pos = value.find(" ");
//             if (colon_pos != std::string::npos && space_pos != std::string::npos) {
//                 int steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
//                 float speed = atof(value.substr(space_pos + 7).c_str()); // "speed:" is 6 chars
//                 motor_speed = -speed;
//                 motor_steps = steps;
//                 motormode = true;
//             } else {
//             }
//         }
//         else {
//             //Serial.println("Unknown command received.");
//         }
//         // char c = MySerial_1.read();
//         // if (c == '\n' || bufferIndex >= MAX_RX_LEN-1) {
//         //     rxBuffer[bufferIndex] = '\0';
//         //     if (strcmp(rxBuffer, "q") == 0) {
//         //         stop_Bit = 0;
//         //     }
//         //     bufferIndex = 0;
//         // } else {
//         //     rxBuffer[bufferIndex++] = c;
//         // }
//     }
// }

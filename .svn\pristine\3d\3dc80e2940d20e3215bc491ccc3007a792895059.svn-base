name=NimBLE-Arduino
version=2.2.3
author=h2zero
maintainer=<PERSON> <<EMAIL>>
sentence=Bluetooth low energy (BLE) library for Arduino based on NimBLE.
paragraph=A light-weight alternative to the original bluedroid based BLE library for esp32 and Nordic nRF5 devices (must use n-able-Arduino core). Uses 50% less flash space and approximately 100KB less ram with the same functionality. Nearly 100% compatible with existing application code, migration guide included.
url=https://github.com/h2zero/NimBLE-Arduino
category=Communication
architectures=esp32,arm-ble
includes=NimBLEDevice.h

/**
 * @file main.cpp
 * @brief ESP32步进电机控制系统主程序
 * @details 基于ESP32-S3的天文望远镜赤道仪控制系统
 *          集成电机控制、编码器反馈、OLED显示、BLE通信等功能
 * <AUTHOR>
 * @version 1.0
 * @date 2024
 */

#include <Arduino.h>
#include "driver/gpio.h"
#include "driver/uart.h"
#include "wifi_manager.h"
#include "ble_manager.h"
#include "motor_control.h"
#include "pid_controller.h"
#include "encoder.h"
#include "spi_config.h"
#include "spi_tle5012b.h"
#include "preferences_manager.h"
#include "serial_command.h"
#include <Wire.h>  // I2C库
#include <oled.h>
#include "key.h"
#include "qma6100p.h"

// WiFi配置参数
const char *WIFI_SSID = "OnePlus 10 Pro";      ///< WiFi网络名称
const char *WIFI_PASSWORD = "123456789";       ///< WiFi密码

// 256细分配置命令数据包
// 这些数据包用于配置步进电机驱动器的256细分模式
uint8_t uart_256mocro_data[] = {0x05, 0x00, 0x80, 0x00, 0x00, 0x01, 0xC1, 0x7F};  ///< 256细分配置命令0
uint8_t uart_256mocro_data1[] = {0x05, 0x00, 0xec, 0x10, 0x01, 0x00, 0x53, 0x97}; ///< 256细分配置命令1
uint8_t uart_256mocro_data2[] = {0x05, 0x01, 0x80, 0x00, 0x00, 0x01, 0xC1, 0x93}; ///< 256细分配置命令2
uint8_t uart_256mocro_data3[] = {0x05, 0x01, 0xec, 0x10, 0x01, 0x00, 0x53, 0x7B}; ///< 256细分配置命令3
uint8_t uart_256mocro_data4[] = {0x05, 0x02, 0x80, 0x00, 0x00, 0x01, 0xC1, 0x09}; ///< 256细分配置命令4
uint8_t uart_256mocro_data5[] = {0x05, 0x02, 0xec, 0x10, 0x01, 0x00, 0x53, 0xE1}; ///< 256细分配置命令5
uint8_t uart_256mocro_data6[] = {0x05, 0x03, 0x80, 0x00, 0x00, 0x01, 0xC1, 0xE5}; ///< 256细分配置命令6
uint8_t uart_256mocro_data7[] = {0x05, 0x03, 0xec, 0x10, 0x01, 0x00, 0x53, 0x0D}; ///< 256细分配置命令7

extern uint16_t tle_angle;  ///< TLE5012B角度传感器读取的角度值

// 系统核心对象实例
MotorControl motor;                           ///< 电机控制器实例
Encoder encoder;                              ///< 编码器实例
PIDController pid(0.8, 0.005, 0.05);        ///< PID控制器实例，初始参数(Kp=0.8, Ki=0.005, Kd=0.05)
TaskHandle_t bleTaskHandle = NULL;            ///< BLE任务句柄
SerialCommand command;                        ///< 串口命令处理器实例

// 外部变量声明
extern int mode_show;      ///< 显示模式标志
extern bool motormode;     ///< 电机模式标志（true=位置模式，false=速度模式）
extern float motor_speed;  ///< 电机速度设定值
extern int motor_steps;    ///< 电机步数设定值

// 串口通信对象
HardwareSerial MySerial_2(2); ///< UART2串口对象，用于256细分命令发送
HardwareSerial MySerial_1(1); ///< UART1串口对象，用于接收控制命令
HardwareSerial MySerial_0(0); ///< UART0串口对象，用于发送状态信息

// 接收缓冲区配置
#define MAX_RX_LEN 64         ///< 最大接收缓冲区长度
char rxBuffer[MAX_RX_LEN];    ///< 接收缓冲区
uint8_t bufferIndex = 0;      ///< 缓冲区索引
int stop_Bit = 1;             ///< 停止位标志（1=正常运行，0=停止BLE）

// extern mode_slect mode_show;
// 函数声明
void send_256sub_command();
void uart0_int();
void uart0_send();
void uart1_receive();
void uart1_int();
void oledTask(void *pvParameters);
void commandTask(void* pvParameters);

static int last_mode = -1;

/**
 * @brief OLED显示任务
 * @details 负责初始化SPI、OLED显示屏和TLE5012B角度传感器，
 *          根据当前模式显示不同的界面，并实时更新角度和速度信息
 * @param pvParameters FreeRTOS任务参数（未使用）
 * @note 该任务运行在核心1上，优先级为2
 * @note 包含以下功能：
 *       - SPI3主机初始化
 *       - OLED显示屏初始化
 *       - TLE5012B角度传感器初始化
 *       - 根据mode_show变量切换显示模式
 *       - 实时显示电机速度和角度信息
 *       - 根据加速度计数据自动翻转显示
 */
void oledTask(void *pvParameters)
{
  vTaskDelay(1000 / portTICK_PERIOD_MS); // 延迟1秒给WiFi/BLE初始化
  spi_master_init(SPI3_HOST,SPI_DMA_CH_AUTO,4092,SPI2_DEF_PIN_NUM_MISO,SPI2_DEF_PIN_NUM_MOSI,SPI2_DEF_PIN_NUM_CLK);
  OLED_Init_GPIO();
  spi_tle5012b_init(SPI3_HOST,1000000,TLE5012B_SOFT_CS0);
  OLED_Init();
  OLED_Clear(0x00);  
    mode_show = 1;
    while(true) {
      if(mode_show != last_mode) { // 仅模式变化时刷新
        last_mode = mode_show;
        switch (mode_show)
        {
          case 1: show_start(); break;
          case 2: GPIO.out_w1tc = (1 << 10); show_stellar_mode(); OLED_ShowNum(60,40,0,10,8,1); break;
          case 3: GPIO.out_w1tc = (1 << 10); show_sun_mode(); OLED_ShowNum(60,40,0,10,8,1); break;  
          case 4: GPIO.out_w1tc = (1 << 10); show_moon_mode(); OLED_ShowNum(60,40,0,10,8,1); break; 
        default:  show_stop_mode(); OLED_ShowNum(60,40,0,10,8,1); motor.setspeedmode(); break;
        }
      }
      if(mode_show == 2 | mode_show == 3 | mode_show == 4 | mode_show == 5) {
        OLED_ShowNum(60,40,motor_speed,10,8,1);
        OLED_Display();
      }
      if (qma6100_x > 0) {OLED_Set_Flip(1);} // 倒过来显示
      else {OLED_Set_Flip(0);} // 正常显示
       tle_angle = tle5012b_read_angle(TLE5012B_SPI, TLE5012B_SOFT_CS0); 
       vTaskDelay(50 / portTICK_PERIOD_MS); // 确保SPI操作完成
       vTaskDelay(1);
    }    
}

/**
 * @brief 串口命令处理任务
 * @details 持续处理串口命令并更新电机位置控制
 * @param pvParameters FreeRTOS任务参数（未使用）
 * @note 该任务运行在核心1上，优先级为4，每50ms执行一次
 * @note 主要功能：
 *       - 处理串口接收到的控制命令
 *       - 更新电机位置控制状态
 */
void commandTask(void* pvParameters) {
  while (1) {
    command.process(pid, encoder, motor);
    motor.updatePositionControl();
    vTaskDelay(pdMS_TO_TICKS(50)); // 缩短为50ms间隔
  }
}

/**
 * @brief UART1管理任务
 * @details 专门负责UART1串口数据接收处理
 * @param pvParameters FreeRTOS任务参数（未使用）
 * @note 该任务运行在核心1上，优先级为3，每50ms执行一次
 * @note 主要功能：
 *       - 持续监听UART1串口数据
 *       - 调用uart1_receive()处理接收到的数据
 */
void uart1_manage(void* pvParameters)
{
    while (1) {
    uart1_receive();
    vTaskDelay(pdMS_TO_TICKS(50)); // 缩短为50ms间隔
  }
}

/**
 * @brief 启动BLE任务
 * @details 创建并启动蓝牙低功耗(BLE)通信任务
 * @note 只有在stop_Bit为1且BLE任务未运行时才会创建新任务
 * @note BLE任务运行在核心0上，优先级为3，堆栈大小为10000字节
 */
void startBLETask() {
    if(bleTaskHandle == NULL && (stop_Bit==1)) {
        xTaskCreatePinnedToCore(
            bleTask,
            "BLE_Task",
            10000,
            NULL,
            3,
            &bleTaskHandle,
            0
        );
    }
}

/**
 * @brief 停止BLE任务
 * @details 删除正在运行的BLE任务并清理任务句柄
 * @note 只有在BLE任务存在时才会执行删除操作
 * @note 删除任务后会将任务句柄设置为NULL
 */
void stopBLETask() {
    if(bleTaskHandle != NULL) {
        vTaskDelete(bleTaskHandle);
        bleTaskHandle = NULL;
    }
}

/**
 * @brief 系统初始化函数
 * @details 执行系统启动时的所有初始化操作
 * @note 该函数在Arduino框架中只执行一次
 * @note 主要初始化内容：
 *       - 串口命令系统初始化
 *       - 256细分命令发送
 *       - UART0和UART1初始化
 *       - 电机控制系统初始化
 *       - QMA6100P加速度计初始化
 *       - 创建各种FreeRTOS任务（OLED、按键、命令处理、UART1管理）
 *       - 启动BLE任务
 */
void setup() {
    command.setup();
    //Serial.begin(115200);
    send_256sub_command();
    uart0_int();
    uart1_int();

    vTaskDelay(1000 / portTICK_PERIOD_MS); // Send every second
    fflush(stdout);
    setvbuf(stdout, NULL, _IONBF, 0);
    motor.init();
    esp_err_t ret = qma6100p_init();
    if (ret != ESP_OK) {
        //Serial.print("QMA6100P initialization failed");
        // while(1);
    }
    
    xTaskCreatePinnedToCore(oledTask, "oled_task", 8192, NULL, 2, NULL, 1);
    xTaskCreatePinnedToCore(key_task, "key_task", 4096, NULL, 1, NULL, 0);
    xTaskCreatePinnedToCore(commandTask, "CommandTask", 4096, NULL, 4, NULL, 1);
    xTaskCreatePinnedToCore(uart1_manage, "uart1_manage", 4096, NULL, 3, NULL, 1);
    startBLETask();

}

/**
 * @brief 主循环函数
 * @details Arduino框架的主循环，持续执行系统的核心控制逻辑
 * @note 该函数在setup()完成后持续循环执行
 * @note 主要功能：
 *       - 根据stop_Bit状态控制BLE任务的启停
 *       - 根据motormode选择电机控制模式（位置模式或速度模式）
 *       - 在位置模式下设置目标位置和更新位置控制
 *       - 在速度模式下直接设置电机输出速度
 *       - 当stop_Bit为1时发送UART0数据
 *       - 每150ms执行一次循环
 */
void loop() {
    if(stop_Bit==0) {
        stopBLETask();
    }

    if(motormode)
    {
      motor.setpositionmode();
      motor.setPosition(motor_steps);
      motor.updatePositionControl();
    }
    else{
      motor.setspeedmode();
      motor.setOutput(motor_speed);
    }

    // uart1_receive();
    if(stop_Bit==1){uart0_send();}

    vTaskDelay(150 / portTICK_PERIOD_MS);
}



/**
 * @brief 发送256细分命令
 * @details 通过UART2向步进电机驱动器发送256细分配置命令
 * @note 使用115200波特率，8数据位，无校验位，1停止位
 * @note 发送引脚为GPIO46，接收引脚未使用(-1)
 * @note 依次发送8个预定义的配置数据包，每个数据包间隔100ms
 * @note 这些命令用于配置步进电机驱动器的细分模式
 */
void send_256sub_command() {
    MySerial_2.begin(115200, SERIAL_8N1, -1, 46);
    delay(500);
    for(int i=0;i<8;i++) {
        if(i==0) MySerial_2.write(uart_256mocro_data,sizeof(uart_256mocro_data));
        else if(i==1) MySerial_2.write(uart_256mocro_data1,sizeof(uart_256mocro_data1));
        else if(i==2) MySerial_2.write(uart_256mocro_data2,sizeof(uart_256mocro_data2));
        else if(i==3) MySerial_2.write(uart_256mocro_data3,sizeof(uart_256mocro_data3));
        else if(i==4) MySerial_2.write(uart_256mocro_data4,sizeof(uart_256mocro_data4));
        else if(i==5) MySerial_2.write(uart_256mocro_data5,sizeof(uart_256mocro_data5));
        else if(i==6) MySerial_2.write(uart_256mocro_data6,sizeof(uart_256mocro_data6));
        else if(i==7) MySerial_2.write(uart_256mocro_data7,sizeof(uart_256mocro_data7));
        delay(100);
    }
}

/**
 * @brief UART0初始化
 * @details 初始化UART0串口通信参数
 * @note 波特率：38400，数据格式：8N1（8数据位，无校验，1停止位）
 * @note RX引脚：GPIO43，TX引脚：GPIO44
 * @note 用于与外部设备进行串口通信
 */
void uart0_int() {
    MySerial_0.begin(38400, SERIAL_8N1, 43, 44);
}

/**
 * @brief UART1初始化
 * @details 初始化UART1串口通信参数
 * @note 波特率：38400，数据格式：8N1（8数据位，无校验，1停止位）
 * @note RX引脚：GPIO18，TX引脚：GPIO17
 * @note 用于接收外部控制命令
 */
void uart1_int() {
    MySerial_1.begin(38400, SERIAL_8N1, 18, 17);
}

/**
 * @brief UART0数据发送
 * @details 通过UART0发送停止BLE的命令
 * @note 发送固定字符串"stop_Ble\n"
 * @note 用于通知外部设备停止蓝牙连接
 */
void uart0_send() {
    MySerial_0.println("stop_Ble\n");
}

/**
 * @brief UART1数据接收处理函数
 * @details 处理从UART1接收到的控制命令，支持多种电机控制指令
 * @note 使用静态缓冲区逐字符接收数据，以换行符为命令结束标志
 * @note 支持的命令格式：
 *       - "stop_Ble": 停止蓝牙功能
 *       - "Tup:速度值": 设置正向连续转动速度
 *       - "Tdown:速度值": 设置反向连续转动速度
 *       - "stop_ud": 停止连续转动
 *       - "up:步数 speed:速度": 正向步进指定步数
 *       - "down:步数 speed:速度": 反向步进指定步数
 * @note 包含UART错误检测和缓冲区满处理
 */
void uart1_receive() {
    static String inputBuffer;

    while (MySerial_1.available() > 0) {
        char c = MySerial_1.read();

        if (c == '\n') {
            inputBuffer.trim();
            if (inputBuffer.length() > 0) {
                std::string value(inputBuffer.c_str());
                //Serial.printf("[UART1] Received: %s\n", value.c_str());

                if (value.find("stop_Ble") == 0) {
                    stop_Bit = 0;
                }
                else if (value.find("Tup:") == 0 && value.length() > 4) {
                    const char* speedStr = value.substr(4).c_str();
                    float speed = atof(speedStr);
                    motor_speed = speed;
                    motormode = false;
                    //Serial.printf("[UART1] Set speed: %.8f\n", speed);
                }
                else if (value.find("Tdown:") == 0 && value.length() > 6) {
                    const char* speedStr = value.substr(6).c_str();
                    float speed = atof(speedStr);
                    motor_speed = -speed;
                    motormode = false;
                    //Serial.printf("[UART1] Set speed: %.8f\n", speed);
                }
                else if (value == "stop_ud") {
                    motor_speed = 0;
                    motormode = false;
                    //Serial.println("[UART1] Stop UD command");
                }
                else if (value.find("up:") == 0) {
                    size_t colon_pos = value.find(":");
                    size_t space_pos = value.find(" ");
                    if (colon_pos != std::string::npos && space_pos != std::string::npos) {
                        int steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
                        float speed = atof(value.substr(space_pos + 7).c_str());
                        motor_speed = speed;
                        motor_steps = steps;
                        motormode = true;
                        //Serial.printf("[UART1] Up steps:%d speed:%.8f\n", steps, speed);
                    }
                }
                else if (value.find("down:") == 0) {
                    size_t colon_pos = value.find(":");
                    size_t space_pos = value.find(" ");
                    if (colon_pos != std::string::npos && space_pos != std::string::npos) {
                        int steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
                        float speed = atof(value.substr(space_pos + 7).c_str());
                        motor_speed = -speed;
                        motor_steps = steps;
                        motormode = true;
                        //Serial.printf("[UART1] Down steps:%d speed:%.8f\n", steps, speed);
                    }
                }
                else {
                    //Serial.printf("[UART1] Unknown command: %s\n", value.c_str());
                }
            }
            inputBuffer = "";
        } else {
            inputBuffer += c;
        }
    }
    
    // Check for UART errors
    if (MySerial_1.availableForWrite() == 0) {
        //Serial.println("[UART1] Error: Buffer full");
        MySerial_1.flush();
    }
}
// void uart1_receive() {
//     if (MySerial_1.available() > 0) {
//         String input = MySerial_1.readStringUntil('\n');
//         input.trim(); // Remove any whitespace/newlines
//         std::string value(input.c_str());

//         if (value.find("stop_Ble") == 0) {
//             stop_Bit = 0;
//         } 
//         else if (value.find("Tup:") == 0 && value.length() > 6) {
//             const char* speedStr = value.substr(6).c_str();
//             float speed = atof(speedStr);
//             motor_speed = speed;
//             motormode = false;
//         }
//         else if (value.find("Tdown:") == 0 && value.length() > 6) {
//             const char* speedStr = value.substr(6).c_str();
//             float speed = atof(speedStr);
//             motor_speed = -speed;
//             motormode = false;
//         }
//         else if (value == "stop_ud") {
//             motor_speed = 0;
//             motormode = false;
//         }
//         else if (value.find("up:") == 0) {
//             size_t colon_pos = value.find(":");
//             size_t space_pos = value.find(" ");
//             if (colon_pos != std::string::npos && space_pos != std::string::npos) {
//                 int steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
//                 float speed = atof(value.substr(space_pos + 7).c_str()); // "speed:" is 6 chars
//                 motor_speed = speed;
//                 motor_steps = steps;
//                 motormode = true;
//             } else {
//             }
//         }
//         else if (value.find("down:") == 0) {
//             size_t colon_pos = value.find(":");
//             size_t space_pos = value.find(" ");
//             if (colon_pos != std::string::npos && space_pos != std::string::npos) {
//                 int steps = atoi(value.substr(colon_pos + 1, space_pos - colon_pos - 1).c_str());
//                 float speed = atof(value.substr(space_pos + 7).c_str()); // "speed:" is 6 chars
//                 motor_speed = -speed;
//                 motor_steps = steps;
//                 motormode = true;
//             } else {
//             }
//         }
//         else {
//             //Serial.println("Unknown command received.");
//         }
//         // char c = MySerial_1.read();
//         // if (c == '\n' || bufferIndex >= MAX_RX_LEN-1) {
//         //     rxBuffer[bufferIndex] = '\0';
//         //     if (strcmp(rxBuffer, "q") == 0) {
//         //         stop_Bit = 0;
//         //     }
//         //     bufferIndex = 0;
//         // } else {
//         //     rxBuffer[bufferIndex++] = c;
//         // }
//     }
// }

#ifndef SERIAL_COMMAND_H
#define SERIAL_COMMAND_H

#include "pid_controller.h"
#include "motor_control.h"
#include "encoder.h"

class SerialCommand {
public:
    static void setup();
    static void process(PIDController& pid, Encoder& encoder, MotorControl& motor);
    
private:
    static void handlePIDCommand(char cmd, float value, PIDController& pid);
    static void handleCalibrateCommand(Encoder& encoder, MotorControl& motor);
    static void handleStatusCommand(PIDController& pid, Encoder& encoder, MotorControl& motor);
    static void position_control_motorCommand(PIDController& pid, Encoder& encoder, MotorControl& motor);
    static void processSkywatcherCommand(String command, PIDController& pid, Encoder& encoder, MotorControl& motor);
    static void processSkywatcherCommandFast(char* command, PIDController& pid, Encoder& encoder, MotorControl& motor);
    static String longToHexString(long value);
    static long hexStringToLong(String hexStr);
};

#endif

#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSansOblique24pt7bBitmaps[] PROGMEM = {
    0x01, 0xE0, 0x3C, 0x0F, 0x81, 0xE0, 0x3C, 0x07, 0x80, 0xF0, 0x3C, 0x07,
    0x80, 0xF0, 0x1E, 0x03, 0xC0, 0xF0, 0x1E, 0x03, 0xC0, 0x78, 0x0F, 0x03,
    0xC0, 0x78, 0x0F, 0x01, 0xE0, 0x38, 0x07, 0x00, 0xE0, 0x18, 0x03, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xF0, 0x1E, 0x07, 0x80, 0xF0, 0x1E, 0x00, 0x78,
    0x7B, 0xC3, 0xDE, 0x1F, 0xE1, 0xEF, 0x0F, 0x78, 0x7B, 0xC3, 0xDC, 0x1C,
    0xE0, 0xE7, 0x07, 0x30, 0x31, 0x81, 0x80, 0x00, 0x07, 0x81, 0xC0, 0x00,
    0x78, 0x3C, 0x00, 0x07, 0x03, 0xC0, 0x00, 0xF0, 0x38, 0x00, 0x0E, 0x07,
    0x80, 0x01, 0xE0, 0x70, 0x00, 0x1E, 0x0F, 0x00, 0x01, 0xC0, 0xF0, 0x00,
    0x3C, 0x0E, 0x00, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF,
    0xFE, 0x00, 0x70, 0x3C, 0x00, 0x0F, 0x03, 0x80, 0x00, 0xF0, 0x78, 0x00,
    0x0E, 0x07, 0x80, 0x01, 0xE0, 0x70, 0x00, 0x1C, 0x0F, 0x00, 0x03, 0xC0,
    0xE0, 0x00, 0x3C, 0x1E, 0x00, 0x03, 0x81, 0xE0, 0x0F, 0xFF, 0xFF, 0xE0,
    0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF, 0xE0, 0x0F, 0x03, 0x80, 0x00, 0xE0,
    0x78, 0x00, 0x1E, 0x07, 0x00, 0x01, 0xC0, 0xF0, 0x00, 0x1C, 0x0F, 0x00,
    0x03, 0xC0, 0xE0, 0x00, 0x38, 0x1E, 0x00, 0x07, 0x81, 0xC0, 0x00, 0x78,
    0x3C, 0x00, 0x07, 0x03, 0xC0, 0x00, 0x00, 0x00, 0xE0, 0x00, 0x00, 0x30,
    0x00, 0x00, 0x7F, 0x80, 0x00, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0x00, 0x7F,
    0xFF, 0xE0, 0x1F, 0x18, 0xF8, 0x0F, 0x8E, 0x1F, 0x07, 0xC3, 0x83, 0xC1,
    0xE0, 0xE0, 0xF0, 0x70, 0x38, 0x3C, 0x3C, 0x0C, 0x0F, 0x0F, 0x07, 0x00,
    0x03, 0xC1, 0xC0, 0x00, 0xF0, 0x70, 0x00, 0x3E, 0x1C, 0x00, 0x0F, 0xE6,
    0x00, 0x01, 0xFF, 0xC0, 0x00, 0x3F, 0xFE, 0x00, 0x03, 0xFF, 0xE0, 0x00,
    0x3F, 0xFC, 0x00, 0x03, 0xFF, 0x80, 0x01, 0xC7, 0xF0, 0x00, 0x70, 0x7C,
    0x00, 0x1C, 0x0F, 0x00, 0x06, 0x03, 0xCF, 0x03, 0x80, 0xF3, 0xC0, 0xE0,
    0x3C, 0xF0, 0x38, 0x0E, 0x3C, 0x0E, 0x07, 0x8F, 0x03, 0x01, 0xE3, 0xE1,
    0xC0, 0xF0, 0xF8, 0x70, 0x78, 0x1F, 0x9C, 0xFC, 0x03, 0xFF, 0xFE, 0x00,
    0x7F, 0xFF, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x38, 0x00, 0x00, 0x0E, 0x00,
    0x00, 0x03, 0x00, 0x00, 0x01, 0xC0, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x07, 0x80, 0x1F, 0x00, 0x00, 0x70, 0x07, 0xFC, 0x00, 0x0E,
    0x00, 0xFF, 0xE0, 0x01, 0xC0, 0x1E, 0x1E, 0x00, 0x3C, 0x03, 0x80, 0xF0,
    0x03, 0x80, 0x70, 0x07, 0x00, 0x70, 0x07, 0x00, 0x70, 0x0E, 0x00, 0xE0,
    0x07, 0x01, 0xC0, 0x0E, 0x00, 0x70, 0x3C, 0x00, 0xE0, 0x0E, 0x03, 0x80,
    0x0E, 0x00, 0xE0, 0x70, 0x00, 0xF0, 0x1C, 0x0E, 0x00, 0x07, 0x87, 0xC1,
    0xE0, 0x00, 0x7F, 0xF8, 0x1C, 0x00, 0x03, 0xFE, 0x03, 0x80, 0x00, 0x0F,
    0x80, 0x70, 0x00, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x00, 0x01, 0xE0, 0x1F,
    0x00, 0x00, 0x1C, 0x07, 0xFC, 0x00, 0x03, 0x80, 0xFF, 0xE0, 0x00, 0x70,
    0x1E, 0x1E, 0x00, 0x0F, 0x03, 0x80, 0xF0, 0x00, 0xE0, 0x70, 0x07, 0x00,
    0x1C, 0x07, 0x00, 0x70, 0x03, 0x80, 0xE0, 0x07, 0x00, 0x70, 0x0E, 0x00,
    0x70, 0x0F, 0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x1C, 0x00,
    0xF0, 0x1C, 0x03, 0x80, 0x07, 0x87, 0xC0, 0x70, 0x00, 0x7F, 0xF8, 0x07,
    0x00, 0x03, 0xFE, 0x00, 0xE0, 0x00, 0x0F, 0x80, 0x00, 0x01, 0xF8, 0x00,
    0x03, 0xFF, 0x00, 0x01, 0xFF, 0xE0, 0x00, 0xF8, 0x7C, 0x00, 0x78, 0x0F,
    0x00, 0x1E, 0x03, 0xC0, 0x0F, 0x00, 0xF0, 0x03, 0xC0, 0x3C, 0x00, 0xF0,
    0x1E, 0x00, 0x3C, 0x07, 0x80, 0x0F, 0x87, 0xC0, 0x01, 0xE3, 0xE0, 0x00,
    0x7F, 0xF0, 0x00, 0x0F, 0xF8, 0x00, 0x03, 0xF8, 0x00, 0x03, 0xFC, 0x00,
    0x03, 0xFF, 0x00, 0x01, 0xFB, 0xE0, 0x70, 0xF8, 0x7C, 0x1C, 0x7C, 0x1F,
    0x0E, 0x3C, 0x03, 0xE3, 0x9E, 0x00, 0x79, 0xE7, 0x80, 0x1F, 0xF3, 0xC0,
    0x03, 0xF8, 0xF0, 0x00, 0xFE, 0x3C, 0x00, 0x1F, 0x0F, 0x00, 0x07, 0xC3,
    0xE0, 0x03, 0xF8, 0xF8, 0x03, 0xFE, 0x3F, 0x83, 0xF7, 0xC7, 0xFF, 0xF8,
    0xF0, 0xFF, 0xFC, 0x3E, 0x1F, 0xFC, 0x07, 0x81, 0xFC, 0x00, 0x00, 0x7B,
    0xDF, 0xEF, 0x7B, 0xDC, 0xE7, 0x31, 0x80, 0x00, 0x0E, 0x00, 0x38, 0x00,
    0xE0, 0x03, 0x80, 0x07, 0x00, 0x1C, 0x00, 0x70, 0x01, 0xE0, 0x03, 0x80,
    0x0F, 0x00, 0x1C, 0x00, 0x78, 0x00, 0xE0, 0x03, 0xC0, 0x07, 0x00, 0x0E,
    0x00, 0x38, 0x00, 0x70, 0x00, 0xE0, 0x03, 0x80, 0x07, 0x00, 0x0E, 0x00,
    0x1C, 0x00, 0x78, 0x00, 0xE0, 0x01, 0xC0, 0x03, 0x80, 0x07, 0x00, 0x0E,
    0x00, 0x1C, 0x00, 0x38, 0x00, 0x70, 0x00, 0xE0, 0x01, 0xC0, 0x03, 0x80,
    0x07, 0x00, 0x06, 0x00, 0x0E, 0x00, 0x1C, 0x00, 0x38, 0x00, 0x30, 0x00,
    0x70, 0x00, 0xE0, 0x00, 0xC0, 0x00, 0x00, 0x30, 0x00, 0x70, 0x00, 0xE0,
    0x00, 0xC0, 0x01, 0xC0, 0x03, 0x80, 0x07, 0x00, 0x0E, 0x00, 0x0E, 0x00,
    0x1C, 0x00, 0x38, 0x00, 0x70, 0x00, 0xE0, 0x01, 0xC0, 0x03, 0x80, 0x07,
    0x00, 0x0E, 0x00, 0x1C, 0x00, 0x38, 0x00, 0x70, 0x01, 0xE0, 0x03, 0x80,
    0x07, 0x00, 0x0E, 0x00, 0x3C, 0x00, 0x70, 0x00, 0xE0, 0x01, 0xC0, 0x07,
    0x00, 0x0E, 0x00, 0x3C, 0x00, 0x70, 0x01, 0xE0, 0x03, 0x80, 0x0F, 0x00,
    0x1C, 0x00, 0x78, 0x00, 0xE0, 0x03, 0x80, 0x0E, 0x00, 0x1C, 0x00, 0x70,
    0x01, 0xC0, 0x07, 0x00, 0x00, 0x01, 0xC0, 0x07, 0x00, 0x38, 0x18, 0xE3,
    0x7B, 0xBF, 0xFF, 0xF3, 0xFF, 0x01, 0xE0, 0x1F, 0xC0, 0xF7, 0x07, 0x9E,
    0x1C, 0x38, 0x20, 0xC0, 0x00, 0x0E, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x78,
    0x00, 0x00, 0xE0, 0x00, 0x01, 0xC0, 0x00, 0x03, 0x80, 0x00, 0x07, 0x00,
    0x00, 0x1C, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xE0, 0x07, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x1C, 0x00, 0x00,
    0x78, 0x00, 0x00, 0xE0, 0x00, 0x01, 0xC0, 0x00, 0x03, 0x80, 0x00, 0x07,
    0x00, 0x00, 0x1C, 0x00, 0x00, 0x38, 0x00, 0x00, 0x3E, 0x7C, 0xF9, 0xE7,
    0xC1, 0x83, 0x0C, 0x18, 0x63, 0xC6, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFE, 0x7D, 0xF7, 0xBE, 0xF8, 0x00, 0x00, 0x18, 0x00, 0x01, 0xC0, 0x00,
    0x1C, 0x00, 0x00, 0xE0, 0x00, 0x0E, 0x00, 0x00, 0x70, 0x00, 0x07, 0x00,
    0x00, 0x30, 0x00, 0x03, 0x80, 0x00, 0x18, 0x00, 0x01, 0xC0, 0x00, 0x0C,
    0x00, 0x00, 0xE0, 0x00, 0x06, 0x00, 0x00, 0x70, 0x00, 0x03, 0x00, 0x00,
    0x38, 0x00, 0x01, 0x80, 0x00, 0x1C, 0x00, 0x00, 0xC0, 0x00, 0x0E, 0x00,
    0x00, 0x60, 0x00, 0x07, 0x00, 0x00, 0x70, 0x00, 0x03, 0x80, 0x00, 0x38,
    0x00, 0x01, 0x80, 0x00, 0x1C, 0x00, 0x00, 0xC0, 0x00, 0x0E, 0x00, 0x00,
    0x60, 0x00, 0x07, 0x00, 0x00, 0x30, 0x00, 0x03, 0x80, 0x00, 0x18, 0x00,
    0x00, 0x00, 0x0F, 0xC0, 0x00, 0xFF, 0xE0, 0x03, 0xFF, 0xE0, 0x0F, 0xFF,
    0xE0, 0x3F, 0x0F, 0xC0, 0xF8, 0x07, 0x81, 0xE0, 0x0F, 0x87, 0x80, 0x0F,
    0x1F, 0x00, 0x1E, 0x3C, 0x00, 0x3C, 0x78, 0x00, 0x79, 0xE0, 0x00, 0xF3,
    0xC0, 0x01, 0xE7, 0x80, 0x07, 0xDE, 0x00, 0x0F, 0xBC, 0x00, 0x1E, 0x78,
    0x00, 0x3C, 0xF0, 0x00, 0x79, 0xE0, 0x00, 0xF7, 0x80, 0x03, 0xEF, 0x00,
    0x07, 0xDE, 0x00, 0x0F, 0x3C, 0x00, 0x1E, 0x78, 0x00, 0x7C, 0xF0, 0x00,
    0xF1, 0xE0, 0x03, 0xE3, 0xC0, 0x07, 0x87, 0xC0, 0x1F, 0x0F, 0x80, 0x7C,
    0x0F, 0xC3, 0xF0, 0x1F, 0xFF, 0xC0, 0x1F, 0xFF, 0x00, 0x1F, 0xFC, 0x00,
    0x0F, 0xC0, 0x00, 0x00, 0x18, 0x01, 0xC0, 0x1C, 0x01, 0xE0, 0x1F, 0x0F,
    0xFB, 0xFF, 0xDF, 0xFC, 0xFF, 0xE0, 0x0F, 0x00, 0x78, 0x07, 0xC0, 0x3C,
    0x01, 0xE0, 0x0F, 0x00, 0x78, 0x07, 0xC0, 0x3C, 0x01, 0xE0, 0x0F, 0x00,
    0x78, 0x07, 0xC0, 0x3C, 0x01, 0xE0, 0x0F, 0x00, 0x78, 0x07, 0xC0, 0x3C,
    0x01, 0xE0, 0x0F, 0x00, 0x78, 0x07, 0xC0, 0x3C, 0x00, 0x00, 0x03, 0xFC,
    0x00, 0x03, 0xFF, 0xE0, 0x00, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0xE0, 0x0F,
    0xC0, 0xFC, 0x03, 0xE0, 0x07, 0xC0, 0xF8, 0x00, 0xF8, 0x1F, 0x00, 0x0F,
    0x03, 0xC0, 0x01, 0xE0, 0xF8, 0x00, 0x3C, 0x1E, 0x00, 0x07, 0x80, 0x00,
    0x01, 0xE0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x03, 0xE0,
    0x00, 0x00, 0xF8, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x1F,
    0xC0, 0x00, 0x0F, 0xE0, 0x00, 0x07, 0xF0, 0x00, 0x03, 0xF8, 0x00, 0x00,
    0xFC, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x03, 0xE0, 0x00,
    0x00, 0xF8, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0xFF,
    0xFF, 0xFC, 0x3F, 0xFF, 0xFF, 0x07, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFC,
    0x00, 0x00, 0x07, 0xF0, 0x00, 0x1F, 0xFE, 0x00, 0x3F, 0xFF, 0x80, 0x3F,
    0xFF, 0xE0, 0x1F, 0x81, 0xF8, 0x1F, 0x00, 0x7C, 0x1F, 0x00, 0x1E, 0x0F,
    0x00, 0x0F, 0x0F, 0x80, 0x07, 0x87, 0x80, 0x03, 0xC0, 0x00, 0x03, 0xC0,
    0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x07, 0xF0, 0x00, 0x7F, 0xE0,
    0x00, 0x3F, 0xE0, 0x00, 0x1F, 0xF8, 0x00, 0x1F, 0xFE, 0x00, 0x00, 0x3F,
    0x00, 0x00, 0x07, 0xC0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0xF0, 0x00, 0x00,
    0x79, 0xE0, 0x00, 0x3C, 0xF0, 0x00, 0x1E, 0x78, 0x00, 0x1E, 0x3C, 0x00,
    0x0F, 0x1E, 0x00, 0x0F, 0x0F, 0x80, 0x1F, 0x83, 0xF0, 0x3F, 0x81, 0xFF,
    0xFF, 0x80, 0x7F, 0xFF, 0x80, 0x1F, 0xFF, 0x00, 0x03, 0xFC, 0x00, 0x00,
    0x00, 0x00, 0x0E, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x7E,
    0x00, 0x00, 0xFE, 0x00, 0x01, 0xFC, 0x00, 0x03, 0xFC, 0x00, 0x07, 0xBC,
    0x00, 0x0F, 0xBC, 0x00, 0x1F, 0x7C, 0x00, 0x3E, 0x78, 0x00, 0x7C, 0x78,
    0x00, 0xF8, 0x78, 0x00, 0xF0, 0x78, 0x01, 0xE0, 0xF0, 0x03, 0xC0, 0xF0,
    0x07, 0x80, 0xF0, 0x0F, 0x00, 0xF0, 0x1E, 0x01, 0xF0, 0x3C, 0x01, 0xE0,
    0x78, 0x01, 0xE0, 0x7F, 0xFF, 0xFF, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE,
    0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xC0, 0x00, 0x03, 0xC0, 0x00, 0x03, 0xC0,
    0x00, 0x07, 0x80, 0x00, 0x07, 0x80, 0x00, 0x07, 0x80, 0x00, 0x07, 0x80,
    0x00, 0x0F, 0x80, 0x00, 0x7F, 0xFF, 0xC0, 0x1F, 0xFF, 0xF8, 0x03, 0xFF,
    0xFF, 0x00, 0x7F, 0xFF, 0xE0, 0x1E, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00,
    0x78, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x78, 0x00,
    0x00, 0x1E, 0x00, 0x00, 0x03, 0xC7, 0xE0, 0x00, 0xF7, 0xFF, 0x80, 0x1F,
    0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0x80, 0xFE, 0x03, 0xF0, 0x1F, 0x00, 0x3F,
    0x03, 0xC0, 0x03, 0xE0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x07, 0x80, 0x00,
    0x00, 0xF0, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x70,
    0x00, 0x00, 0x1E, 0x1E, 0x00, 0x03, 0xC3, 0xC0, 0x00, 0xF0, 0x7C, 0x00,
    0x3C, 0x0F, 0x80, 0x0F, 0x80, 0xFC, 0x07, 0xE0, 0x1F, 0xFF, 0xF8, 0x01,
    0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x07,
    0xE0, 0x00, 0x3F, 0xF8, 0x00, 0x7F, 0xFC, 0x00, 0xFF, 0xFE, 0x01, 0xF8,
    0x3E, 0x03, 0xE0, 0x1F, 0x07, 0xC0, 0x1F, 0x0F, 0x80, 0x0F, 0x0F, 0x00,
    0x0F, 0x1F, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x3C, 0x1F,
    0x80, 0x3C, 0x7F, 0xE0, 0x3D, 0xFF, 0xF0, 0x7B, 0xFF, 0xF8, 0x7F, 0xC1,
    0xF8, 0x7F, 0x00, 0x7C, 0x7E, 0x00, 0x7C, 0xFC, 0x00, 0x3C, 0xF8, 0x00,
    0x3C, 0xF8, 0x00, 0x3C, 0xF0, 0x00, 0x3C, 0xF0, 0x00, 0x38, 0xF0, 0x00,
    0x78, 0xF0, 0x00, 0x78, 0xF0, 0x00, 0xF0, 0xF8, 0x01, 0xF0, 0x7C, 0x03,
    0xE0, 0x7E, 0x0F, 0xC0, 0x3F, 0xFF, 0xC0, 0x3F, 0xFF, 0x80, 0x0F, 0xFE,
    0x00, 0x03, 0xF8, 0x00, 0x1F, 0xFF, 0xFF, 0x87, 0xFF, 0xFF, 0xE1, 0xFF,
    0xFF, 0xF8, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x07, 0x80,
    0x00, 0x03, 0xC0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x78,
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x07, 0x80, 0x00, 0x03,
    0xC0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x3C, 0x00, 0x00,
    0x1E, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x03, 0xC0, 0x00, 0x01, 0xE0, 0x00,
    0x00, 0xF8, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x07, 0x80,
    0x00, 0x03, 0xC0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x1E,
    0x00, 0x00, 0x0F, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x01, 0xF0, 0x00, 0x00,
    0x78, 0x00, 0x00, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x1F, 0xFE, 0x00, 0x1F,
    0xFF, 0x80, 0x1F, 0xFF, 0xE0, 0x1F, 0x81, 0xF8, 0x1F, 0x00, 0x7C, 0x0F,
    0x00, 0x1E, 0x0F, 0x00, 0x0F, 0x07, 0x80, 0x07, 0x83, 0xC0, 0x03, 0xC1,
    0xE0, 0x03, 0xC0, 0xF8, 0x03, 0xC0, 0x7E, 0x07, 0xC0, 0x1F, 0xFF, 0xC0,
    0x07, 0xFF, 0xC0, 0x03, 0xFF, 0xE0, 0x07, 0xFF, 0xF8, 0x07, 0xE0, 0x7E,
    0x07, 0xC0, 0x0F, 0x07, 0x80, 0x07, 0xC7, 0xC0, 0x01, 0xE3, 0xC0, 0x00,
    0xF3, 0xC0, 0x00, 0x79, 0xE0, 0x00, 0x3C, 0xF0, 0x00, 0x1C, 0x78, 0x00,
    0x1E, 0x3C, 0x00, 0x0F, 0x1F, 0x00, 0x0F, 0x0F, 0xC0, 0x0F, 0x83, 0xF0,
    0x3F, 0x81, 0xFF, 0xFF, 0x80, 0x7F, 0xFF, 0x80, 0x0F, 0xFF, 0x00, 0x01,
    0xFE, 0x00, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x7F, 0xF0, 0x01, 0xFF, 0xFC,
    0x03, 0xFF, 0xFC, 0x07, 0xF0, 0x7E, 0x07, 0xC0, 0x3E, 0x0F, 0x80, 0x1F,
    0x0F, 0x00, 0x0F, 0x1E, 0x00, 0x0F, 0x1E, 0x00, 0x0F, 0x3C, 0x00, 0x0F,
    0x3C, 0x00, 0x0F, 0x3C, 0x00, 0x1F, 0x3C, 0x00, 0x1F, 0x3C, 0x00, 0x3F,
    0x3E, 0x00, 0x7E, 0x3E, 0x00, 0xFE, 0x1F, 0x83, 0xFE, 0x1F, 0xFF, 0xFE,
    0x0F, 0xFF, 0xBC, 0x07, 0xFE, 0x3C, 0x01, 0xF8, 0x7C, 0x00, 0x00, 0x7C,
    0x00, 0x00, 0x78, 0x00, 0x00, 0xF8, 0x00, 0x00, 0xF0, 0xF0, 0x01, 0xF0,
    0xF0, 0x03, 0xE0, 0xF8, 0x07, 0xC0, 0xFC, 0x1F, 0xC0, 0x7F, 0xFF, 0x80,
    0x3F, 0xFE, 0x00, 0x1F, 0xFC, 0x00, 0x07, 0xF0, 0x00, 0x07, 0xC1, 0xF0,
    0x78, 0x3E, 0x0F, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7C, 0x1F,
    0x07, 0x83, 0xE0, 0xF8, 0x00, 0x03, 0xE0, 0x7C, 0x0F, 0x03, 0xE0, 0x7C,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xE0, 0x7C, 0x0F,
    0x81, 0xE0, 0x7C, 0x01, 0x80, 0x30, 0x0C, 0x01, 0x80, 0x60, 0x3C, 0x06,
    0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x70, 0x00, 0x00, 0xF8, 0x00,
    0x00, 0xFE, 0x00, 0x01, 0xFF, 0x00, 0x03, 0xFE, 0x00, 0x03, 0xFE, 0x00,
    0x07, 0xFC, 0x00, 0x07, 0xFC, 0x00, 0x0F, 0xF8, 0x00, 0x07, 0xF0, 0x00,
    0x03, 0xF0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFE,
    0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x1F, 0xF0, 0x00,
    0x01, 0xFF, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x0E,
    0x00, 0x00, 0x00, 0x80, 0x1F, 0xFF, 0xFF, 0xC7, 0xFF, 0xFF, 0xE3, 0xFF,
    0xFF, 0xF8, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0x1F, 0xFF, 0xFF,
    0xC7, 0xFF, 0xFF, 0xE1, 0xFF, 0xFF, 0xF8, 0x04, 0x00, 0x00, 0x01, 0xC0,
    0x00, 0x00, 0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x03, 0xFE, 0x00, 0x00,
    0x3F, 0xE0, 0x00, 0x03, 0xFE, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFE,
    0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x3F, 0x00, 0x00,
    0x3F, 0x80, 0x00, 0x7F, 0xC0, 0x00, 0xFF, 0x80, 0x00, 0xFF, 0x80, 0x01,
    0xFF, 0x00, 0x01, 0xFF, 0x00, 0x03, 0xFE, 0x00, 0x01, 0xFC, 0x00, 0x00,
    0x7C, 0x00, 0x00, 0x38, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0xFE,
    0x00, 0x3F, 0xF8, 0x0F, 0xFF, 0xC1, 0xFF, 0xFE, 0x1F, 0x03, 0xE3, 0xE0,
    0x1F, 0x7C, 0x00, 0xF7, 0x80, 0x0F, 0x78, 0x00, 0xFF, 0x00, 0x0F, 0xF0,
    0x01, 0xF0, 0x00, 0x1E, 0x00, 0x03, 0xE0, 0x00, 0x7C, 0x00, 0x0F, 0x80,
    0x01, 0xF0, 0x00, 0x3E, 0x00, 0x0F, 0xC0, 0x01, 0xF8, 0x00, 0x1F, 0x00,
    0x03, 0xE0, 0x00, 0x7C, 0x00, 0x07, 0x80, 0x00, 0x78, 0x00, 0x0F, 0x80,
    0x00, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x1E, 0x00, 0x01, 0xE0, 0x00, 0x1E, 0x00, 0x01, 0xE0, 0x00, 0x3E,
    0x00, 0x00, 0x00, 0x00, 0x01, 0xFF, 0x80, 0x00, 0x00, 0x00, 0xFF, 0xFF,
    0x80, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xFE,
    0x00, 0x00, 0x7F, 0xE0, 0x0F, 0xF8, 0x00, 0x0F, 0xF0, 0x00, 0x1F, 0xE0,
    0x00, 0xFE, 0x00, 0x00, 0x3F, 0x80, 0x0F, 0xC0, 0x00, 0x00, 0xFC, 0x00,
    0xFC, 0x00, 0x00, 0x01, 0xF0, 0x0F, 0xC0, 0x00, 0x00, 0x0F, 0x80, 0xF8,
    0x00, 0xFC, 0x00, 0x3E, 0x0F, 0x80, 0x1F, 0xF9, 0xE1, 0xF0, 0x78, 0x03,
    0xFF, 0xCF, 0x07, 0x87, 0xC0, 0x3F, 0x0F, 0xF0, 0x3C, 0x7C, 0x03, 0xE0,
    0x3F, 0x01, 0xE3, 0xC0, 0x3E, 0x01, 0xF8, 0x0F, 0x3E, 0x03, 0xE0, 0x0F,
    0x80, 0x79, 0xE0, 0x1E, 0x00, 0x7C, 0x03, 0xDF, 0x01, 0xE0, 0x03, 0xC0,
    0x3E, 0xF0, 0x1F, 0x00, 0x3E, 0x01, 0xE7, 0x80, 0xF0, 0x01, 0xE0, 0x0F,
    0x38, 0x07, 0x80, 0x0F, 0x00, 0xFB, 0xC0, 0x78, 0x00, 0xF0, 0x07, 0x9E,
    0x03, 0xC0, 0x07, 0x80, 0x7C, 0xF0, 0x1E, 0x00, 0x78, 0x07, 0xC7, 0x80,
    0xF0, 0x07, 0xC0, 0x7E, 0x3C, 0x07, 0x80, 0x7C, 0x07, 0xE1, 0xE0, 0x3E,
    0x07, 0xE0, 0x7E, 0x0F, 0x00, 0xF8, 0x7F, 0x8F, 0xC0, 0x7C, 0x07, 0xFF,
    0x7F, 0xFC, 0x01, 0xE0, 0x1F, 0xF1, 0xFF, 0x80, 0x0F, 0x00, 0x7E, 0x0F,
    0xF0, 0x00, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x00,
    0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00,
    0x00, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x03, 0xFE, 0x00, 0xF8, 0x00, 0x00,
    0x0F, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0x00, 0x00, 0x00,
    0x3F, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x1F, 0x80, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x03, 0xF8, 0x00, 0x00,
    0x1F, 0xE0, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x03, 0xDF, 0x00, 0x00, 0x1E,
    0x7C, 0x00, 0x00, 0x79, 0xF0, 0x00, 0x03, 0xC7, 0xC0, 0x00, 0x0F, 0x1F,
    0x00, 0x00, 0x78, 0x3C, 0x00, 0x03, 0xE0, 0xF0, 0x00, 0x0F, 0x03, 0xE0,
    0x00, 0x78, 0x0F, 0x80, 0x01, 0xE0, 0x3E, 0x00, 0x0F, 0x00, 0xF8, 0x00,
    0x3C, 0x03, 0xE0, 0x01, 0xE0, 0x0F, 0x80, 0x0F, 0x80, 0x1E, 0x00, 0x3C,
    0x00, 0x7C, 0x01, 0xFF, 0xFF, 0xF0, 0x07, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF,
    0xFF, 0x00, 0xFF, 0xFF, 0xFC, 0x07, 0xC0, 0x01, 0xF0, 0x3E, 0x00, 0x03,
    0xC0, 0xF8, 0x00, 0x0F, 0x87, 0xC0, 0x00, 0x3E, 0x1E, 0x00, 0x00, 0xF8,
    0xF8, 0x00, 0x03, 0xE3, 0xC0, 0x00, 0x0F, 0x9F, 0x00, 0x00, 0x3E, 0xF8,
    0x00, 0x00, 0x7B, 0xE0, 0x00, 0x01, 0xF0, 0x01, 0xFF, 0xFF, 0x00, 0x0F,
    0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xE0, 0x3E, 0x00,
    0x1F, 0x81, 0xE0, 0x00, 0x7C, 0x0F, 0x00, 0x01, 0xE0, 0x78, 0x00, 0x0F,
    0x03, 0xC0, 0x00, 0x78, 0x3C, 0x00, 0x03, 0xC1, 0xE0, 0x00, 0x3C, 0x0F,
    0x00, 0x01, 0xE0, 0x78, 0x00, 0x1E, 0x07, 0xC0, 0x03, 0xE0, 0x3F, 0xFF,
    0xFC, 0x01, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFE,
    0x07, 0x80, 0x01, 0xF0, 0x3C, 0x00, 0x07, 0xC1, 0xE0, 0x00, 0x1E, 0x0F,
    0x00, 0x00, 0xF0, 0xF0, 0x00, 0x07, 0x87, 0x80, 0x00, 0x3C, 0x3C, 0x00,
    0x01, 0xE1, 0xE0, 0x00, 0x1E, 0x1F, 0x00, 0x00, 0xF0, 0xF0, 0x00, 0x0F,
    0x87, 0x80, 0x00, 0xF8, 0x3C, 0x00, 0x1F, 0x81, 0xFF, 0xFF, 0xF8, 0x1F,
    0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xF8, 0x07, 0xFF, 0xFF, 0x00, 0x00, 0x00,
    0x01, 0xFE, 0x00, 0x00, 0x3F, 0xFE, 0x00, 0x03, 0xFF, 0xFE, 0x00, 0x1F,
    0xFF, 0xFC, 0x00, 0xFE, 0x03, 0xF0, 0x07, 0xE0, 0x03, 0xE0, 0x3E, 0x00,
    0x07, 0x81, 0xF0, 0x00, 0x1E, 0x07, 0x80, 0x00, 0x3C, 0x3C, 0x00, 0x00,
    0xF1, 0xF0, 0x00, 0x03, 0xC7, 0x80, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00,
    0xF0, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x78,
    0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x3C, 0x00,
    0x00, 0x00, 0xF0, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x0F, 0x00, 0x00,
    0x00, 0x3C, 0x00, 0x00, 0x3C, 0xF0, 0x00, 0x01, 0xF3, 0xC0, 0x00, 0x07,
    0x8F, 0x80, 0x00, 0x3E, 0x3E, 0x00, 0x00, 0xF0, 0x7C, 0x00, 0x07, 0xC1,
    0xF0, 0x00, 0x3E, 0x03, 0xE0, 0x03, 0xF0, 0x0F, 0xE0, 0x3F, 0x80, 0x1F,
    0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x3F,
    0xC0, 0x00, 0x01, 0xFF, 0xFF, 0x00, 0x03, 0xFF, 0xFF, 0x80, 0x07, 0xFF,
    0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0x80, 0x3E, 0x00, 0x3F, 0x80, 0x78, 0x00,
    0x1F, 0x80, 0xF0, 0x00, 0x1F, 0x03, 0xE0, 0x00, 0x1E, 0x07, 0xC0, 0x00,
    0x3E, 0x0F, 0x00, 0x00, 0x3C, 0x1E, 0x00, 0x00, 0x78, 0x3C, 0x00, 0x00,
    0xF0, 0xF8, 0x00, 0x01, 0xE1, 0xF0, 0x00, 0x03, 0xC3, 0xC0, 0x00, 0x07,
    0x87, 0x80, 0x00, 0x0F, 0x0F, 0x00, 0x00, 0x3C, 0x3E, 0x00, 0x00, 0x78,
    0x7C, 0x00, 0x00, 0xF0, 0xF0, 0x00, 0x01, 0xE1, 0xE0, 0x00, 0x07, 0x87,
    0xC0, 0x00, 0x0F, 0x0F, 0x80, 0x00, 0x3E, 0x1E, 0x00, 0x00, 0x78, 0x3C,
    0x00, 0x01, 0xF0, 0x78, 0x00, 0x03, 0xC1, 0xF0, 0x00, 0x0F, 0x03, 0xE0,
    0x00, 0x3E, 0x07, 0x80, 0x01, 0xF8, 0x0F, 0x00, 0x0F, 0xE0, 0x1F, 0xFF,
    0xFF, 0x80, 0x7F, 0xFF, 0xFE, 0x00, 0xFF, 0xFF, 0xF0, 0x01, 0xFF, 0xFF,
    0x00, 0x00, 0x01, 0xFF, 0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xFC, 0x07, 0xFF,
    0xFF, 0xF0, 0x1F, 0xFF, 0xFF, 0xE0, 0x3E, 0x00, 0x00, 0x00, 0x78, 0x00,
    0x00, 0x00, 0xF0, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x07, 0xC0, 0x00,
    0x00, 0x0F, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x3C, 0x00, 0x00,
    0x00, 0xF8, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00,
    0x07, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0xE0, 0x3F, 0xFF, 0xFF, 0x80,
    0x7F, 0xFF, 0xFF, 0x00, 0xF0, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x07,
    0xC0, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x3C,
    0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x03, 0xE0,
    0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x1F, 0xFF,
    0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFF, 0x81, 0xFF, 0xFF,
    0xFE, 0x00, 0x01, 0xFF, 0xFF, 0xFC, 0x07, 0xFF, 0xFF, 0xF0, 0x1F, 0xFF,
    0xFF, 0xC0, 0xFF, 0xFF, 0xFE, 0x03, 0xE0, 0x00, 0x00, 0x0F, 0x00, 0x00,
    0x00, 0x3C, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00,
    0x1E, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x0F,
    0x80, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x03, 0xFF,
    0xFF, 0xC0, 0x0F, 0xFF, 0xFF, 0x00, 0x7F, 0xFF, 0xFC, 0x01, 0xFF, 0xFF,
    0xF0, 0x07, 0x80, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xF8, 0x00, 0x00,
    0x03, 0xE0, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x00,
    0xF0, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x78,
    0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x3E, 0x00,
    0x00, 0x00, 0xF0, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x7F, 0xC0, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xFF, 0x00, 0x07,
    0xFF, 0xFF, 0xC0, 0x07, 0xF0, 0x0F, 0xF0, 0x0F, 0xC0, 0x00, 0xF8, 0x0F,
    0xC0, 0x00, 0x3E, 0x07, 0x80, 0x00, 0x1F, 0x07, 0x80, 0x00, 0x07, 0x87,
    0xC0, 0x00, 0x03, 0xC3, 0xC0, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x03,
    0xE0, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x00,
    0xF0, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x3C, 0x00, 0x1F, 0xFF,
    0xBC, 0x00, 0x0F, 0xFF, 0xDE, 0x00, 0x0F, 0xFF, 0xEF, 0x00, 0x07, 0xFF,
    0xF7, 0x80, 0x00, 0x00, 0x73, 0xC0, 0x00, 0x00, 0x39, 0xE0, 0x00, 0x00,
    0x3C, 0xF0, 0x00, 0x00, 0x1E, 0x78, 0x00, 0x00, 0x1F, 0x3E, 0x00, 0x00,
    0x0F, 0x8F, 0x00, 0x00, 0x0F, 0x87, 0xC0, 0x00, 0x0F, 0xC3, 0xF0, 0x00,
    0x0F, 0xE0, 0xFC, 0x00, 0x1F, 0xF0, 0x7F, 0x80, 0x7F, 0x78, 0x1F, 0xFF,
    0xFE, 0x38, 0x03, 0xFF, 0xFE, 0x1C, 0x00, 0xFF, 0xFC, 0x0E, 0x00, 0x0F,
    0xF0, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x07, 0x80, 0xF0, 0x00, 0x03, 0xC0,
    0x78, 0x00, 0x03, 0xE0, 0x7C, 0x00, 0x01, 0xF0, 0x3E, 0x00, 0x00, 0xF0,
    0x1E, 0x00, 0x00, 0x78, 0x0F, 0x00, 0x00, 0x3C, 0x0F, 0x80, 0x00, 0x3E,
    0x07, 0xC0, 0x00, 0x1F, 0x03, 0xC0, 0x00, 0x0F, 0x01, 0xE0, 0x00, 0x07,
    0x80, 0xF0, 0x00, 0x03, 0xC0, 0xF8, 0x00, 0x03, 0xE0, 0x7C, 0x00, 0x01,
    0xF0, 0x3C, 0x00, 0x00, 0xF0, 0x1F, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF,
    0xFC, 0x0F, 0xFF, 0xFF, 0xFE, 0x07, 0xFF, 0xFF, 0xFE, 0x03, 0xC0, 0x00,
    0x0F, 0x01, 0xE0, 0x00, 0x07, 0x81, 0xF0, 0x00, 0x07, 0xC0, 0xF8, 0x00,
    0x03, 0xE0, 0x78, 0x00, 0x01, 0xE0, 0x3C, 0x00, 0x00, 0xF0, 0x1E, 0x00,
    0x00, 0x78, 0x1F, 0x00, 0x00, 0x7C, 0x0F, 0x80, 0x00, 0x3C, 0x07, 0x80,
    0x00, 0x1E, 0x03, 0xC0, 0x00, 0x0F, 0x01, 0xE0, 0x00, 0x0F, 0x81, 0xF0,
    0x00, 0x07, 0xC0, 0xF0, 0x00, 0x03, 0xC0, 0x78, 0x00, 0x01, 0xE0, 0x00,
    0x03, 0xE0, 0x7C, 0x0F, 0x81, 0xE0, 0x3C, 0x07, 0x81, 0xF0, 0x3E, 0x07,
    0x80, 0xF0, 0x1E, 0x07, 0xC0, 0xF8, 0x1E, 0x03, 0xC0, 0x78, 0x1F, 0x03,
    0xE0, 0x78, 0x0F, 0x01, 0xE0, 0x7C, 0x0F, 0x81, 0xE0, 0x3C, 0x07, 0x81,
    0xF0, 0x3E, 0x07, 0x80, 0xF0, 0x1E, 0x07, 0xC0, 0xF8, 0x1E, 0x00, 0x00,
    0x00, 0x07, 0x80, 0x00, 0x03, 0xC0, 0x00, 0x03, 0xC0, 0x00, 0x01, 0xE0,
    0x00, 0x00, 0xF0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x3C,
    0x00, 0x00, 0x1E, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x07, 0x80, 0x00, 0x07,
    0xC0, 0x00, 0x03, 0xC0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0xF0, 0x00, 0x00,
    0x78, 0x00, 0x00, 0x78, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x1E, 0x00, 0x00,
    0x0F, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x07, 0x80, 0x00, 0x03, 0xC0, 0xF0,
    0x01, 0xE0, 0x78, 0x00, 0xF0, 0x78, 0x00, 0xF8, 0x3C, 0x00, 0x78, 0x1E,
    0x00, 0x3C, 0x0F, 0x00, 0x3E, 0x07, 0xC0, 0x3E, 0x03, 0xF0, 0x7E, 0x00,
    0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0x00, 0x0F, 0xFE, 0x00, 0x01, 0xFC, 0x00,
    0x00, 0x01, 0xE0, 0x00, 0x0F, 0xC0, 0x78, 0x00, 0x07, 0xC0, 0x1E, 0x00,
    0x03, 0xE0, 0x0F, 0x80, 0x03, 0xF0, 0x03, 0xE0, 0x01, 0xF8, 0x00, 0xF0,
    0x00, 0xFC, 0x00, 0x3C, 0x00, 0x7C, 0x00, 0x1F, 0x00, 0x3E, 0x00, 0x07,
    0xC0, 0x3F, 0x00, 0x01, 0xE0, 0x1F, 0x80, 0x00, 0x78, 0x0F, 0x80, 0x00,
    0x1E, 0x07, 0xC0, 0x00, 0x0F, 0x83, 0xE0, 0x00, 0x03, 0xE3, 0xF0, 0x00,
    0x00, 0xF1, 0xFC, 0x00, 0x00, 0x3C, 0xFF, 0x00, 0x00, 0x0F, 0x7F, 0xE0,
    0x00, 0x07, 0xFE, 0xF8, 0x00, 0x01, 0xFE, 0x1E, 0x00, 0x00, 0x7F, 0x07,
    0xC0, 0x00, 0x1F, 0x80, 0xF0, 0x00, 0x0F, 0xC0, 0x3E, 0x00, 0x03, 0xE0,
    0x07, 0x80, 0x00, 0xF0, 0x01, 0xF0, 0x00, 0x3C, 0x00, 0x7C, 0x00, 0x0F,
    0x00, 0x0F, 0x80, 0x07, 0xC0, 0x03, 0xE0, 0x01, 0xF0, 0x00, 0x7C, 0x00,
    0x78, 0x00, 0x1F, 0x00, 0x1E, 0x00, 0x03, 0xE0, 0x07, 0x80, 0x00, 0xF8,
    0x03, 0xE0, 0x00, 0x1F, 0x00, 0xF0, 0x00, 0x07, 0xC0, 0x3C, 0x00, 0x00,
    0xF8, 0x00, 0x01, 0xE0, 0x00, 0x07, 0x80, 0x00, 0x1E, 0x00, 0x00, 0xF8,
    0x00, 0x03, 0xE0, 0x00, 0x0F, 0x00, 0x00, 0x3C, 0x00, 0x01, 0xF0, 0x00,
    0x07, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0x78, 0x00, 0x01, 0xE0, 0x00, 0x0F,
    0x80, 0x00, 0x3E, 0x00, 0x00, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x00,
    0x00, 0x7C, 0x00, 0x01, 0xE0, 0x00, 0x07, 0x80, 0x00, 0x1E, 0x00, 0x00,
    0xF8, 0x00, 0x03, 0xE0, 0x00, 0x0F, 0x00, 0x00, 0x3C, 0x00, 0x00, 0xF0,
    0x00, 0x07, 0xC0, 0x00, 0x1F, 0x00, 0x00, 0x78, 0x00, 0x01, 0xE0, 0x00,
    0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF, 0xE0,
    0x01, 0xF8, 0x00, 0x00, 0x7E, 0x03, 0xF8, 0x00, 0x01, 0xFC, 0x0F, 0xF0,
    0x00, 0x03, 0xF8, 0x1F, 0xE0, 0x00, 0x0F, 0xF0, 0x3F, 0xC0, 0x00, 0x1F,
    0xC0, 0x7F, 0x80, 0x00, 0x7F, 0x80, 0xFF, 0x00, 0x00, 0xEF, 0x03, 0xFE,
    0x00, 0x03, 0xFE, 0x07, 0xBC, 0x00, 0x0F, 0x78, 0x0F, 0x3C, 0x00, 0x1E,
    0xF0, 0x1E, 0x78, 0x00, 0x79, 0xE0, 0x3C, 0xF0, 0x00, 0xF3, 0xC0, 0xF9,
    0xE0, 0x03, 0xCF, 0x81, 0xE3, 0xC0, 0x07, 0x9E, 0x03, 0xC7, 0x80, 0x1E,
    0x3C, 0x07, 0x8F, 0x00, 0x38, 0x78, 0x1F, 0x1E, 0x00, 0xF0, 0xF0, 0x3C,
    0x1E, 0x03, 0xC3, 0xE0, 0x78, 0x3C, 0x07, 0x87, 0x80, 0xF0, 0x78, 0x1E,
    0x0F, 0x01, 0xE0, 0xF0, 0x3C, 0x1E, 0x07, 0xC1, 0xE0, 0xF0, 0x7C, 0x0F,
    0x03, 0xC1, 0xE0, 0xF0, 0x1E, 0x07, 0x87, 0x81, 0xE0, 0x3C, 0x0F, 0x0E,
    0x03, 0xC0, 0x78, 0x0F, 0x3C, 0x07, 0x81, 0xF0, 0x1E, 0x70, 0x1F, 0x03,
    0xC0, 0x3D, 0xE0, 0x3C, 0x07, 0x80, 0x7F, 0x80, 0x78, 0x0F, 0x00, 0xFF,
    0x00, 0xF0, 0x3E, 0x01, 0xFC, 0x01, 0xE0, 0x78, 0x03, 0xF8, 0x07, 0xC0,
    0xF0, 0x07, 0xE0, 0x0F, 0x01, 0xE0, 0x07, 0x80, 0x1E, 0x00, 0x01, 0xF0,
    0x00, 0x03, 0xC0, 0x7E, 0x00, 0x01, 0xF0, 0x3F, 0x80, 0x00, 0x78, 0x0F,
    0xE0, 0x00, 0x1E, 0x03, 0xFC, 0x00, 0x07, 0x80, 0xFF, 0x00, 0x03, 0xE0,
    0x3F, 0xE0, 0x00, 0xF0, 0x1F, 0xF8, 0x00, 0x3C, 0x07, 0x9E, 0x00, 0x0F,
    0x01, 0xE7, 0xC0, 0x03, 0xC0, 0x78, 0xF0, 0x01, 0xF0, 0x1E, 0x3E, 0x00,
    0x78, 0x0F, 0x87, 0x80, 0x1E, 0x03, 0xC1, 0xF0, 0x07, 0x80, 0xF0, 0x7C,
    0x01, 0xE0, 0x3C, 0x0F, 0x00, 0xF8, 0x1F, 0x03, 0xE0, 0x3C, 0x07, 0x80,
    0x78, 0x0F, 0x01, 0xE0, 0x1F, 0x03, 0xC0, 0x78, 0x07, 0xC1, 0xF0, 0x1E,
    0x00, 0xF8, 0x78, 0x0F, 0x80, 0x3E, 0x1E, 0x03, 0xC0, 0x07, 0x87, 0x80,
    0xF0, 0x01, 0xF1, 0xE0, 0x3C, 0x00, 0x3C, 0xF8, 0x0F, 0x00, 0x0F, 0xBC,
    0x07, 0xC0, 0x03, 0xEF, 0x01, 0xE0, 0x00, 0x7F, 0xC0, 0x78, 0x00, 0x1F,
    0xF0, 0x1E, 0x00, 0x03, 0xFC, 0x0F, 0x80, 0x00, 0xFE, 0x03, 0xC0, 0x00,
    0x1F, 0x80, 0xF0, 0x00, 0x07, 0xE0, 0x3C, 0x00, 0x01, 0xF8, 0x00, 0x00,
    0x00, 0xFF, 0x00, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFC, 0x00,
    0x0F, 0xFF, 0xFF, 0x80, 0x0F, 0xF0, 0x1F, 0xC0, 0x0F, 0xC0, 0x03, 0xF0,
    0x0F, 0x80, 0x00, 0xFC, 0x0F, 0x80, 0x00, 0x3E, 0x0F, 0x80, 0x00, 0x0F,
    0x07, 0x80, 0x00, 0x07, 0xC7, 0xC0, 0x00, 0x01, 0xE3, 0xC0, 0x00, 0x00,
    0xF3, 0xC0, 0x00, 0x00, 0x79, 0xE0, 0x00, 0x00, 0x3D, 0xE0, 0x00, 0x00,
    0x1E, 0xF0, 0x00, 0x00, 0x0F, 0x78, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00,
    0x07, 0xFC, 0x00, 0x00, 0x03, 0xDE, 0x00, 0x00, 0x01, 0xEF, 0x00, 0x00,
    0x00, 0xF7, 0x80, 0x00, 0x00, 0xFB, 0xC0, 0x00, 0x00, 0x79, 0xE0, 0x00,
    0x00, 0x3C, 0xF0, 0x00, 0x00, 0x3E, 0x78, 0x00, 0x00, 0x1E, 0x3E, 0x00,
    0x00, 0x1F, 0x0F, 0x00, 0x00, 0x1F, 0x07, 0xC0, 0x00, 0x1F, 0x03, 0xF0,
    0x00, 0x1F, 0x00, 0xFC, 0x00, 0x3F, 0x80, 0x3F, 0x80, 0x7F, 0x80, 0x1F,
    0xFF, 0xFF, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFE, 0x00, 0x00,
    0x0F, 0xF8, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xFE, 0x00,
    0x7F, 0xFF, 0xF8, 0x07, 0xFF, 0xFF, 0xE0, 0x3E, 0x00, 0x3F, 0x81, 0xE0,
    0x00, 0x7C, 0x0F, 0x00, 0x01, 0xE0, 0xF8, 0x00, 0x0F, 0x07, 0xC0, 0x00,
    0x78, 0x3C, 0x00, 0x03, 0xC1, 0xE0, 0x00, 0x1E, 0x0F, 0x00, 0x01, 0xE0,
    0xF8, 0x00, 0x0F, 0x07, 0xC0, 0x00, 0xF8, 0x3C, 0x00, 0x0F, 0x81, 0xE0,
    0x01, 0xF8, 0x0F, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFC, 0x07, 0xFF, 0xFF,
    0x80, 0x3F, 0xFF, 0xF0, 0x01, 0xE0, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00,
    0xF8, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x01, 0xE0,
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x07, 0x80, 0x00,
    0x00, 0x3C, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00,
    0xF0, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00,
    0x00, 0x03, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xFC, 0x00, 0x0F, 0xFF, 0xFF,
    0x80, 0x0F, 0xF0, 0x1F, 0xC0, 0x0F, 0xC0, 0x03, 0xF0, 0x0F, 0xC0, 0x00,
    0xFC, 0x0F, 0x80, 0x00, 0x3E, 0x0F, 0x80, 0x00, 0x0F, 0x07, 0x80, 0x00,
    0x07, 0xC7, 0xC0, 0x00, 0x01, 0xE3, 0xC0, 0x00, 0x00, 0xF3, 0xC0, 0x00,
    0x00, 0x79, 0xE0, 0x00, 0x00, 0x3D, 0xE0, 0x00, 0x00, 0x1E, 0xF0, 0x00,
    0x00, 0x0F, 0x78, 0x00, 0x00, 0x07, 0xB8, 0x00, 0x00, 0x03, 0xFC, 0x00,
    0x00, 0x03, 0xDE, 0x00, 0x00, 0x01, 0xEF, 0x00, 0x00, 0x00, 0xF7, 0x80,
    0x00, 0x00, 0x7B, 0xC0, 0x00, 0x00, 0x79, 0xE0, 0x00, 0x00, 0x3C, 0xF0,
    0x00, 0x00, 0x3C, 0x78, 0x00, 0x08, 0x3E, 0x3E, 0x00, 0x0E, 0x1E, 0x0F,
    0x00, 0x0F, 0x9F, 0x07, 0xC0, 0x07, 0xFF, 0x03, 0xF0, 0x01, 0xFF, 0x00,
    0xFC, 0x00, 0x7F, 0x00, 0x3F, 0x80, 0xFF, 0x80, 0x1F, 0xFF, 0xFF, 0xE0,
    0x03, 0xFF, 0xFF, 0xF8, 0x00, 0xFF, 0xFC, 0x7E, 0x00, 0x0F, 0xF0, 0x1F,
    0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFF,
    0xC0, 0x07, 0xFF, 0xFF, 0xC0, 0x1F, 0xFF, 0xFF, 0x80, 0xFF, 0xFF, 0xFE,
    0x03, 0xE0, 0x00, 0xFC, 0x0F, 0x00, 0x01, 0xF0, 0x3C, 0x00, 0x03, 0xC1,
    0xF0, 0x00, 0x0F, 0x07, 0xC0, 0x00, 0x3C, 0x1E, 0x00, 0x00, 0xF0, 0x78,
    0x00, 0x03, 0xC1, 0xE0, 0x00, 0x1E, 0x0F, 0x80, 0x00, 0x78, 0x3E, 0x00,
    0x03, 0xE0, 0xF0, 0x00, 0x1F, 0x03, 0xC0, 0x01, 0xF8, 0x0F, 0xFF, 0xFF,
    0xC0, 0x7F, 0xFF, 0xFE, 0x01, 0xFF, 0xFF, 0xF8, 0x07, 0xFF, 0xFF, 0xF0,
    0x1E, 0x00, 0x07, 0xE0, 0xF8, 0x00, 0x0F, 0x83, 0xE0, 0x00, 0x1E, 0x0F,
    0x00, 0x00, 0x78, 0x3C, 0x00, 0x01, 0xE0, 0xF0, 0x00, 0x07, 0x87, 0xC0,
    0x00, 0x1E, 0x1F, 0x00, 0x00, 0xF0, 0x78, 0x00, 0x03, 0xC1, 0xE0, 0x00,
    0x0F, 0x07, 0x80, 0x00, 0x3C, 0x3E, 0x00, 0x00, 0xF0, 0xF0, 0x00, 0x03,
    0xC3, 0xC0, 0x00, 0x0F, 0x00, 0x00, 0x03, 0xFE, 0x00, 0x00, 0xFF, 0xFC,
    0x00, 0x1F, 0xFF, 0xF8, 0x01, 0xFF, 0xFF, 0xC0, 0x1F, 0xC0, 0x7F, 0x01,
    0xF0, 0x00, 0xFC, 0x0F, 0x00, 0x03, 0xE0, 0xF0, 0x00, 0x0F, 0x07, 0x00,
    0x00, 0x78, 0x78, 0x00, 0x03, 0xC3, 0xC0, 0x00, 0x1E, 0x1E, 0x00, 0x00,
    0x00, 0xF8, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x00,
    0xFF, 0xE0, 0x00, 0x03, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0xF0, 0x00, 0x0F,
    0xFF, 0xC0, 0x00, 0x07, 0xFF, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x07,
    0xF0, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x3C, 0xF0, 0x00, 0x01, 0xE7,
    0x80, 0x00, 0x0F, 0x3C, 0x00, 0x00, 0x71, 0xE0, 0x00, 0x07, 0x8F, 0x00,
    0x00, 0x3C, 0x7C, 0x00, 0x03, 0xC1, 0xF0, 0x00, 0x7C, 0x0F, 0xE0, 0x1F,
    0xC0, 0x3F, 0xFF, 0xFC, 0x00, 0xFF, 0xFF, 0xC0, 0x03, 0xFF, 0xF8, 0x00,
    0x03, 0xFE, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xF7, 0xFF, 0xFF, 0xFF, 0x7F,
    0xFF, 0xFF, 0xF7, 0xFF, 0xFF, 0xFE, 0x00, 0x0F, 0x00, 0x00, 0x01, 0xF0,
    0x00, 0x00, 0x1F, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x1E, 0x00, 0x00,
    0x01, 0xE0, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x3C,
    0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x07, 0xC0, 0x00,
    0x00, 0x78, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x78, 0x00, 0x00, 0x0F,
    0x80, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0xF0, 0x00,
    0x00, 0x0F, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x01,
    0xE0, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x3E, 0x00,
    0x00, 0x03, 0xC0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00,
    0x7C, 0x00, 0x00, 0x07, 0x80, 0x00, 0x1E, 0x0F, 0x00, 0x00, 0x3C, 0x1E,
    0x00, 0x00, 0xF8, 0x7C, 0x00, 0x01, 0xF0, 0xF8, 0x00, 0x03, 0xC1, 0xE0,
    0x00, 0x07, 0x83, 0xC0, 0x00, 0x0F, 0x0F, 0x80, 0x00, 0x3E, 0x1F, 0x00,
    0x00, 0x7C, 0x3C, 0x00, 0x00, 0xF0, 0x78, 0x00, 0x01, 0xE0, 0xF0, 0x00,
    0x03, 0xC3, 0xE0, 0x00, 0x0F, 0x87, 0xC0, 0x00, 0x1F, 0x0F, 0x00, 0x00,
    0x3C, 0x1E, 0x00, 0x00, 0x78, 0x3C, 0x00, 0x01, 0xF0, 0xF8, 0x00, 0x03,
    0xE1, 0xF0, 0x00, 0x07, 0x83, 0xC0, 0x00, 0x0F, 0x07, 0x80, 0x00, 0x1E,
    0x1F, 0x00, 0x00, 0x7C, 0x3E, 0x00, 0x00, 0xF8, 0x78, 0x00, 0x01, 0xE0,
    0xF0, 0x00, 0x03, 0xC1, 0xE0, 0x00, 0x0F, 0x83, 0xC0, 0x00, 0x1E, 0x07,
    0x80, 0x00, 0x7C, 0x0F, 0x80, 0x01, 0xF0, 0x0F, 0x80, 0x07, 0xE0, 0x1F,
    0xC0, 0x7F, 0x80, 0x1F, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xF0, 0x00, 0x1F,
    0xFF, 0xC0, 0x00, 0x07, 0xFC, 0x00, 0x00, 0xF8, 0x00, 0x00, 0xFF, 0xC0,
    0x00, 0x0F, 0xBE, 0x00, 0x00, 0x79, 0xF0, 0x00, 0x07, 0xC7, 0x80, 0x00,
    0x3C, 0x3C, 0x00, 0x03, 0xE1, 0xE0, 0x00, 0x1E, 0x0F, 0x80, 0x01, 0xF0,
    0x7C, 0x00, 0x0F, 0x03, 0xE0, 0x00, 0xF8, 0x1F, 0x00, 0x0F, 0x80, 0x78,
    0x00, 0x78, 0x03, 0xC0, 0x07, 0xC0, 0x1E, 0x00, 0x3C, 0x00, 0xF0, 0x03,
    0xE0, 0x07, 0xC0, 0x1E, 0x00, 0x3E, 0x01, 0xF0, 0x01, 0xF0, 0x0F, 0x00,
    0x07, 0x80, 0xF0, 0x00, 0x3C, 0x07, 0x80, 0x01, 0xE0, 0x78, 0x00, 0x0F,
    0x07, 0xC0, 0x00, 0x7C, 0x3C, 0x00, 0x03, 0xE3, 0xE0, 0x00, 0x1F, 0x1E,
    0x00, 0x00, 0xF9, 0xF0, 0x00, 0x03, 0xCF, 0x00, 0x00, 0x1E, 0xF0, 0x00,
    0x00, 0xF7, 0x80, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x01,
    0xFC, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0xF0,
    0x00, 0x1F, 0x00, 0x03, 0xDE, 0x00, 0x07, 0xE0, 0x00, 0xFB, 0xC0, 0x00,
    0xFC, 0x00, 0x1E, 0x78, 0x00, 0x3F, 0x80, 0x07, 0xCF, 0x00, 0x07, 0xF0,
    0x00, 0xF9, 0xE0, 0x01, 0xFE, 0x00, 0x3E, 0x3C, 0x00, 0x7F, 0xC0, 0x07,
    0xC7, 0x80, 0x0F, 0x78, 0x01, 0xF0, 0xF0, 0x03, 0xEF, 0x00, 0x3E, 0x1E,
    0x00, 0x79, 0xE0, 0x0F, 0x83, 0xC0, 0x1F, 0x3C, 0x01, 0xF0, 0x78, 0x03,
    0xC7, 0x80, 0x3C, 0x0F, 0x00, 0xF8, 0xF0, 0x0F, 0x80, 0xE0, 0x1E, 0x1E,
    0x01, 0xE0, 0x1C, 0x07, 0xC1, 0xC0, 0x7C, 0x03, 0x80, 0xF0, 0x3C, 0x0F,
    0x00, 0x70, 0x3E, 0x07, 0x83, 0xE0, 0x0E, 0x07, 0x80, 0xF0, 0x78, 0x01,
    0xC1, 0xF0, 0x1E, 0x1F, 0x00, 0x3C, 0x3C, 0x03, 0xC3, 0xE0, 0x07, 0x8F,
    0x80, 0x78, 0x78, 0x00, 0xF1, 0xE0, 0x0F, 0x1F, 0x00, 0x1E, 0x7C, 0x01,
    0xE3, 0xC0, 0x03, 0xCF, 0x00, 0x3C, 0xF8, 0x00, 0x7B, 0xE0, 0x07, 0x9E,
    0x00, 0x0F, 0x78, 0x00, 0xF7, 0xC0, 0x01, 0xFF, 0x00, 0x1E, 0xF0, 0x00,
    0x3F, 0xC0, 0x03, 0xFE, 0x00, 0x07, 0xF8, 0x00, 0x7F, 0x80, 0x00, 0xFE,
    0x00, 0x07, 0xF0, 0x00, 0x1F, 0xC0, 0x00, 0xFC, 0x00, 0x03, 0xF0, 0x00,
    0x1F, 0x80, 0x00, 0x7E, 0x00, 0x03, 0xE0, 0x00, 0x0F, 0x80, 0x00, 0x7C,
    0x00, 0x00, 0x00, 0xFC, 0x00, 0x03, 0xF0, 0x07, 0xC0, 0x00, 0x3E, 0x00,
    0x7C, 0x00, 0x07, 0xC0, 0x03, 0xE0, 0x00, 0xF8, 0x00, 0x3E, 0x00, 0x1F,
    0x00, 0x01, 0xF0, 0x03, 0xE0, 0x00, 0x1F, 0x00, 0x7C, 0x00, 0x00, 0xF8,
    0x0F, 0x80, 0x00, 0x0F, 0x81, 0xF0, 0x00, 0x00, 0x7C, 0x1F, 0x00, 0x00,
    0x07, 0xC3, 0xE0, 0x00, 0x00, 0x7C, 0x7C, 0x00, 0x00, 0x03, 0xEF, 0x80,
    0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x00, 0x1F,
    0xC0, 0x00, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x00,
    0x03, 0xFC, 0x00, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x00, 0x0F, 0xBE, 0x00,
    0x00, 0x01, 0xF3, 0xE0, 0x00, 0x00, 0x3E, 0x1F, 0x00, 0x00, 0x03, 0xE1,
    0xF0, 0x00, 0x00, 0x7C, 0x0F, 0x80, 0x00, 0x0F, 0x80, 0xF8, 0x00, 0x01,
    0xF0, 0x07, 0xC0, 0x00, 0x3E, 0x00, 0x7C, 0x00, 0x07, 0xC0, 0x03, 0xE0,
    0x00, 0xF8, 0x00, 0x3E, 0x00, 0x1F, 0x00, 0x03, 0xF0, 0x03, 0xF0, 0x00,
    0x1F, 0x00, 0x7E, 0x00, 0x01, 0xF8, 0x0F, 0xC0, 0x00, 0x0F, 0x80, 0xF8,
    0x00, 0x00, 0x7D, 0xF0, 0x00, 0x03, 0xE7, 0xC0, 0x00, 0x1F, 0x1F, 0x80,
    0x00, 0xF8, 0x3E, 0x00, 0x03, 0xE0, 0xF8, 0x00, 0x1F, 0x01, 0xF0, 0x00,
    0xF8, 0x07, 0xC0, 0x07, 0xC0, 0x0F, 0x00, 0x3E, 0x00, 0x3E, 0x01, 0xF0,
    0x00, 0xF8, 0x07, 0xC0, 0x01, 0xF0, 0x3E, 0x00, 0x07, 0xC1, 0xF0, 0x00,
    0x0F, 0x0F, 0x80, 0x00, 0x3E, 0x7C, 0x00, 0x00, 0x79, 0xE0, 0x00, 0x01,
    0xFF, 0x80, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x00, 0x3F,
    0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x0F, 0x80,
    0x00, 0x00, 0x3C, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x03, 0xC0, 0x00,
    0x00, 0x1F, 0x00, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x00,
    0x07, 0x80, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x03,
    0xE0, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xC0,
    0x1F, 0xFF, 0xFF, 0xE0, 0x07, 0xFF, 0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xFE,
    0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x07,
    0xC0, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x00,
    0xF8, 0x00, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00,
    0x1F, 0x00, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00,
    0x03, 0xE0, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x00, 0xF8, 0x00, 0x00,
    0x00, 0x7C, 0x00, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x00,
    0x00, 0x0F, 0x80, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00, 0x03, 0xE0, 0x00,
    0x00, 0x01, 0xF0, 0x00, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x00, 0x7C, 0x00,
    0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x0F, 0x80,
    0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFE, 0x01, 0xFF, 0xFF, 0xFF, 0x80, 0x7F,
    0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x7F, 0xC0, 0x1F,
    0xF0, 0x07, 0xFC, 0x01, 0xFE, 0x00, 0xF0, 0x00, 0x3C, 0x00, 0x0F, 0x00,
    0x03, 0xC0, 0x01, 0xF0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x07, 0x80, 0x01,
    0xE0, 0x00, 0xF8, 0x00, 0x3C, 0x00, 0x0F, 0x00, 0x03, 0xC0, 0x00, 0xF0,
    0x00, 0x78, 0x00, 0x1E, 0x00, 0x07, 0x80, 0x01, 0xE0, 0x00, 0xF8, 0x00,
    0x3C, 0x00, 0x0F, 0x00, 0x03, 0xC0, 0x00, 0xF0, 0x00, 0x7C, 0x00, 0x1E,
    0x00, 0x07, 0x80, 0x01, 0xE0, 0x00, 0x78, 0x00, 0x3C, 0x00, 0x0F, 0x00,
    0x03, 0xC0, 0x00, 0xF0, 0x00, 0x7C, 0x00, 0x1E, 0x00, 0x07, 0x80, 0x01,
    0xE0, 0x00, 0x7F, 0xC0, 0x3F, 0xE0, 0x0F, 0xF8, 0x03, 0xFE, 0x00, 0xE3,
    0x8E, 0x38, 0xE1, 0x86, 0x18, 0x61, 0x87, 0x1C, 0x71, 0xC7, 0x0C, 0x30,
    0xC3, 0x0C, 0x38, 0xE3, 0x8E, 0x38, 0x61, 0x86, 0x18, 0x61, 0xC7, 0x1C,
    0x71, 0xC0, 0x00, 0x7F, 0xC0, 0x1F, 0xF0, 0x07, 0xFC, 0x03, 0xFE, 0x00,
    0x07, 0x80, 0x01, 0xE0, 0x00, 0x78, 0x00, 0x3E, 0x00, 0x0F, 0x00, 0x03,
    0xC0, 0x00, 0xF0, 0x00, 0x3C, 0x00, 0x1E, 0x00, 0x07, 0x80, 0x01, 0xE0,
    0x00, 0x78, 0x00, 0x3E, 0x00, 0x0F, 0x00, 0x03, 0xC0, 0x00, 0xF0, 0x00,
    0x3C, 0x00, 0x1F, 0x00, 0x07, 0x80, 0x01, 0xE0, 0x00, 0x78, 0x00, 0x1E,
    0x00, 0x0F, 0x00, 0x03, 0xC0, 0x00, 0xF0, 0x00, 0x3C, 0x00, 0x1F, 0x00,
    0x07, 0x80, 0x01, 0xE0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x0F, 0x80, 0x03,
    0xC0, 0x00, 0xF0, 0x00, 0x3C, 0x00, 0x0F, 0x00, 0x7F, 0x80, 0x3F, 0xE0,
    0x0F, 0xF8, 0x03, 0xFE, 0x00, 0x00, 0x3C, 0x00, 0x1E, 0x00, 0x1F, 0x00,
    0x1F, 0xC0, 0x0E, 0xE0, 0x0E, 0x70, 0x0F, 0x38, 0x07, 0x1C, 0x07, 0x0E,
    0x03, 0x83, 0x83, 0x81, 0xC3, 0xC0, 0xE1, 0xC0, 0x71, 0xC0, 0x39, 0xE0,
    0x0E, 0xE0, 0x07, 0xF0, 0x03, 0xF0, 0x01, 0xC0, 0x7F, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xC0, 0xF8, 0x78, 0x3C, 0x1C, 0x0E, 0x0E, 0x07, 0x00,
    0x1F, 0xE0, 0x01, 0xFF, 0xF0, 0x07, 0xFF, 0xF0, 0x1F, 0xFF, 0xF0, 0x7E,
    0x07, 0xE1, 0xF0, 0x07, 0xC3, 0xC0, 0x07, 0x80, 0x00, 0x0F, 0x00, 0x00,
    0x1E, 0x00, 0x00, 0x38, 0x00, 0x00, 0xF0, 0x00, 0x07, 0xE0, 0x0F, 0xFF,
    0xC0, 0xFF, 0xFF, 0x07, 0xFF, 0x9E, 0x1F, 0xC0, 0x3C, 0x7C, 0x00, 0x78,
    0xF0, 0x00, 0xF3, 0xC0, 0x03, 0xC7, 0x80, 0x07, 0x8F, 0x00, 0x1F, 0x1E,
    0x00, 0x7E, 0x3F, 0x07, 0xFC, 0x3F, 0xFF, 0x7E, 0x7F, 0xFC, 0xFC, 0x7F,
    0xF0, 0xF8, 0x3F, 0x00, 0xF0, 0x01, 0xE0, 0x00, 0x00, 0xF0, 0x00, 0x00,
    0xF8, 0x00, 0x00, 0x78, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x1E, 0x00, 0x00,
    0x0F, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x07, 0x83, 0xE0, 0x03, 0xC7, 0xFC,
    0x01, 0xEF, 0xFF, 0x00, 0xFF, 0xFF, 0xC0, 0xF7, 0x83, 0xF0, 0x7F, 0x00,
    0xF8, 0x3F, 0x00, 0x3E, 0x1F, 0x00, 0x0F, 0x1F, 0x80, 0x07, 0x8F, 0x80,
    0x03, 0xC7, 0x80, 0x01, 0xE3, 0xC0, 0x00, 0xF1, 0xE0, 0x00, 0x79, 0xF0,
    0x00, 0x3C, 0xF0, 0x00, 0x3C, 0x78, 0x00, 0x1E, 0x3C, 0x00, 0x0F, 0x1E,
    0x00, 0x0F, 0x9F, 0x00, 0x07, 0x8F, 0xC0, 0x07, 0xC7, 0xE0, 0x07, 0xC3,
    0xF8, 0x07, 0xC1, 0xFE, 0x0F, 0xC1, 0xEF, 0xFF, 0xE0, 0xF3, 0xFF, 0xC0,
    0x78, 0xFF, 0xC0, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x3F, 0x80, 0x03, 0xFF,
    0x80, 0x3F, 0xFF, 0x01, 0xFF, 0xFE, 0x0F, 0xE0, 0xF8, 0x7E, 0x01, 0xF1,
    0xF0, 0x03, 0xCF, 0x80, 0x0F, 0x3C, 0x00, 0x3D, 0xF0, 0x00, 0x07, 0x80,
    0x00, 0x1E, 0x00, 0x00, 0xF8, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x00, 0x00,
    0x3C, 0x00, 0x00, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x00, 0x0F, 0x3C,
    0x00, 0x3C, 0xF8, 0x01, 0xE1, 0xF0, 0x0F, 0x87, 0xE0, 0xFC, 0x0F, 0xFF,
    0xE0, 0x3F, 0xFF, 0x00, 0x7F, 0xF8, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x00,
    0x03, 0xE0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x01, 0xE0,
    0x00, 0x00, 0x3C, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x01, 0xE0, 0x00, 0x00,
    0x3C, 0x00, 0x3F, 0x07, 0x80, 0x1F, 0xF8, 0xF0, 0x0F, 0xFF, 0x3E, 0x03,
    0xFF, 0xF7, 0x80, 0xFC, 0x1F, 0xF0, 0x3F, 0x00, 0xFE, 0x07, 0xC0, 0x0F,
    0xC1, 0xF0, 0x01, 0xF0, 0x3C, 0x00, 0x3E, 0x0F, 0x80, 0x07, 0xC1, 0xE0,
    0x00, 0x78, 0x3C, 0x00, 0x1F, 0x0F, 0x80, 0x03, 0xC1, 0xE0, 0x00, 0x78,
    0x3C, 0x00, 0x0F, 0x07, 0x80, 0x01, 0xE0, 0xF0, 0x00, 0x7C, 0x1E, 0x00,
    0x0F, 0x03, 0xC0, 0x03, 0xE0, 0x78, 0x00, 0x7C, 0x0F, 0x80, 0x1F, 0x80,
    0xF8, 0x07, 0xF0, 0x1F, 0x83, 0xFC, 0x03, 0xFF, 0xFF, 0x80, 0x3F, 0xFE,
    0xF0, 0x03, 0xFF, 0x1E, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x00, 0x1F, 0x80,
    0x01, 0xFF, 0xC0, 0x07, 0xFF, 0xE0, 0x3F, 0xFF, 0xC0, 0xFE, 0x0F, 0xC1,
    0xF0, 0x07, 0xC7, 0xC0, 0x0F, 0x8F, 0x00, 0x0F, 0x3C, 0x00, 0x1E, 0x78,
    0x00, 0x3D, 0xE0, 0x00, 0x7B, 0xFF, 0xFF, 0xF7, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xBF, 0xFF, 0xFF, 0x78, 0x00, 0x00, 0xF0, 0x00, 0x01, 0xE0, 0x00,
    0x03, 0xC0, 0x00, 0x07, 0x80, 0x03, 0xCF, 0x80, 0x0F, 0x0F, 0x80, 0x3E,
    0x1F, 0x81, 0xF8, 0x1F, 0xFF, 0xE0, 0x1F, 0xFF, 0x80, 0x1F, 0xFC, 0x00,
    0x0F, 0xE0, 0x00, 0x00, 0x3E, 0x01, 0xFC, 0x07, 0xF8, 0x0F, 0xE0, 0x3E,
    0x00, 0x78, 0x00, 0xF0, 0x01, 0xE0, 0x07, 0xC0, 0x7F, 0xF0, 0xFF, 0xE3,
    0xFF, 0xC0, 0x78, 0x01, 0xE0, 0x03, 0xC0, 0x07, 0x80, 0x0F, 0x00, 0x3E,
    0x00, 0x78, 0x00, 0xF0, 0x01, 0xE0, 0x03, 0xC0, 0x0F, 0x80, 0x1E, 0x00,
    0x3C, 0x00, 0x78, 0x00, 0xF0, 0x03, 0xC0, 0x07, 0x80, 0x0F, 0x00, 0x1E,
    0x00, 0x3C, 0x00, 0xF0, 0x01, 0xE0, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0x07,
    0xFE, 0x3C, 0x01, 0xFF, 0xE7, 0x00, 0xFF, 0xFE, 0xE0, 0x1F, 0x83, 0xFC,
    0x07, 0xC0, 0x3F, 0x81, 0xF0, 0x03, 0xF0, 0x3C, 0x00, 0x7C, 0x0F, 0x00,
    0x0F, 0x81, 0xE0, 0x01, 0xF0, 0x78, 0x00, 0x3E, 0x0F, 0x00, 0x07, 0xC1,
    0xE0, 0x00, 0xF0, 0x38, 0x00, 0x1E, 0x0F, 0x00, 0x03, 0xC1, 0xE0, 0x00,
    0xF8, 0x3C, 0x00, 0x1F, 0x07, 0x80, 0x03, 0xC0, 0xF0, 0x00, 0xF8, 0x1E,
    0x00, 0x3F, 0x03, 0xE0, 0x07, 0xE0, 0x3E, 0x01, 0xF8, 0x07, 0xE0, 0xFF,
    0x00, 0x7F, 0xFD, 0xE0, 0x0F, 0xFF, 0x3C, 0x00, 0xFF, 0xCF, 0x00, 0x07,
    0xE1, 0xE0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x0F, 0x01, 0xE0, 0x03, 0xE0,
    0x3C, 0x00, 0xF8, 0x07, 0xE0, 0x7F, 0x00, 0x7F, 0xFF, 0xC0, 0x0F, 0xFF,
    0xF0, 0x00, 0x7F, 0xF8, 0x00, 0x03, 0xFC, 0x00, 0x00, 0x01, 0xE0, 0x00,
    0x03, 0xC0, 0x00, 0x0F, 0x80, 0x00, 0x1E, 0x00, 0x00, 0x3C, 0x00, 0x00,
    0x78, 0x00, 0x00, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x07, 0x83, 0xF0, 0x0F,
    0x1F, 0xF0, 0x1E, 0xFF, 0xF0, 0x3F, 0xFF, 0xE0, 0xFF, 0x87, 0xE1, 0xFC,
    0x07, 0xC3, 0xF0, 0x07, 0x87, 0xC0, 0x0F, 0x1F, 0x00, 0x1E, 0x3E, 0x00,
    0x3C, 0x78, 0x00, 0x78, 0xF0, 0x01, 0xE1, 0xE0, 0x03, 0xC7, 0xC0, 0x07,
    0x8F, 0x00, 0x0F, 0x1E, 0x00, 0x1E, 0x3C, 0x00, 0x78, 0x78, 0x00, 0xF1,
    0xE0, 0x01, 0xE3, 0xC0, 0x03, 0xC7, 0x80, 0x0F, 0x8F, 0x00, 0x1E, 0x1E,
    0x00, 0x3C, 0x78, 0x00, 0x78, 0xF0, 0x00, 0xF1, 0xE0, 0x03, 0xC0, 0x01,
    0xE0, 0x3C, 0x0F, 0x01, 0xE0, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xF0, 0x1E, 0x03, 0xC0, 0xF0, 0x1E, 0x03, 0xC0, 0x78, 0x0F, 0x03, 0xC0,
    0x78, 0x0F, 0x01, 0xE0, 0x3C, 0x0F, 0x01, 0xE0, 0x3C, 0x07, 0x80, 0xF0,
    0x3C, 0x07, 0x80, 0xF0, 0x1E, 0x03, 0xC0, 0xF0, 0x1E, 0x00, 0x00, 0x07,
    0x80, 0x01, 0xE0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x07, 0x80, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xC0, 0x01, 0xE0, 0x00,
    0x78, 0x00, 0x1E, 0x00, 0x07, 0x80, 0x03, 0xC0, 0x00, 0xF0, 0x00, 0x3C,
    0x00, 0x0F, 0x00, 0x03, 0xC0, 0x01, 0xE0, 0x00, 0x78, 0x00, 0x1E, 0x00,
    0x07, 0x80, 0x03, 0xE0, 0x00, 0xF0, 0x00, 0x3C, 0x00, 0x0F, 0x00, 0x03,
    0xC0, 0x01, 0xE0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x07, 0x80, 0x01, 0xE0,
    0x00, 0xF0, 0x00, 0x3C, 0x00, 0x0F, 0x00, 0x03, 0xC0, 0x01, 0xF0, 0x00,
    0x78, 0x00, 0x3E, 0x00, 0x7F, 0x80, 0x3F, 0xC0, 0x0F, 0xE0, 0x03, 0xE0,
    0x00, 0x01, 0xE0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x78,
    0x00, 0x00, 0x3C, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x0F,
    0x00, 0x00, 0x07, 0x80, 0x00, 0x03, 0xC0, 0x0F, 0x81, 0xE0, 0x0F, 0x80,
    0xF0, 0x0F, 0x80, 0xF0, 0x1F, 0x00, 0x78, 0x1F, 0x00, 0x3C, 0x1F, 0x00,
    0x1E, 0x1F, 0x00, 0x1F, 0x1F, 0x00, 0x0F, 0x1E, 0x00, 0x07, 0xBF, 0x80,
    0x03, 0xFF, 0xC0, 0x01, 0xFD, 0xE0, 0x01, 0xFC, 0xF8, 0x00, 0xFC, 0x3C,
    0x00, 0x7C, 0x1F, 0x00, 0x3C, 0x07, 0x80, 0x1E, 0x03, 0xC0, 0x1F, 0x01,
    0xF0, 0x0F, 0x00, 0x78, 0x07, 0x80, 0x3E, 0x03, 0xC0, 0x0F, 0x01, 0xE0,
    0x07, 0x81, 0xE0, 0x03, 0xE0, 0xF0, 0x00, 0xF0, 0x78, 0x00, 0x7C, 0x00,
    0x01, 0xE0, 0x3C, 0x0F, 0x01, 0xE0, 0x3C, 0x07, 0x80, 0xF0, 0x3C, 0x07,
    0x80, 0xF0, 0x1E, 0x03, 0xC0, 0xF0, 0x1E, 0x03, 0xC0, 0x78, 0x0F, 0x03,
    0xC0, 0x78, 0x0F, 0x01, 0xE0, 0x3C, 0x0F, 0x01, 0xE0, 0x3C, 0x07, 0x80,
    0xF0, 0x3C, 0x07, 0x80, 0xF0, 0x1E, 0x03, 0xC0, 0xF0, 0x1E, 0x00, 0x00,
    0x07, 0xE0, 0x1F, 0x80, 0xF9, 0xFF, 0x07, 0xFC, 0x0F, 0x3F, 0xF8, 0xFF,
    0xE0, 0xF7, 0xFF, 0x9F, 0xFF, 0x0F, 0xF0, 0xFF, 0xC3, 0xF0, 0xFC, 0x07,
    0xF8, 0x1F, 0x1F, 0x80, 0x3F, 0x00, 0xF1, 0xF0, 0x03, 0xE0, 0x0F, 0x1E,
    0x00, 0x3C, 0x00, 0xF1, 0xE0, 0x03, 0xC0, 0x0F, 0x1E, 0x00, 0x3C, 0x00,
    0xF1, 0xE0, 0x07, 0x80, 0x0F, 0x3C, 0x00, 0x78, 0x01, 0xF3, 0xC0, 0x07,
    0x80, 0x1E, 0x3C, 0x00, 0x78, 0x01, 0xE3, 0xC0, 0x0F, 0x80, 0x1E, 0x3C,
    0x00, 0xF0, 0x01, 0xE7, 0xC0, 0x0F, 0x00, 0x3C, 0x78, 0x00, 0xF0, 0x03,
    0xC7, 0x80, 0x0F, 0x00, 0x3C, 0x78, 0x01, 0xE0, 0x03, 0xC7, 0x80, 0x1E,
    0x00, 0x3C, 0xF8, 0x01, 0xE0, 0x07, 0x8F, 0x00, 0x1E, 0x00, 0x78, 0xF0,
    0x01, 0xE0, 0x07, 0x8F, 0x00, 0x3C, 0x00, 0x78, 0x00, 0x07, 0xE0, 0x1F,
    0x3F, 0xF0, 0x3C, 0xFF, 0xF0, 0x7B, 0xFF, 0xE0, 0xFF, 0x07, 0xE1, 0xF8,
    0x07, 0xC7, 0xE0, 0x07, 0x8F, 0x80, 0x0F, 0x1F, 0x00, 0x1E, 0x3C, 0x00,
    0x3C, 0x78, 0x00, 0x78, 0xF0, 0x01, 0xE3, 0xC0, 0x03, 0xC7, 0x80, 0x07,
    0x8F, 0x00, 0x0F, 0x1E, 0x00, 0x3E, 0x3C, 0x00, 0x78, 0xF0, 0x00, 0xF1,
    0xE0, 0x01, 0xE3, 0xC0, 0x03, 0xC7, 0x80, 0x0F, 0x8F, 0x00, 0x1E, 0x3E,
    0x00, 0x3C, 0x78, 0x00, 0x78, 0xF0, 0x00, 0xF1, 0xE0, 0x03, 0xC0, 0x00,
    0x1F, 0x80, 0x01, 0xFF, 0xC0, 0x0F, 0xFF, 0xE0, 0x3F, 0xFF, 0xC0, 0xFE,
    0x0F, 0xC1, 0xF0, 0x0F, 0x87, 0xC0, 0x0F, 0x8F, 0x00, 0x0F, 0x3C, 0x00,
    0x1E, 0x78, 0x00, 0x3D, 0xE0, 0x00, 0x7B, 0xC0, 0x00, 0xF7, 0x80, 0x01,
    0xFE, 0x00, 0x03, 0xFC, 0x00, 0x0F, 0x78, 0x00, 0x1E, 0xF0, 0x00, 0x3D,
    0xE0, 0x00, 0xF3, 0xC0, 0x01, 0xE7, 0x80, 0x07, 0x8F, 0x80, 0x1F, 0x0F,
    0x80, 0x7C, 0x1F, 0x83, 0xF8, 0x1F, 0xFF, 0xE0, 0x3F, 0xFF, 0x00, 0x1F,
    0xFC, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x3C, 0x7F, 0xE0,
    0x07, 0xBF, 0xFE, 0x01, 0xFF, 0xFF, 0xC0, 0x3D, 0xE0, 0xFC, 0x07, 0xF0,
    0x0F, 0x80, 0xFC, 0x00, 0xF8, 0x1F, 0x00, 0x0F, 0x07, 0xC0, 0x01, 0xE0,
    0xF8, 0x00, 0x3C, 0x1F, 0x00, 0x07, 0x83, 0xC0, 0x00, 0xF0, 0x78, 0x00,
    0x1E, 0x1F, 0x00, 0x03, 0xC3, 0xC0, 0x00, 0xF0, 0x78, 0x00, 0x1E, 0x0F,
    0x00, 0x03, 0xC3, 0xE0, 0x00, 0xF8, 0x7C, 0x00, 0x1E, 0x0F, 0x80, 0x07,
    0xC1, 0xF8, 0x01, 0xF0, 0x3F, 0x80, 0x7C, 0x0F, 0xF8, 0x3F, 0x81, 0xEF,
    0xFF, 0xE0, 0x3C, 0xFF, 0xF8, 0x07, 0x8F, 0xFC, 0x00, 0xF0, 0xFE, 0x00,
    0x3E, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x1E, 0x00,
    0x00, 0x03, 0xC0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x03,
    0xC0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x3F,
    0xF8, 0xF0, 0x1F, 0xFF, 0x3C, 0x0F, 0xFF, 0xDF, 0x07, 0xE0, 0xFF, 0x83,
    0xE0, 0x1F, 0xE1, 0xF0, 0x03, 0xF8, 0x78, 0x00, 0xFE, 0x3C, 0x00, 0x1F,
    0x8F, 0x00, 0x07, 0xC7, 0x80, 0x01, 0xF1, 0xE0, 0x00, 0x7C, 0x78, 0x00,
    0x1F, 0x3C, 0x00, 0x0F, 0x8F, 0x00, 0x03, 0xE3, 0xC0, 0x00, 0xF8, 0xF0,
    0x00, 0x3E, 0x3C, 0x00, 0x1F, 0x8F, 0x00, 0x0F, 0xC3, 0xC0, 0x03, 0xF0,
    0xF8, 0x01, 0xFC, 0x1F, 0x00, 0xFF, 0x07, 0xE0, 0xFF, 0xC0, 0xFF, 0xFD,
    0xE0, 0x1F, 0xFE, 0x78, 0x03, 0xFF, 0x3E, 0x00, 0x3F, 0x0F, 0x80, 0x00,
    0x03, 0xC0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x1F, 0x00,
    0x00, 0x07, 0xC0, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x3E,
    0x00, 0x00, 0x0F, 0x80, 0x00, 0x07, 0x87, 0xCF, 0xC3, 0xCF, 0xE1, 0xEF,
    0xE0, 0xFF, 0x80, 0x7F, 0x00, 0x7E, 0x00, 0x3F, 0x00, 0x1F, 0x00, 0x0F,
    0x00, 0x07, 0x80, 0x03, 0xC0, 0x03, 0xC0, 0x01, 0xE0, 0x00, 0xF0, 0x00,
    0x78, 0x00, 0x3C, 0x00, 0x3E, 0x00, 0x1E, 0x00, 0x0F, 0x00, 0x07, 0x80,
    0x03, 0xC0, 0x03, 0xE0, 0x01, 0xE0, 0x00, 0xF0, 0x00, 0x78, 0x00, 0x00,
    0x00, 0x3F, 0x80, 0x07, 0xFF, 0x00, 0xFF, 0xFC, 0x0F, 0xFF, 0xE0, 0xFC,
    0x1F, 0x87, 0x80, 0x3C, 0x7C, 0x01, 0xE3, 0xC0, 0x0F, 0x1E, 0x00, 0x00,
    0xF0, 0x00, 0x07, 0xC0, 0x00, 0x3F, 0xC0, 0x00, 0xFF, 0xE0, 0x03, 0xFF,
    0xC0, 0x07, 0xFF, 0x80, 0x07, 0xFE, 0x00, 0x03, 0xF0, 0x00, 0x07, 0xBC,
    0x00, 0x3D, 0xE0, 0x01, 0xEF, 0x00, 0x1F, 0x7C, 0x01, 0xF3, 0xF0, 0x1F,
    0x8F, 0xFF, 0xF8, 0x7F, 0xFF, 0x80, 0xFF, 0xF0, 0x01, 0xFE, 0x00, 0x03,
    0xC0, 0x1E, 0x01, 0xE0, 0x0F, 0x00, 0x78, 0x03, 0xC1, 0xFF, 0xEF, 0xFF,
    0x7F, 0xF0, 0x78, 0x03, 0xC0, 0x3C, 0x01, 0xE0, 0x0F, 0x00, 0x78, 0x03,
    0xC0, 0x3C, 0x01, 0xE0, 0x0F, 0x00, 0x78, 0x03, 0xC0, 0x3C, 0x01, 0xE0,
    0x0F, 0x00, 0x78, 0x07, 0xC0, 0x3C, 0x01, 0xE0, 0x0F, 0xF0, 0x7F, 0x81,
    0xF8, 0x07, 0xC0, 0x0F, 0x00, 0x0F, 0x0F, 0x00, 0x1E, 0x0F, 0x00, 0x1E,
    0x1F, 0x00, 0x1E, 0x1E, 0x00, 0x1E, 0x1E, 0x00, 0x1E, 0x1E, 0x00, 0x3C,
    0x1E, 0x00, 0x3C, 0x3E, 0x00, 0x3C, 0x3C, 0x00, 0x3C, 0x3C, 0x00, 0x3C,
    0x3C, 0x00, 0x7C, 0x3C, 0x00, 0x78, 0x78, 0x00, 0x78, 0x78, 0x00, 0x78,
    0x78, 0x00, 0x78, 0x78, 0x00, 0xF8, 0x78, 0x00, 0xF0, 0xF0, 0x01, 0xF0,
    0xF0, 0x03, 0xF0, 0xF0, 0x07, 0xF0, 0xF8, 0x1F, 0xF0, 0xFF, 0xFF, 0xE0,
    0x7F, 0xFD, 0xE0, 0x3F, 0xF1, 0xE0, 0x1F, 0xC0, 0x00, 0xF0, 0x00, 0x7F,
    0xC0, 0x01, 0xEF, 0x00, 0x0F, 0xBC, 0x00, 0x3C, 0x78, 0x01, 0xE1, 0xE0,
    0x07, 0x87, 0x80, 0x3C, 0x1E, 0x01, 0xF0, 0x78, 0x07, 0x81, 0xE0, 0x3E,
    0x07, 0x80, 0xF0, 0x1E, 0x07, 0x80, 0x38, 0x1E, 0x00, 0xF0, 0xF0, 0x03,
    0xC7, 0xC0, 0x0F, 0x1E, 0x00, 0x3C, 0xF0, 0x00, 0xF3, 0xC0, 0x03, 0xDE,
    0x00, 0x07, 0x78, 0x00, 0x1F, 0xC0, 0x00, 0x7E, 0x00, 0x01, 0xF8, 0x00,
    0x07, 0xC0, 0x00, 0x1F, 0x00, 0x00, 0xF0, 0x07, 0xC0, 0x0F, 0x78, 0x03,
    0xE0, 0x0F, 0xBC, 0x03, 0xF0, 0x07, 0x9E, 0x01, 0xF8, 0x03, 0xCF, 0x00,
    0xFC, 0x03, 0xC7, 0x80, 0xFE, 0x01, 0xE3, 0xC0, 0x77, 0x01, 0xE0, 0xE0,
    0x7B, 0x80, 0xF0, 0x70, 0x39, 0xC0, 0xF0, 0x38, 0x3C, 0xE0, 0x78, 0x1C,
    0x1E, 0x78, 0x78, 0x0F, 0x1E, 0x3C, 0x3C, 0x07, 0x8F, 0x1E, 0x3C, 0x03,
    0xC7, 0x0F, 0x1E, 0x01, 0xE7, 0x87, 0x9E, 0x00, 0xF3, 0x81, 0xCF, 0x00,
    0x7B, 0xC0, 0xEF, 0x00, 0x3D, 0xC0, 0x77, 0x80, 0x1F, 0xE0, 0x3F, 0x80,
    0x0F, 0xF0, 0x1F, 0xC0, 0x07, 0xF0, 0x0F, 0xC0, 0x01, 0xF8, 0x07, 0xE0,
    0x00, 0xF8, 0x03, 0xE0, 0x00, 0x7C, 0x01, 0xF0, 0x00, 0x3C, 0x00, 0xF0,
    0x00, 0x00, 0x03, 0xC0, 0x07, 0xC0, 0xF8, 0x01, 0xE0, 0x1E, 0x00, 0xF0,
    0x07, 0x80, 0x78, 0x00, 0xF0, 0x3C, 0x00, 0x3C, 0x1F, 0x00, 0x0F, 0x8F,
    0x80, 0x01, 0xE7, 0xC0, 0x00, 0x7D, 0xE0, 0x00, 0x0F, 0xF0, 0x00, 0x03,
    0xF8, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x0F, 0xC0, 0x00,
    0x07, 0xF0, 0x00, 0x03, 0xFE, 0x00, 0x01, 0xF7, 0x80, 0x00, 0xF9, 0xF0,
    0x00, 0x3C, 0x3C, 0x00, 0x1E, 0x0F, 0x80, 0x0F, 0x01, 0xE0, 0x07, 0x80,
    0x7C, 0x03, 0xE0, 0x0F, 0x01, 0xF0, 0x03, 0xE0, 0xF8, 0x00, 0x78, 0x00,
    0x03, 0xC0, 0x01, 0xE0, 0x78, 0x00, 0x78, 0x0F, 0x00, 0x0F, 0x01, 0xE0,
    0x03, 0xC0, 0x3C, 0x00, 0x78, 0x07, 0xC0, 0x1E, 0x00, 0x78, 0x07, 0xC0,
    0x0F, 0x00, 0xF0, 0x01, 0xE0, 0x3C, 0x00, 0x3C, 0x07, 0x80, 0x07, 0x81,
    0xE0, 0x00, 0xF0, 0x3C, 0x00, 0x1E, 0x0F, 0x00, 0x03, 0xC1, 0xC0, 0x00,
    0x3C, 0x78, 0x00, 0x07, 0x9E, 0x00, 0x00, 0xF3, 0xC0, 0x00, 0x1E, 0xF0,
    0x00, 0x03, 0xDE, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x0F, 0xE0, 0x00, 0x01,
    0xFC, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x78, 0x00,
    0x00, 0x0F, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x1E,
    0x00, 0x00, 0x07, 0x80, 0x00, 0x01, 0xF0, 0x00, 0x03, 0xFC, 0x00, 0x00,
    0xFF, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x00, 0x01,
    0xFF, 0xFF, 0x81, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF, 0xE0,
    0x00, 0x01, 0xF0, 0x00, 0x01, 0xF0, 0x00, 0x01, 0xF0, 0x00, 0x01, 0xF0,
    0x00, 0x01, 0xF0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0, 0x00, 0x01, 0xE0,
    0x00, 0x01, 0xE0, 0x00, 0x03, 0xE0, 0x00, 0x03, 0xE0, 0x00, 0x03, 0xE0,
    0x00, 0x03, 0xE0, 0x00, 0x03, 0xE0, 0x00, 0x03, 0xE0, 0x00, 0x03, 0xE0,
    0x00, 0x03, 0xC0, 0x00, 0x03, 0xFF, 0xFF, 0xC1, 0xFF, 0xFF, 0xE0, 0xFF,
    0xFF, 0xF0, 0x7F, 0xFF, 0xF8, 0x00, 0x00, 0x1F, 0x00, 0x7E, 0x00, 0xFE,
    0x00, 0xF0, 0x01, 0xE0, 0x01, 0xE0, 0x01, 0xE0, 0x03, 0xC0, 0x03, 0xC0,
    0x03, 0xC0, 0x03, 0xC0, 0x07, 0xC0, 0x07, 0x80, 0x07, 0x80, 0x07, 0x80,
    0x07, 0x80, 0x0F, 0x00, 0x0F, 0x00, 0x1E, 0x00, 0x3C, 0x00, 0xF8, 0x00,
    0xE0, 0x00, 0xF0, 0x00, 0x78, 0x00, 0x38, 0x00, 0x38, 0x00, 0x38, 0x00,
    0x38, 0x00, 0x38, 0x00, 0x3C, 0x00, 0x7C, 0x00, 0x78, 0x00, 0x78, 0x00,
    0x78, 0x00, 0x78, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0xE0, 0x00,
    0xE0, 0x00, 0xF0, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0x7C, 0x00, 0x00, 0x70,
    0x07, 0x00, 0x60, 0x06, 0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x0C, 0x01, 0xC0,
    0x1C, 0x01, 0xC0, 0x1C, 0x01, 0x80, 0x38, 0x03, 0x80, 0x38, 0x03, 0x00,
    0x30, 0x07, 0x00, 0x70, 0x07, 0x00, 0x60, 0x0E, 0x00, 0xE0, 0x0E, 0x00,
    0xE0, 0x0C, 0x01, 0xC0, 0x1C, 0x01, 0xC0, 0x1C, 0x01, 0x80, 0x38, 0x03,
    0x80, 0x38, 0x03, 0x00, 0x70, 0x07, 0x00, 0x70, 0x07, 0x00, 0x60, 0x0E,
    0x00, 0xE0, 0x06, 0x00, 0x00, 0x3E, 0x00, 0x3E, 0x00, 0x3F, 0x00, 0x0F,
    0x00, 0x07, 0x00, 0x07, 0x00, 0x0F, 0x00, 0x0F, 0x00, 0x0F, 0x00, 0x1E,
    0x00, 0x1E, 0x00, 0x1E, 0x00, 0x1E, 0x00, 0x3E, 0x00, 0x3C, 0x00, 0x1C,
    0x00, 0x1C, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x1E, 0x00, 0x0F,
    0x00, 0x07, 0x00, 0x1F, 0x00, 0x3C, 0x00, 0x78, 0x00, 0xF0, 0x00, 0xF0,
    0x01, 0xE0, 0x01, 0xE0, 0x01, 0xE0, 0x01, 0xE0, 0x03, 0xE0, 0x03, 0xC0,
    0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x07, 0x80, 0x07, 0x80, 0x07, 0x80,
    0x0F, 0x00, 0x7F, 0x00, 0x7E, 0x00, 0xF8, 0x00, 0x0F, 0x00, 0x01, 0xFE,
    0x00, 0xCF, 0xFC, 0x0E, 0xE3, 0xF0, 0xE6, 0x07, 0xFF, 0x60, 0x0F, 0xF0,
    0x00, 0x1E, 0x00};

const GFXglyph FreeSansOblique24pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 13, 0, 1},         // 0x20 ' '
    {0, 11, 34, 13, 6, -33},     // 0x21 '!'
    {47, 13, 12, 17, 8, -32},    // 0x22 '"'
    {67, 28, 34, 26, 3, -32},    // 0x23 '#'
    {186, 26, 42, 26, 3, -35},   // 0x24 '$'
    {323, 36, 34, 42, 6, -32},   // 0x25 '%'
    {476, 26, 34, 31, 4, -32},   // 0x26 '&'
    {587, 5, 12, 9, 8, -32},     // 0x27 '''
    {595, 15, 44, 16, 5, -33},   // 0x28 '('
    {678, 15, 44, 16, 1, -33},   // 0x29 ')'
    {761, 14, 13, 18, 8, -33},   // 0x2A '*'
    {784, 23, 22, 27, 5, -20},   // 0x2B '+'
    {848, 7, 12, 13, 3, -4},     // 0x2C ','
    {859, 12, 4, 16, 5, -14},    // 0x2D '-'
    {865, 6, 5, 13, 4, -4},      // 0x2E '.'
    {869, 21, 35, 13, -1, -33},  // 0x2F '/'
    {961, 23, 34, 26, 5, -32},   // 0x30 '0'
    {1059, 13, 33, 26, 10, -32}, // 0x31 '1'
    {1113, 27, 33, 26, 2, -32},  // 0x32 '2'
    {1225, 25, 34, 26, 3, -32},  // 0x33 '3'
    {1332, 24, 33, 26, 3, -32},  // 0x34 '4'
    {1431, 27, 34, 26, 3, -32},  // 0x35 '5'
    {1546, 24, 34, 26, 4, -32},  // 0x36 '6'
    {1648, 26, 33, 26, 6, -32},  // 0x37 '7'
    {1756, 25, 34, 26, 3, -32},  // 0x38 '8'
    {1863, 24, 34, 26, 4, -32},  // 0x39 '9'
    {1965, 10, 25, 13, 5, -24},  // 0x3A ':'
    {1997, 11, 32, 13, 4, -24},  // 0x3B ';'
    {2041, 26, 23, 27, 4, -22},  // 0x3C '<'
    {2116, 26, 12, 27, 3, -16},  // 0x3D '='
    {2155, 26, 23, 27, 2, -21},  // 0x3E '>'
    {2230, 20, 35, 26, 9, -34},  // 0x3F '?'
    {2318, 45, 42, 48, 4, -34},  // 0x40 '@'
    {2555, 30, 34, 31, 1, -33},  // 0x41 'A'
    {2683, 29, 34, 31, 4, -33},  // 0x42 'B'
    {2807, 30, 36, 33, 5, -34},  // 0x43 'C'
    {2942, 31, 34, 33, 4, -33},  // 0x44 'D'
    {3074, 31, 34, 31, 4, -33},  // 0x45 'E'
    {3206, 30, 34, 28, 4, -33},  // 0x46 'F'
    {3334, 33, 36, 37, 5, -34},  // 0x47 'G'
    {3483, 33, 34, 34, 4, -33},  // 0x48 'H'
    {3624, 11, 34, 13, 5, -33},  // 0x49 'I'
    {3671, 25, 35, 24, 2, -33},  // 0x4A 'J'
    {3781, 34, 34, 31, 4, -33},  // 0x4B 'K'
    {3926, 22, 34, 26, 4, -33},  // 0x4C 'L'
    {4020, 39, 34, 40, 4, -33},  // 0x4D 'M'
    {4186, 34, 34, 34, 4, -33},  // 0x4E 'N'
    {4331, 33, 36, 36, 5, -34},  // 0x4F 'O'
    {4480, 29, 34, 30, 4, -33},  // 0x50 'P'
    {4604, 33, 38, 36, 5, -34},  // 0x51 'Q'
    {4761, 30, 34, 33, 4, -33},  // 0x52 'R'
    {4889, 29, 36, 31, 4, -34},  // 0x53 'S'
    {5020, 28, 34, 29, 7, -33},  // 0x54 'T'
    {5139, 31, 35, 34, 6, -33},  // 0x55 'U'
    {5275, 29, 34, 30, 8, -33},  // 0x56 'V'
    {5399, 43, 34, 44, 8, -33},  // 0x57 'W'
    {5582, 36, 34, 31, 1, -33},  // 0x58 'X'
    {5735, 30, 34, 32, 8, -33},  // 0x59 'Y'
    {5863, 34, 34, 29, 1, -33},  // 0x5A 'Z'
    {6008, 18, 44, 13, 1, -33},  // 0x5B '['
    {6107, 6, 35, 13, 7, -33},   // 0x5C '\'
    {6134, 18, 44, 13, -1, -33}, // 0x5D ']'
    {6233, 17, 18, 22, 6, -32},  // 0x5E '^'
    {6272, 29, 2, 26, -3, 7},    // 0x5F '_'
    {6280, 8, 7, 16, 8, -34},    // 0x60 '`'
    {6287, 23, 27, 26, 3, -25},  // 0x61 'a'
    {6365, 25, 35, 26, 3, -33},  // 0x62 'b'
    {6475, 22, 27, 24, 4, -25},  // 0x63 'c'
    {6550, 27, 35, 26, 4, -33},  // 0x64 'd'
    {6669, 23, 27, 26, 4, -25},  // 0x65 'e'
    {6747, 15, 34, 12, 3, -33},  // 0x66 'f'
    {6811, 27, 36, 26, 2, -25},  // 0x67 'g'
    {6933, 23, 34, 25, 3, -33},  // 0x68 'h'
    {7031, 11, 34, 10, 3, -33},  // 0x69 'i'
    {7078, 18, 44, 11, -2, -33}, // 0x6A 'j'
    {7177, 25, 34, 24, 3, -33},  // 0x6B 'k'
    {7284, 11, 34, 10, 3, -33},  // 0x6C 'l'
    {7331, 36, 26, 38, 3, -25},  // 0x6D 'm'
    {7448, 23, 26, 25, 3, -25},  // 0x6E 'n'
    {7523, 23, 27, 26, 4, -25},  // 0x6F 'o'
    {7601, 27, 36, 26, 1, -25},  // 0x70 'p'
    {7723, 26, 36, 26, 3, -25},  // 0x71 'q'
    {7840, 17, 26, 15, 3, -25},  // 0x72 'r'
    {7896, 21, 27, 24, 3, -25},  // 0x73 's'
    {7967, 13, 32, 12, 4, -30},  // 0x74 't'
    {8019, 24, 26, 25, 4, -24},  // 0x75 'u'
    {8097, 22, 25, 23, 6, -24},  // 0x76 'v'
    {8166, 33, 25, 34, 6, -24},  // 0x77 'w'
    {8270, 26, 25, 23, 1, -24},  // 0x78 'x'
    {8352, 27, 35, 23, 0, -24},  // 0x79 'y'
    {8471, 25, 25, 23, 1, -24},  // 0x7A 'z'
    {8550, 16, 44, 16, 5, -33},  // 0x7B '{'
    {8638, 12, 44, 12, 3, -33},  // 0x7C '|'
    {8704, 16, 44, 16, -1, -33}, // 0x7D '}'
    {8792, 21, 7, 27, 6, -19}};  // 0x7E '~'

const GFXfont FreeSansOblique24pt7b PROGMEM = {
    (uint8_t *)FreeSansOblique24pt7bBitmaps,
    (GFXglyph *)FreeSansOblique24pt7bGlyphs, 0x20, 0x7E, 56};

// Approx. 9483 bytes

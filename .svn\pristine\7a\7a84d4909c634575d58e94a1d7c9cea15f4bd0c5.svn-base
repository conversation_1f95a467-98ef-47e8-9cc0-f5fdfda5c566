#include "spi_config.h"
#include "driver/spi_master.h"
#include "esp_err.h"
#include "esp_log.h"

// void spi_master_init(spi_host_device_t host_id, int dma_chan, uint32_t max_tran_size, gpio_num_t miso_io_num, gpio_num_t mosi_io_num, gpio_num_t clk_io_num)
// {
//     esp_err_t ret;
//     // 配置 MISO、MOSI、CLK、CS 的引脚，和DMA最大传输字节数
//     spi_bus_config_t buscfg = {};
//     buscfg.miso_io_num = miso_io_num;
//     buscfg.mosi_io_num = mosi_io_num;
//     buscfg.sclk_io_num = clk_io_num;
//     buscfg.quadwp_io_num = -1;
//     buscfg.quadhd_io_num = -1;
//     buscfg.max_transfer_sz = max_tran_size;
//     buscfg.flags = SPICOMMON_BUSFLAG_MASTER | SPICOMMON_BUSFLAG_SCLK | SPICOMMON_BUSFLAG_MISO | SPICOMMON_BUSFLAG_MOSI;

//     // 初始化SPI总线 - ESP32-S3只支持自动分配DMA通道
//     ret = spi_bus_initialize(host_id, &buscfg, SPI_DMA_CH_AUTO);
//     if (ret != ESP_OK) {
//         ESP_LOGE("SPI", "Failed to initialize bus: %s", esp_err_to_name(ret));
//         return;
//     }
//     ESP_LOGI("SPI", "SPI%d initialized successfully", host_id + 1);
// }
void spi_master_init(spi_host_device_t host_id, int dma_chan, uint32_t max_tran_size, gpio_num_t miso_io_num, gpio_num_t mosi_io_num, gpio_num_t clk_io_num)
{
    esp_err_t ret;

    spi_bus_config_t buscfg = {};
    buscfg.miso_io_num = miso_io_num;
    buscfg.mosi_io_num = mosi_io_num;
    buscfg.sclk_io_num = clk_io_num;
    buscfg.quadwp_io_num = -1;
    buscfg.quadhd_io_num = -1;
    buscfg.max_transfer_sz = max_tran_size;
    buscfg.flags = SPICOMMON_BUSFLAG_MASTER;

    // 避免重复初始化
    static bool spi_initialized[3] = {false, false, false};
    if (spi_initialized[host_id]) {
        ESP_LOGW("SPI", "SPI%d already initialized, skipping", host_id + 1);
        return;
    }

    ret = spi_bus_initialize(host_id, &buscfg, dma_chan);
    if (ret != ESP_OK) {
        ESP_LOGE("SPI", "Failed to initialize SPI%d: %s", host_id + 1, esp_err_to_name(ret));
        return;
    }

    spi_initialized[host_id] = true;
    ESP_LOGI("SPI", "SPI%d initialized successfully", host_id + 1);
}

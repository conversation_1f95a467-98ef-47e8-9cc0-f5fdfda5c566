/**
 * @file device_identification_test.cpp
 * @brief 设备唯一标识测试示例
 * @details 演示如何使用基于MAC地址的设备唯一标识功能
 * <AUTHOR>
 * @version 1.0
 * @date 2024
 */

#include <Arduino.h>
#include <WiFi.h>
#include "ble_manager.h"

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("=== ESP32设备唯一标识测试 ===");
    Serial.println();
    
    // 初始化WiFi以获取MAC地址
    WiFi.mode(WIFI_STA);
    
    // 获取原始MAC地址
    uint8_t mac[6];
    esp_read_mac(mac, ESP_MAC_WIFI_STA);
    Serial.print("原始MAC地址: ");
    for(int i = 0; i < 6; i++) {
        if(i > 0) Serial.print(":");
        Serial.printf("%02X", mac[i]);
    }
    Serial.println();
    Serial.println();
    
    // 测试设备标识功能
    Serial.println("=== 设备唯一标识信息 ===");
    
    // 1. 获取设备后缀
    String suffix = DeviceIdentifier::getDeviceSuffix();
    Serial.println("设备后缀: " + suffix);
    
    // 2. 获取唯一蓝牙设备名
    String uniqueBLEName = DeviceIdentifier::getUniqueDeviceName("ESP32-Stepper");
    Serial.println("唯一蓝牙设备名: " + uniqueBLEName);
    
    // 3. 获取唯一WiFi主机名
    String uniqueHostname = DeviceIdentifier::getUniqueHostname("ESP32-Stepper");
    Serial.println("唯一WiFi主机名: " + uniqueHostname);
    
    // 4. 获取唯一版本响应
    String uniqueVersion = DeviceIdentifier::getUniqueVersionResponse();
    Serial.println("唯一版本响应: " + uniqueVersion);
    
    Serial.println();
    Serial.println("=== 实际应用示例 ===");
    
    // 设置WiFi主机名
    WiFi.setHostname(uniqueHostname.c_str());
    Serial.println("WiFi主机名已设置为: " + uniqueHostname);
    
    // 模拟Skywatcher协议版本查询
    Serial.println();
    Serial.println("模拟Skywatcher协议版本查询:");
    Serial.println("发送命令: :e1");
    Serial.print("设备响应: ");
    Serial.print(uniqueVersion);
    
    Serial.println();
    Serial.println("=== 多设备区分示例 ===");
    Serial.println("如果您有多个相同的ESP32设备，它们将显示为:");
    Serial.println("- 蓝牙设备: ESP32-Stepper-" + suffix + " (每个设备后缀不同)");
    Serial.println("- WiFi主机名: ESP32-Stepper-" + suffix + " (每个设备后缀不同)");
    Serial.println("- 版本响应: " + uniqueVersion + " (每个设备版本号不同)");
    
    Serial.println();
    Serial.println("测试完成！");
}

void loop() {
    // 每10秒显示一次当前设备信息
    static unsigned long lastTime = 0;
    if (millis() - lastTime > 10000) {
        lastTime = millis();
        
        Serial.println();
        Serial.println("=== 当前设备信息 ===");
        Serial.println("设备后缀: " + DeviceIdentifier::getDeviceSuffix());
        Serial.println("蓝牙名称: " + DeviceIdentifier::getUniqueDeviceName("ESP32-Stepper"));
        Serial.println("WiFi主机名: " + DeviceIdentifier::getUniqueHostname("ESP32-Stepper"));
        Serial.println("版本响应: " + DeviceIdentifier::getUniqueVersionResponse());
    }
    
    delay(1000);
}

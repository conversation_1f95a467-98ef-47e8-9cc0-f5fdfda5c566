#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSerifBoldItalic12pt7bBitmaps[] PROGMEM = {
    0x07, 0x07, 0x07, 0x0F, 0x0E, 0x0E, 0x0C, 0x0C, 0x08, 0x18, 0x10, 0x00,
    0x00, 0x60, 0xF0, 0xF0, 0x60, 0x61, 0xF1, 0xF8, 0xF8, 0x6C, 0x34, 0x12,
    0x08, 0x01, 0x8C, 0x06, 0x60, 0x31, 0x80, 0xCC, 0x06, 0x30, 0xFF, 0xF0,
    0xC6, 0x03, 0x18, 0x0C, 0xC0, 0x63, 0x0F, 0xFF, 0x0C, 0x60, 0x33, 0x01,
    0x8C, 0x06, 0x30, 0x19, 0x80, 0x00, 0x80, 0x08, 0x07, 0xC1, 0x96, 0x31,
    0x33, 0x13, 0x3A, 0x23, 0xE0, 0x1E, 0x01, 0xF0, 0x07, 0x80, 0x7C, 0x05,
    0xC4, 0xCC, 0x48, 0xCC, 0x8C, 0xF8, 0x83, 0x30, 0x1E, 0x01, 0x00, 0x00,
    0x02, 0x07, 0x83, 0x03, 0x9F, 0x81, 0xC4, 0x20, 0x71, 0x10, 0x3C, 0x44,
    0x0E, 0x22, 0x03, 0x88, 0x80, 0xE4, 0x40, 0x1E, 0x31, 0xE0, 0x08, 0xE4,
    0x06, 0x71, 0x01, 0x3C, 0x40, 0x8E, 0x10, 0x23, 0x88, 0x10, 0xE2, 0x04,
    0x39, 0x02, 0x07, 0x80, 0x00, 0xF0, 0x01, 0x98, 0x03, 0x98, 0x03, 0x98,
    0x03, 0xB0, 0x03, 0xE0, 0x03, 0x80, 0x0F, 0x9F, 0x19, 0xCE, 0x31, 0xCC,
    0x61, 0xC8, 0xE1, 0xC8, 0xE0, 0xF0, 0xE0, 0xE0, 0xF0, 0x70, 0x78, 0x79,
    0x3F, 0xBE, 0x7F, 0xED, 0x20, 0x02, 0x08, 0x20, 0xC3, 0x0E, 0x18, 0x30,
    0xE1, 0x83, 0x06, 0x0C, 0x18, 0x30, 0x20, 0x40, 0x80, 0x81, 0x01, 0x00,
    0x10, 0x10, 0x20, 0x20, 0x40, 0xC1, 0x83, 0x06, 0x0C, 0x18, 0x70, 0xE1,
    0x83, 0x0C, 0x18, 0x61, 0x86, 0x00, 0x00, 0x0C, 0x33, 0x6C, 0x9B, 0xAE,
    0x1C, 0x3F, 0xEC, 0x9B, 0x36, 0x0C, 0x02, 0x00, 0x06, 0x00, 0x60, 0x06,
    0x00, 0x60, 0x06, 0x0F, 0xFF, 0xFF, 0xF0, 0x60, 0x06, 0x00, 0x60, 0x06,
    0x00, 0x60, 0x31, 0xCE, 0x31, 0x08, 0x98, 0xFF, 0xFF, 0xC0, 0x6F, 0xF6,
    0x01, 0x80, 0x60, 0x30, 0x0C, 0x07, 0x01, 0x80, 0xE0, 0x30, 0x1C, 0x06,
    0x01, 0x80, 0xC0, 0x30, 0x18, 0x06, 0x03, 0x00, 0x03, 0x81, 0xC8, 0x71,
    0x1C, 0x33, 0x86, 0xE1, 0xDC, 0x3B, 0x87, 0xE0, 0xFC, 0x3B, 0x87, 0x70,
    0xEC, 0x39, 0x87, 0x31, 0xC2, 0x30, 0x3C, 0x00, 0x01, 0xC3, 0xF0, 0x38,
    0x0E, 0x03, 0x81, 0xE0, 0x70, 0x1C, 0x0F, 0x03, 0x80, 0xE0, 0x38, 0x1E,
    0x07, 0x01, 0xC0, 0xF0, 0xFF, 0x80, 0x07, 0x81, 0xF8, 0x47, 0x90, 0x70,
    0x0E, 0x01, 0xC0, 0x30, 0x0E, 0x01, 0x80, 0x60, 0x18, 0x06, 0x01, 0x80,
    0x40, 0x8F, 0xF3, 0xFC, 0xFF, 0x80, 0x07, 0xC3, 0x3C, 0x03, 0x80, 0x70,
    0x0C, 0x03, 0x81, 0xC0, 0xFC, 0x07, 0xC0, 0x78, 0x07, 0x00, 0xE0, 0x1C,
    0x03, 0x30, 0xE7, 0x10, 0x7C, 0x00, 0x00, 0x10, 0x01, 0x80, 0x3C, 0x03,
    0xE0, 0x2E, 0x02, 0x70, 0x23, 0x82, 0x38, 0x21, 0xC2, 0x0E, 0x1F, 0xF9,
    0xFF, 0xC0, 0x38, 0x01, 0xC0, 0x1C, 0x00, 0xE0, 0x07, 0xF0, 0x7E, 0x0F,
    0xE0, 0x80, 0x08, 0x01, 0xE0, 0x1F, 0x83, 0xF8, 0x03, 0xC0, 0x1C, 0x00,
    0xC0, 0x0C, 0x00, 0xC0, 0x08, 0x61, 0x8F, 0x30, 0x7C, 0x00, 0x00, 0x60,
    0x78, 0x1C, 0x0F, 0x01, 0xC0, 0x70, 0x1F, 0xC3, 0x8C, 0xE1, 0xDC, 0x3B,
    0x87, 0x61, 0xEC, 0x3D, 0x87, 0x31, 0xE2, 0x38, 0x3C, 0x00, 0x3F, 0xEF,
    0xF9, 0xFF, 0x60, 0xC8, 0x18, 0x06, 0x00, 0x80, 0x30, 0x0C, 0x01, 0x80,
    0x60, 0x1C, 0x03, 0x00, 0xC0, 0x18, 0x06, 0x00, 0x03, 0x81, 0x88, 0x61,
    0x8C, 0x31, 0x86, 0x38, 0xC7, 0xB0, 0x78, 0x0F, 0x86, 0x71, 0x87, 0x60,
    0x6C, 0x0D, 0x81, 0xB0, 0x63, 0x18, 0x3E, 0x00, 0x07, 0x81, 0xC8, 0x71,
    0x8E, 0x33, 0xC6, 0x70, 0xCE, 0x39, 0xC7, 0x38, 0xE3, 0x38, 0x3F, 0x01,
    0xC0, 0x38, 0x0E, 0x03, 0x81, 0xC0, 0xE0, 0x00, 0x0C, 0x3C, 0x78, 0x60,
    0x00, 0x00, 0x00, 0x61, 0xE3, 0xC3, 0x00, 0x0E, 0x0F, 0x0F, 0x0E, 0x00,
    0x00, 0x00, 0x00, 0x38, 0x38, 0x38, 0x18, 0x10, 0x20, 0x40, 0x00, 0x10,
    0x07, 0x01, 0xF0, 0x7C, 0x3F, 0x0F, 0x80, 0xE0, 0x0F, 0x80, 0x3E, 0x00,
    0xF8, 0x03, 0xE0, 0x07, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00,
    0xFF, 0xFF, 0xFF, 0x80, 0x07, 0x00, 0x3F, 0x00, 0x3E, 0x00, 0x7C, 0x00,
    0xF8, 0x01, 0xE0, 0x1F, 0x07, 0xE0, 0xF8, 0x1F, 0x01, 0xE0, 0x0C, 0x00,
    0x00, 0x1E, 0x19, 0x8C, 0xE6, 0x70, 0x38, 0x38, 0x1C, 0x18, 0x18, 0x08,
    0x08, 0x00, 0x00, 0x03, 0x03, 0xC1, 0xE0, 0x60, 0x00, 0x03, 0xF0, 0x07,
    0x06, 0x06, 0x00, 0x86, 0x0E, 0x66, 0x0D, 0xDB, 0x0C, 0xE7, 0x06, 0x33,
    0x83, 0x31, 0xC3, 0x18, 0xE1, 0x8C, 0x70, 0xCC, 0x4C, 0x66, 0x46, 0x1F,
    0xC1, 0x80, 0x00, 0x30, 0x10, 0x07, 0xF0, 0x00, 0x10, 0x00, 0x30, 0x00,
    0x70, 0x00, 0x70, 0x00, 0xF0, 0x01, 0xF0, 0x01, 0x78, 0x03, 0x78, 0x02,
    0x38, 0x04, 0x38, 0x0C, 0x38, 0x0F, 0xF8, 0x18, 0x3C, 0x30, 0x3C, 0x20,
    0x3C, 0x60, 0x3C, 0xF8, 0x7F, 0x1F, 0xFC, 0x07, 0x9E, 0x07, 0x0F, 0x07,
    0x0F, 0x0F, 0x0F, 0x0F, 0x1E, 0x0E, 0x3C, 0x0F, 0xE0, 0x1E, 0x3C, 0x1E,
    0x1E, 0x1C, 0x1E, 0x3C, 0x1E, 0x3C, 0x1E, 0x3C, 0x3E, 0x38, 0x3C, 0x7C,
    0x78, 0xFF, 0xE0, 0x01, 0xF2, 0x0E, 0x1C, 0x38, 0x18, 0xE0, 0x33, 0xC0,
    0x4F, 0x00, 0x9E, 0x00, 0x7C, 0x00, 0xF0, 0x01, 0xE0, 0x03, 0xC0, 0x07,
    0x80, 0x0F, 0x00, 0x1E, 0x00, 0x1E, 0x04, 0x1E, 0x30, 0x0F, 0x80, 0x1F,
    0xFC, 0x01, 0xE3, 0xC0, 0x70, 0x78, 0x1C, 0x0E, 0x0F, 0x03, 0xC3, 0xC0,
    0xF0, 0xE0, 0x3C, 0x38, 0x0F, 0x1E, 0x03, 0xC7, 0x81, 0xF1, 0xC0, 0x78,
    0xF0, 0x1E, 0x3C, 0x0F, 0x0F, 0x03, 0xC3, 0x81, 0xC1, 0xE1, 0xE0, 0xFF,
    0xE0, 0x00, 0x1F, 0xFF, 0x83, 0xC1, 0xC1, 0xC0, 0x40, 0xE0, 0x20, 0xF0,
    0x00, 0x78, 0xC0, 0x38, 0x40, 0x1F, 0xE0, 0x1E, 0x70, 0x0F, 0x18, 0x07,
    0x08, 0x03, 0x84, 0x03, 0xC0, 0x61, 0xE0, 0x20, 0xE0, 0x30, 0xF8, 0x78,
    0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0x07, 0x87, 0x07, 0x02, 0x07, 0x02, 0x0F,
    0x00, 0x0F, 0x18, 0x0E, 0x10, 0x0F, 0xF0, 0x1E, 0x70, 0x1E, 0x30, 0x1C,
    0x20, 0x1C, 0x00, 0x3C, 0x00, 0x3C, 0x00, 0x38, 0x00, 0x7C, 0x00, 0xFE,
    0x00, 0x01, 0xF9, 0x03, 0xC3, 0x83, 0x81, 0xC3, 0x80, 0x43, 0xC0, 0x23,
    0xC0, 0x01, 0xE0, 0x01, 0xF0, 0x00, 0xF0, 0x3F, 0xF8, 0x0F, 0x3C, 0x07,
    0x9E, 0x03, 0xCF, 0x01, 0xC3, 0x80, 0xE1, 0xE0, 0xF0, 0x78, 0x70, 0x0F,
    0xE0, 0x00, 0x1F, 0xE7, 0xF0, 0x78, 0x3C, 0x07, 0x83, 0xC0, 0x70, 0x3C,
    0x0F, 0x03, 0x80, 0xF0, 0x78, 0x0E, 0x07, 0x80, 0xE0, 0x70, 0x1F, 0xFF,
    0x01, 0xE0, 0xF0, 0x1C, 0x0F, 0x03, 0xC0, 0xE0, 0x3C, 0x1E, 0x03, 0xC1,
    0xE0, 0x38, 0x1E, 0x07, 0xC3, 0xE0, 0xFE, 0x7F, 0x00, 0x1F, 0xC1, 0xE0,
    0x70, 0x1C, 0x0F, 0x03, 0xC0, 0xE0, 0x38, 0x1E, 0x07, 0x81, 0xC0, 0x70,
    0x3C, 0x0F, 0x03, 0x81, 0xF0, 0xFE, 0x00, 0x01, 0xFC, 0x03, 0xC0, 0x0F,
    0x00, 0x38, 0x00, 0xE0, 0x07, 0x80, 0x1E, 0x00, 0x70, 0x01, 0xC0, 0x0F,
    0x00, 0x3C, 0x00, 0xE0, 0x07, 0x80, 0x1E, 0x0E, 0x70, 0x3B, 0xC0, 0xCE,
    0x01, 0xF0, 0x00, 0x1F, 0xEF, 0x83, 0xC1, 0x81, 0xC1, 0x80, 0xE1, 0x80,
    0xF1, 0x80, 0x79, 0x00, 0x39, 0x00, 0x1F, 0x80, 0x1F, 0xE0, 0x0F, 0x70,
    0x07, 0x3C, 0x07, 0x8E, 0x03, 0xC7, 0x01, 0xE3, 0xC0, 0xE0, 0xE0, 0xF8,
    0x78, 0xFE, 0xFE, 0x00, 0x1F, 0xE0, 0x0F, 0x00, 0x1C, 0x00, 0x38, 0x00,
    0xF0, 0x01, 0xE0, 0x03, 0x80, 0x07, 0x00, 0x1E, 0x00, 0x3C, 0x00, 0x70,
    0x00, 0xE0, 0x03, 0xC0, 0x27, 0x00, 0xCE, 0x03, 0x3C, 0x1E, 0xFF, 0xFC,
    0x0F, 0x80, 0x7E, 0x0F, 0x00, 0xF0, 0x1E, 0x03, 0xE0, 0x3C, 0x0F, 0x80,
    0xB8, 0x17, 0x01, 0x70, 0x5E, 0x02, 0xF1, 0xBC, 0x05, 0xE2, 0x70, 0x11,
    0xC8, 0xE0, 0x23, 0xB3, 0xC0, 0x47, 0x47, 0x81, 0x0F, 0x8E, 0x02, 0x1E,
    0x1C, 0x04, 0x38, 0x78, 0x08, 0x70, 0xF0, 0x30, 0xC3, 0xE0, 0xF9, 0x8F,
    0xE0, 0x1F, 0x03, 0xE0, 0xF0, 0x38, 0x1E, 0x02, 0x03, 0xE0, 0xC0, 0xBC,
    0x10, 0x13, 0xC2, 0x02, 0x78, 0x40, 0x47, 0x90, 0x10, 0xF2, 0x02, 0x0F,
    0x40, 0x41, 0xE8, 0x18, 0x1E, 0x02, 0x03, 0xC0, 0x40, 0x38, 0x08, 0x06,
    0x03, 0x00, 0x40, 0x10, 0x08, 0x00, 0x01, 0xF8, 0x07, 0x1C, 0x0E, 0x0E,
    0x1E, 0x0F, 0x3C, 0x0F, 0x3C, 0x0F, 0x78, 0x0F, 0x78, 0x0F, 0xF8, 0x1F,
    0xF0, 0x1E, 0xF0, 0x1E, 0xF0, 0x3C, 0xF0, 0x3C, 0xF0, 0x78, 0x70, 0x70,
    0x38, 0xE0, 0x1F, 0x80, 0x1F, 0xFC, 0x07, 0x9E, 0x07, 0x0F, 0x07, 0x0F,
    0x0F, 0x0F, 0x0F, 0x0F, 0x0E, 0x1E, 0x0E, 0x3C, 0x1F, 0xF0, 0x1E, 0x00,
    0x1C, 0x00, 0x1C, 0x00, 0x3C, 0x00, 0x38, 0x00, 0x38, 0x00, 0x7C, 0x00,
    0xFE, 0x00, 0x01, 0xF8, 0x07, 0x1C, 0x0E, 0x0E, 0x1E, 0x0F, 0x3C, 0x0F,
    0x3C, 0x0F, 0x78, 0x0F, 0x78, 0x1F, 0xF8, 0x1F, 0xF0, 0x1E, 0xF0, 0x1E,
    0xF0, 0x3C, 0xF0, 0x3C, 0xF0, 0x78, 0x70, 0x70, 0x39, 0xC0, 0x0E, 0x00,
    0x08, 0x02, 0x3F, 0x04, 0x7F, 0xF8, 0x83, 0xF0, 0x1F, 0xF8, 0x07, 0x9E,
    0x07, 0x8F, 0x07, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x1E, 0x0E, 0x3C,
    0x1F, 0xF0, 0x1E, 0xF0, 0x1C, 0xF0, 0x3C, 0xF0, 0x3C, 0x78, 0x3C, 0x78,
    0x3C, 0x78, 0x7C, 0x3C, 0xFE, 0x3E, 0x07, 0x91, 0xC7, 0x18, 0x73, 0x82,
    0x38, 0x23, 0xC0, 0x3E, 0x01, 0xF0, 0x0F, 0x80, 0x7C, 0x01, 0xE0, 0x1E,
    0x40, 0xE4, 0x0E, 0x60, 0xCE, 0x1C, 0x9F, 0x00, 0x7F, 0xFE, 0xE7, 0x9D,
    0x0E, 0x16, 0x3C, 0x20, 0x78, 0x40, 0xE0, 0x01, 0xC0, 0x07, 0x80, 0x0F,
    0x00, 0x1C, 0x00, 0x38, 0x00, 0xF0, 0x01, 0xE0, 0x03, 0x80, 0x0F, 0x00,
    0x1E, 0x00, 0xFF, 0x00, 0x7F, 0x1F, 0x3C, 0x0E, 0x38, 0x04, 0x38, 0x0C,
    0x78, 0x08, 0x78, 0x08, 0x70, 0x08, 0x70, 0x10, 0xF0, 0x10, 0xF0, 0x10,
    0xF0, 0x10, 0xF0, 0x20, 0xF0, 0x20, 0xF0, 0x20, 0xF0, 0x40, 0x78, 0xC0,
    0x3F, 0x00, 0xFF, 0x1F, 0x3C, 0x06, 0x3C, 0x04, 0x3C, 0x08, 0x3C, 0x08,
    0x3C, 0x10, 0x1C, 0x20, 0x1C, 0x20, 0x1E, 0x40, 0x1E, 0x80, 0x1E, 0x80,
    0x1F, 0x00, 0x0E, 0x00, 0x0E, 0x00, 0x0C, 0x00, 0x08, 0x00, 0xFE, 0x7C,
    0x79, 0xE1, 0xC1, 0x8F, 0x0E, 0x08, 0x78, 0x70, 0x43, 0xC7, 0x84, 0x1E,
    0x3E, 0x20, 0x72, 0xF2, 0x03, 0x97, 0x90, 0x1D, 0x1D, 0x00, 0xE8, 0xE8,
    0x07, 0x87, 0x80, 0x3C, 0x3C, 0x01, 0xC1, 0xC0, 0x0E, 0x0E, 0x00, 0x20,
    0x60, 0x01, 0x02, 0x00, 0x1F, 0xCF, 0x83, 0xC1, 0x81, 0xE1, 0x80, 0x71,
    0x80, 0x39, 0x80, 0x1F, 0x80, 0x07, 0x80, 0x03, 0x80, 0x01, 0xE0, 0x01,
    0xF0, 0x00, 0xB8, 0x00, 0x9C, 0x00, 0x8F, 0x00, 0x83, 0x80, 0xC1, 0xC0,
    0xE0, 0xF0, 0xF9, 0xFE, 0x00, 0xFE, 0x7C, 0xE0, 0x63, 0x81, 0x0F, 0x08,
    0x1C, 0x40, 0x71, 0x01, 0xE8, 0x03, 0xC0, 0x0E, 0x00, 0x38, 0x01, 0xE0,
    0x07, 0x80, 0x1C, 0x00, 0x70, 0x03, 0xC0, 0x0F, 0x00, 0xFF, 0x00, 0x1F,
    0xFE, 0x38, 0x78, 0x60, 0xF1, 0x83, 0xC2, 0x0F, 0x00, 0x1E, 0x00, 0x78,
    0x01, 0xE0, 0x07, 0xC0, 0x0F, 0x00, 0x3C, 0x00, 0xF8, 0x01, 0xE0, 0x47,
    0x81, 0x1F, 0x06, 0x3C, 0x3C, 0xFF, 0xF0, 0x07, 0xC1, 0x80, 0xE0, 0x30,
    0x0C, 0x03, 0x01, 0xC0, 0x60, 0x18, 0x06, 0x03, 0x80, 0xC0, 0x30, 0x0C,
    0x07, 0x01, 0xC0, 0x60, 0x18, 0x0E, 0x03, 0xE0, 0xC3, 0x06, 0x18, 0x61,
    0x83, 0x0C, 0x30, 0xC1, 0x86, 0x18, 0x60, 0xC3, 0x0F, 0x81, 0xC0, 0xE0,
    0x60, 0x30, 0x18, 0x1C, 0x0C, 0x06, 0x03, 0x03, 0x81, 0x80, 0xC0, 0x60,
    0x70, 0x38, 0x18, 0x0C, 0x0E, 0x1F, 0x00, 0x0C, 0x07, 0x81, 0xE0, 0xDC,
    0x33, 0x18, 0xC6, 0x1B, 0x06, 0xC0, 0xC0, 0xFF, 0xF0, 0xC7, 0x0C, 0x30,
    0x07, 0x70, 0xCE, 0x1C, 0xE3, 0x8E, 0x70, 0xC7, 0x0C, 0x71, 0xCE, 0x1C,
    0xE1, 0x8E, 0x79, 0xE9, 0xA7, 0x1C, 0x02, 0x07, 0xC0, 0x38, 0x06, 0x01,
    0xC0, 0x38, 0x06, 0x71, 0xF7, 0x38, 0xE7, 0x1C, 0xC3, 0xB8, 0x77, 0x1C,
    0xE3, 0xB8, 0xE7, 0x18, 0xE6, 0x0F, 0x80, 0x07, 0x0C, 0xCE, 0x66, 0x07,
    0x03, 0x83, 0x81, 0xC0, 0xE0, 0x70, 0xBC, 0x87, 0x80, 0x00, 0x08, 0x03,
    0xE0, 0x03, 0x80, 0x0E, 0x00, 0x70, 0x01, 0xC0, 0x77, 0x03, 0x3C, 0x18,
    0xE0, 0xE3, 0x87, 0x0E, 0x1C, 0x70, 0x71, 0xC3, 0x87, 0x0E, 0x3C, 0x38,
    0xE8, 0xE5, 0xA1, 0xE7, 0x00, 0x07, 0x0C, 0xCE, 0x66, 0x37, 0x33, 0xBB,
    0xB1, 0xE0, 0xE0, 0x70, 0xB8, 0x87, 0x80, 0x00, 0x38, 0x01, 0xB0, 0x0C,
    0xC0, 0x30, 0x01, 0xC0, 0x07, 0x00, 0x7E, 0x00, 0xE0, 0x03, 0x80, 0x0E,
    0x00, 0x30, 0x01, 0xC0, 0x07, 0x00, 0x1C, 0x00, 0x70, 0x03, 0x80, 0x0E,
    0x00, 0x38, 0x00, 0xC0, 0x33, 0x00, 0xD8, 0x01, 0xC0, 0x00, 0x03, 0x80,
    0x73, 0xC7, 0x1C, 0x38, 0xE1, 0xCF, 0x06, 0x70, 0x1E, 0x01, 0x00, 0x1C,
    0x00, 0xF8, 0x07, 0xF0, 0xC7, 0x8C, 0x0C, 0x60, 0x63, 0x86, 0x07, 0xE0,
    0x01, 0x00, 0xF8, 0x01, 0x80, 0x1C, 0x00, 0xE0, 0x07, 0x00, 0x31, 0xC3,
    0xBE, 0x1E, 0x70, 0xE3, 0x8F, 0x38, 0x71, 0xC3, 0x8E, 0x1C, 0xE1, 0xC7,
    0x0E, 0x3A, 0x71, 0xD3, 0x0F, 0x00, 0x1C, 0x71, 0xC0, 0x00, 0x6F, 0x8E,
    0x31, 0xC7, 0x18, 0x63, 0x8E, 0xBC, 0xE0, 0x00, 0xE0, 0x1C, 0x03, 0x80,
    0x00, 0x00, 0x0F, 0x80, 0x70, 0x0E, 0x01, 0xC0, 0x70, 0x0E, 0x01, 0xC0,
    0x38, 0x0E, 0x01, 0xC0, 0x38, 0x06, 0x01, 0xC3, 0x38, 0x6E, 0x07, 0x80,
    0x01, 0x00, 0xF8, 0x01, 0xC0, 0x1C, 0x00, 0xE0, 0x07, 0x00, 0x33, 0xE3,
    0x8C, 0x1C, 0xC0, 0xE4, 0x06, 0x40, 0x7E, 0x03, 0xF0, 0x1D, 0x81, 0xCE,
    0x0E, 0x72, 0x71, 0xA3, 0x8E, 0x00, 0x06, 0x7C, 0x70, 0xE1, 0xC3, 0x0E,
    0x1C, 0x38, 0x61, 0xC3, 0x87, 0x0C, 0x38, 0x72, 0xE9, 0xE0, 0x3C, 0x73,
    0xC7, 0x7D, 0x71, 0xE7, 0x9C, 0xF1, 0xCE, 0x3C, 0xF3, 0x8E, 0x39, 0xC3,
    0x8E, 0x71, 0xC3, 0x1C, 0x71, 0xC7, 0x1C, 0x71, 0xD7, 0x1C, 0x7B, 0x8E,
    0x1C, 0x3C, 0xF1, 0xD7, 0x1E, 0x73, 0xCE, 0x3C, 0xE3, 0x8E, 0x39, 0xC7,
    0x9C, 0x71, 0xC7, 0x1D, 0x71, 0xEE, 0x1C, 0x0F, 0x06, 0x63, 0x9D, 0xC7,
    0x71, 0xF8, 0x7E, 0x3F, 0x8E, 0xE3, 0xB9, 0xC6, 0x60, 0xF0, 0x0F, 0x38,
    0x1F, 0x70, 0x71, 0xC1, 0xC7, 0x0E, 0x1C, 0x38, 0xF0, 0xE3, 0x83, 0x8E,
    0x1C, 0x70, 0x71, 0xC1, 0xCE, 0x07, 0xE0, 0x38, 0x00, 0xE0, 0x03, 0x80,
    0x3F, 0x00, 0x07, 0x70, 0xCE, 0x18, 0xE3, 0x8E, 0x70, 0xE7, 0x1C, 0xF1,
    0xCE, 0x1C, 0xE3, 0x8E, 0x38, 0xE7, 0x87, 0xB0, 0x07, 0x00, 0x70, 0x0F,
    0x03, 0xF8, 0x0D, 0xDF, 0x71, 0xAC, 0xF0, 0x38, 0x0E, 0x03, 0x81, 0xC0,
    0x70, 0x1C, 0x0E, 0x00, 0x1D, 0x99, 0x8C, 0x46, 0x23, 0x80, 0xE0, 0x70,
    0x1C, 0x06, 0x23, 0x19, 0x17, 0x00, 0x0C, 0x10, 0xE3, 0xF3, 0x86, 0x1C,
    0x38, 0x71, 0xC3, 0x87, 0x0E, 0x9E, 0x38, 0x00, 0xF8, 0xE3, 0x8E, 0x38,
    0xC3, 0x9C, 0x71, 0xC7, 0x18, 0x71, 0x87, 0x38, 0xE3, 0x8E, 0xFA, 0xF3,
    0xAE, 0x3C, 0xF0, 0xDC, 0x33, 0x0C, 0xC2, 0x31, 0x8C, 0xC3, 0x60, 0xF0,
    0x38, 0x0C, 0x02, 0x00, 0xE0, 0x86, 0xE3, 0x0C, 0xC6, 0x19, 0x9C, 0x23,
    0x78, 0xC7, 0xF9, 0x0E, 0x74, 0x1C, 0xF0, 0x31, 0xC0, 0x43, 0x00, 0x84,
    0x00, 0x0E, 0x31, 0xF3, 0x83, 0xA0, 0x0E, 0x00, 0x70, 0x03, 0x80, 0x1C,
    0x00, 0xE0, 0x0B, 0x02, 0x5D, 0x3C, 0xF1, 0xC3, 0x00, 0x04, 0x67, 0x8C,
    0x79, 0x87, 0x10, 0xE2, 0x1C, 0x81, 0x90, 0x3A, 0x07, 0x80, 0xF0, 0x1C,
    0x03, 0x00, 0x40, 0x08, 0x32, 0x07, 0x80, 0x3F, 0xCF, 0xE6, 0x30, 0x08,
    0x04, 0x02, 0x01, 0x00, 0xC0, 0x30, 0x1E, 0x0F, 0x98, 0x76, 0x07, 0x00,
    0x01, 0xE0, 0x70, 0x1C, 0x03, 0x80, 0x60, 0x1C, 0x03, 0x80, 0x60, 0x0C,
    0x03, 0x80, 0xF0, 0x3C, 0x07, 0x00, 0x40, 0x0C, 0x01, 0x80, 0x70, 0x0E,
    0x01, 0xC0, 0x30, 0x03, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, 0x07, 0x00, 0xE0,
    0x18, 0x06, 0x01, 0x80, 0xE0, 0x38, 0x0C, 0x03, 0x00, 0xC0, 0x10, 0x1F,
    0x07, 0x03, 0x80, 0xE0, 0x30, 0x0C, 0x07, 0x01, 0x80, 0xE0, 0xE0, 0x00,
    0x38, 0x0F, 0xCD, 0x1F, 0x80, 0xE0};

const GFXglyph FreeSerifBoldItalic12pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 6, 0, 1},          // 0x20 ' '
    {0, 8, 17, 9, 2, -15},       // 0x21 '!'
    {17, 9, 7, 13, 4, -15},      // 0x22 '"'
    {25, 14, 16, 12, -1, -15},   // 0x23 '#'
    {53, 12, 20, 12, 0, -17},    // 0x24 '$'
    {83, 18, 18, 20, 1, -16},    // 0x25 '%'
    {124, 16, 17, 19, 0, -15},   // 0x26 '&'
    {158, 3, 7, 7, 3, -15},      // 0x27 '''
    {161, 7, 21, 8, 1, -15},     // 0x28 '('
    {180, 7, 21, 8, -1, -15},    // 0x29 ')'
    {199, 10, 10, 12, 1, -15},   // 0x2A '*'
    {212, 12, 12, 14, 1, -11},   // 0x2B '+'
    {230, 5, 8, 6, -2, -3},      // 0x2C ','
    {235, 6, 3, 8, 0, -6},       // 0x2D '-'
    {238, 4, 4, 6, 0, -2},       // 0x2E '.'
    {240, 10, 16, 8, 0, -15},    // 0x2F '/'
    {260, 11, 17, 12, 0, -15},   // 0x30 '0'
    {284, 10, 17, 12, 0, -15},   // 0x31 '1'
    {306, 11, 17, 12, 0, -15},   // 0x32 '2'
    {330, 11, 17, 12, 0, -15},   // 0x33 '3'
    {354, 13, 16, 12, 0, -15},   // 0x34 '4'
    {380, 12, 17, 12, 0, -15},   // 0x35 '5'
    {406, 11, 17, 12, 1, -15},   // 0x36 '6'
    {430, 11, 16, 12, 2, -15},   // 0x37 '7'
    {452, 11, 17, 12, 0, -15},   // 0x38 '8'
    {476, 11, 17, 12, 0, -15},   // 0x39 '9'
    {500, 7, 12, 6, 0, -10},     // 0x3A ':'
    {511, 8, 15, 6, -1, -10},    // 0x3B ';'
    {526, 12, 13, 14, 1, -12},   // 0x3C '<'
    {546, 12, 6, 14, 2, -8},     // 0x3D '='
    {555, 13, 13, 14, 1, -12},   // 0x3E '>'
    {577, 9, 17, 12, 2, -15},    // 0x3F '?'
    {597, 17, 16, 20, 1, -15},   // 0x40 '@'
    {631, 16, 17, 17, 0, -15},   // 0x41 'A'
    {665, 16, 17, 15, 0, -15},   // 0x42 'B'
    {699, 15, 17, 15, 1, -15},   // 0x43 'C'
    {731, 18, 17, 17, 0, -15},   // 0x44 'D'
    {770, 17, 17, 15, 0, -15},   // 0x45 'E'
    {807, 16, 17, 15, 0, -15},   // 0x46 'F'
    {841, 17, 17, 17, 1, -15},   // 0x47 'G'
    {878, 20, 17, 18, 0, -15},   // 0x48 'H'
    {921, 10, 17, 9, 0, -15},    // 0x49 'I'
    {943, 14, 18, 12, 0, -15},   // 0x4A 'J'
    {975, 17, 17, 16, 0, -15},   // 0x4B 'K'
    {1012, 15, 17, 15, 0, -15},  // 0x4C 'L'
    {1044, 23, 17, 21, 0, -15},  // 0x4D 'M'
    {1093, 19, 17, 17, 0, -15},  // 0x4E 'N'
    {1134, 16, 17, 16, 1, -15},  // 0x4F 'O'
    {1168, 16, 17, 14, 0, -15},  // 0x50 'P'
    {1202, 16, 21, 16, 1, -15},  // 0x51 'Q'
    {1244, 16, 17, 16, 0, -15},  // 0x52 'R'
    {1278, 12, 17, 12, 0, -15},  // 0x53 'S'
    {1304, 15, 17, 14, 2, -15},  // 0x54 'T'
    {1336, 16, 17, 17, 3, -15},  // 0x55 'U'
    {1370, 16, 16, 17, 3, -15},  // 0x56 'V'
    {1402, 21, 16, 22, 3, -15},  // 0x57 'W'
    {1444, 17, 17, 17, 0, -15},  // 0x58 'X'
    {1481, 14, 17, 15, 3, -15},  // 0x59 'Y'
    {1511, 15, 17, 13, 0, -15},  // 0x5A 'Z'
    {1543, 10, 20, 8, -1, -15},  // 0x5B '['
    {1568, 6, 16, 10, 3, -15},   // 0x5C '\'
    {1580, 9, 20, 8, -1, -15},   // 0x5D ']'
    {1603, 10, 9, 14, 2, -15},   // 0x5E '^'
    {1615, 12, 1, 12, 0, 4},     // 0x5F '_'
    {1617, 5, 4, 8, 2, -15},     // 0x60 '`'
    {1620, 12, 12, 12, 0, -10},  // 0x61 'a'
    {1638, 11, 18, 12, 1, -16},  // 0x62 'b'
    {1663, 9, 12, 10, 1, -10},   // 0x63 'c'
    {1677, 14, 18, 12, 0, -16},  // 0x64 'd'
    {1709, 9, 12, 10, 1, -10},   // 0x65 'e'
    {1723, 14, 22, 12, -2, -16}, // 0x66 'f'
    {1762, 13, 16, 12, -1, -10}, // 0x67 'g'
    {1788, 13, 18, 13, 0, -16},  // 0x68 'h'
    {1818, 6, 17, 7, 1, -15},    // 0x69 'i'
    {1831, 11, 21, 8, -2, -15},  // 0x6A 'j'
    {1860, 13, 18, 12, 0, -16},  // 0x6B 'k'
    {1890, 7, 18, 7, 1, -16},    // 0x6C 'l'
    {1906, 18, 12, 18, 0, -10},  // 0x6D 'm'
    {1933, 12, 12, 13, 0, -10},  // 0x6E 'n'
    {1951, 10, 12, 11, 1, -10},  // 0x6F 'o'
    {1966, 14, 16, 12, -2, -10}, // 0x70 'p'
    {1994, 12, 16, 12, 0, -10},  // 0x71 'q'
    {2018, 10, 11, 10, 0, -10},  // 0x72 'r'
    {2032, 9, 12, 9, 0, -10},    // 0x73 's'
    {2046, 7, 15, 7, 1, -13},    // 0x74 't'
    {2060, 12, 12, 13, 1, -10},  // 0x75 'u'
    {2078, 10, 11, 11, 1, -10},  // 0x76 'v'
    {2092, 15, 11, 16, 1, -10},  // 0x77 'w'
    {2113, 13, 12, 11, -1, -10}, // 0x78 'x'
    {2133, 11, 16, 10, -1, -10}, // 0x79 'y'
    {2155, 10, 13, 10, 0, -10},  // 0x7A 'z'
    {2172, 11, 21, 8, 0, -16},   // 0x7B '{'
    {2201, 2, 16, 6, 3, -15},    // 0x7C '|'
    {2205, 10, 21, 8, -3, -16},  // 0x7D '}'
    {2232, 11, 4, 14, 1, -7}};   // 0x7E '~'

const GFXfont FreeSerifBoldItalic12pt7b PROGMEM = {
    (uint8_t *)FreeSerifBoldItalic12pt7bBitmaps,
    (GFXglyph *)FreeSerifBoldItalic12pt7bGlyphs, 0x20, 0x7E, 29};

// Approx. 2910 bytes

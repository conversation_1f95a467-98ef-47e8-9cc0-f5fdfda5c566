#ifndef ENCODER_H
#define ENCODER_H

#include <driver/pcnt.h>
#include <Preferences.h>
#include "spi_tle5012b.h"

// Forward declarations
class MotorControl;

// Calibration parameters
#define SOFT_DIVIDE_NUM 256
#define EC_SAMPLE_COUNTS_PER_STEP 16
#define MOTOR_ONE_CIRCLE_SUBDIVIDE_STEPS 51200
#define DEFAULT_CALIBRATION_CURRENT 500 // mA

class Encoder {
public:
    Encoder();
    void init(float caliCurrent = DEFAULT_CALIBRATION_CURRENT);
    float getPosition();
    void reset();
    void calibrate(MotorControl& motor);
    bool saveCalibrationToFlash();
    bool loadCalibrationFromFlash();

private:
    float calculateAverage(uint16_t* data, uint8_t count);
    void calculateCalibrationParams();
    
    pcnt_unit_t pcnt_unit;
    static constexpr int PCNT_HIGH_LIMIT = 32767;
    static constexpr int PCNT_LOW_LIMIT = -32768;
    static constexpr gpio_num_t ENCODER_A_PIN = GPIO_NUM_4;
    static constexpr gpio_num_t ENCODER_B_PIN = GPIO_NUM_5;
    
    enum CalibrationState {
        CALI_DISABLE,
        CALI_FORWARD_PREPARE,
        CALI_FORWARD_MEASURE,
        CALI_BACKWARD_RETURN,
        CALI_BACKWARD_GAP_DISMISS,
        CALI_BACKWARD_MEASURE,
        CALI_CALCULATING
    } caliState;

    typedef enum{
        CALI_NO_ERROR = 0x00,
        CALI_ERROR_AVERAGE_DIR,
        CALI_ERROR_AVERAGE_CONTINUTY,
        CALI_ERROR_PHASE_STEP,
        CALI_ERROR_ANALYSIS_QUANTITY,
    } cali_Error_t;
    
    struct CalibrationData {
        float offset;
        float scale;
        bool calibrated;
        float forwardData[SOFT_DIVIDE_NUM];
        float backwardData[SOFT_DIVIDE_NUM];
    } calibration;
    
    struct {
        uint16_t sampleCount;
        uint16_t goPosition;
        uint16_t sampleDataRaw[EC_SAMPLE_COUNTS_PER_STEP];
    } caliParams;
    
    Preferences prefs;
    float calibrationCurrent;
};
#endif

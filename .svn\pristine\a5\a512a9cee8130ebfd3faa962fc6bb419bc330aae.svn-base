/**
 * @file oled.h
 * @brief OLED显示屏驱动头文件
 * @details 基于SSD1306控制器的128x64 OLED显示屏驱动程序
 *          支持SPI通信、图形绘制、文字显示和中文显示功能
 */
#ifndef OLED_H
#define OLED_H
#include <SPI.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

typedef uint8_t u8;   ///< 8位无符号整型别名
typedef uint32_t u32; ///< 32位无符号整型别名
typedef uint16_t u16; ///< 16位无符号整型别名

//--------------OLED参数定义---------------------
#define PAGE_SIZE			8      ///< 页面大小
#define XLevelL				0x00   ///< X坐标低位
#define XLevelH				0x10   ///< X坐标高位
#define YLevel				0xB0   ///< Y坐标
#define	Brightness		0xFF   ///< 亮度设置
#define WIDTH					128    ///< 屏幕宽度
#define HEIGHT				64     ///< 屏幕高度

//-------------写命令和数据定义-------------------
#define OLED_CMD			0		///< 写命令
#define OLED_DATA			1		///< 写数据

#define OLED_RST_PIN		GPIO_NUM_21		///< 复位信号引脚
#define OLED_DC_PIN			GPIO_NUM_7		///< 数据/命令控制信号引脚
#define OLED_CS_PIN			GPIO_NUM_8		///< 片选信号引脚



//-----------------OLED端口操作定义----------------
#define OLED_RST_PIN_Clr()	gpio_set_level(OLED_RST_PIN, 0)
#define OLED_RST_PIN_Set()	gpio_set_level(OLED_RST_PIN, 1)

#define OLED_DC_PIN_Clr()	gpio_set_level(OLED_DC_PIN, 0)
#define OLED_DC_PIN_Set()	gpio_set_level(OLED_DC_PIN, 1)

#define OLED_CS_PIN_Clr()	gpio_set_level(OLED_CS_PIN, 0)
#define OLED_CS_PIN_Set()	gpio_set_level(OLED_CS_PIN, 1)





extern int oled_counter;
extern int mode_show;
extern uint16_t tle_angle;
extern float angle_date;
// extern unsigned char epd_bitmap_sellectmode [];
// extern unsigned char epd_bitmap_logo[];



/**
 * @brief 向OLED写入一个字节数据
 * @param dat 要写入的数据
 * @param cmd 命令/数据标志（OLED_CMD或OLED_DATA）
 */
void OLED_Write_Byte(uint8_t dat, uint8_t cmd);

/**
 * @brief 打开OLED显示
 */
void OLED_Display_On(void);

/**
 * @brief 关闭OLED显示
 */
void OLED_Display_Off(void);

/**
 * @brief 设置OLED显示位置
 * @param x X坐标
 * @param y Y坐标
 */
void OLED_Set_Pos(uint8_t x, uint8_t y);

/**
 * @brief 重置OLED显示屏
 */
void OLED_Reset(void);

/**
 * @brief 设置像素点
 * @param x X坐标
 * @param y Y坐标
 * @param color 颜色值
 */
void OLED_Set_Pixel(uint8_t x, uint8_t y, uint8_t color);

/**
 * @brief 刷新OLED显示
 * @details 将缓冲区内容显示到屏幕上
 */
void OLED_Display(void);

/**
 * @brief 清除OLED屏幕
 * @param dat 填充数据
 */
void OLED_Clear(uint8_t dat);

/**
 * @brief 初始化OLED GPIO引脚
 */
void OLED_Init_GPIO(void);

/**
 * @brief 初始化OLED SSD1306控制器
 */
void OLED_Init(void);

/**
 * @brief 初始化OLED SPI通信
 */
void OLED_Init_SPI(void);

/**
 * @brief 设置OLED显示翻转
 * @param flip 翻转标志（0=正常，1=翻转）
 */
void OLED_Set_Flip(int flip);

void OLED_DrawPoint(u8 x, u8 y, u8 color);      // 在OLED屏幕中绘制点
void OLED_Fill(u8 sx,u8 sy,u8 ex,u8 ey,u8 color);  // 填充指定区域
void OLED_DrawLine(u8 x1, u8 y1, u8 x2, u8 y2,u8 color);  // 在两点之间画一条线
void OLED_DrawRectangle(u8 x1, u8 y1, u8 x2, u8 y2,u8 color);  // 绘制矩形
void OLED_FillRectangle(u8 x1, u8 y1, u8 x2, u8 y2,u8 color);  // 填充矩形
void OLED_DrawCircle(u8 xc, u8 yc, u8 color, u8 r);  // 在指定位置绘制指定大小的圆
void OLED_FillCircle(u8 xc, u8 yc, u8 color, u8 r);  // 在指定位置填充指定大小的圆
void OLED_DrawTriangel(u8 x0,u8 y0,u8 x1,u8 y1,u8 x2,u8 y2,u8 color);  // 在指定位置绘制三角形
void OLED_FillTriangel(u8 x0,u8 y0,u8 x1,u8 y1,u8 x2,u8 y2,u8 color);  // 在指定位置填充三角形
void OLED_ShowChar(u8 x,u8 y,u8 chr,u8 Char_Size,u8 mode);  // 显示单个英文字符
void OLED_ShowNum(u8 x,u8 y,u32 num,u8 len,u8 Size,u8 mode);  // 显示英文字符串
void OLED_ShowFloat(uint8_t x, uint8_t y, float num, uint8_t size, uint8_t mode);
void OLED_ShowString(u8 x,u8 y,const char *chr,u8 Char_Size,u8 mode);  // 显示数字
void OLED_ShowFont16(u8 x,u8 y,u8 *s,u8 mode);  // 显示单个16x16汉字
void OLED_ShowFont24(u8 x,u8 y,u8 *s,u8 mode);  // 显示单个24x24汉字
void OLED_ShowFont32(u8 x,u8 y,u8 *s,u8 mode);  // 显示单个32x32汉字
void OLED_ShowCHinese(u8 x,u8 y,u8 hsize,u8 *str,u8 mode);  // 显示中文字符串
void OLED_DrawBMP(u8 x,u8 y,u8 width, u8 height, u8 BMP[], u8 mode);  // 显示BMP单色图片






/**
 * @brief OLED测试函数
 * @details 显示测试图案和文字，用于验证OLED功能
 */
void oled_test();

/**
 * @brief 重置OLED计数器
 */
void reset_oled_counter();

/**
 * @brief 显示启动界面
 * @details 显示系统启动时的欢迎界面和Logo
 */
void show_start();

/**
 * @brief 显示恒星追踪模式界面
 * @details 显示恒星追踪模式的UI和相关参数
 */
void show_stellar_mode();

/**
 * @brief 显示太阳追踪模式界面
 * @details 显示太阳追踪模式的UI和相关参数
 */
void show_sun_mode();

/**
 * @brief 显示月球追踪模式界面
 * @details 显示月球追踪模式的UI和相关参数
 */
void show_moon_mode();

/**
 * @brief 显示停止模式界面
 * @details 显示系统停止状态的UI
 */
void show_stop_mode();

/**
 * @brief 切换显示模式
 * @details 在不同的显示模式之间循环切换
 */
void change_mode();


static unsigned char OLED_buffer[1024] = 
{
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};


/* 8x16 ASCII font */
/* Offset: 32 */
/* Row-wise, forward (high bit first) */
static const unsigned char F8X16[] = {
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,// 0
	0x00,0x00,0x00,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x00,0x00,0x18,0x18,0x00,0x00,//! 1
	0x00,0x12,0x36,0x24,0x48,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//" 2
	0x00,0x00,0x00,0x24,0x24,0x24,0xFE,0x48,0x48,0x48,0xFE,0x48,0x48,0x48,0x00,0x00,//# 3
	0x00,0x00,0x10,0x38,0x54,0x54,0x50,0x30,0x1C,0x14,0x14,0x54,0x54,0x38,0x10,0x10,//$ 4
	0x00,0x00,0x00,0x44,0xA4,0xA8,0xA8,0xA8,0x54,0x1A,0x2A,0x2A,0x2A,0x44,0x00,0x00,//% 5
	0x00,0x00,0x00,0x30,0x48,0x48,0x48,0x50,0x6E,0xA4,0x94,0x88,0x89,0x76,0x00,0x00,//& 6
	0x00,0x60,0x60,0x20,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//' 7
	0x00,0x02,0x04,0x08,0x08,0x10,0x10,0x10,0x10,0x10,0x10,0x08,0x08,0x04,0x02,0x00,//( 8
	0x00,0x40,0x20,0x10,0x10,0x08,0x08,0x08,0x08,0x08,0x08,0x10,0x10,0x20,0x40,0x00,//) 9
	0x00,0x00,0x00,0x00,0x10,0x10,0xD6,0x38,0x38,0xD6,0x10,0x10,0x00,0x00,0x00,0x00,//* 10
	0x00,0x00,0x00,0x00,0x10,0x10,0x10,0x10,0xFE,0x10,0x10,0x10,0x10,0x00,0x00,0x00,//+ 11
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x20,0xC0,//, 12
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//- 13
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,//. 14
	0x00,0x00,0x01,0x02,0x02,0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x00,/// 15
	0x00,0x00,0x00,0x18,0x24,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x24,0x18,0x00,0x00,//0 16
	0x00,0x00,0x00,0x10,0x70,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x7C,0x00,0x00,//1 17
	0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x04,0x04,0x08,0x10,0x20,0x42,0x7E,0x00,0x00,//2 18
	0x00,0x00,0x00,0x3C,0x42,0x42,0x04,0x18,0x04,0x02,0x02,0x42,0x44,0x38,0x00,0x00,//3 19
	0x00,0x00,0x00,0x04,0x0C,0x14,0x24,0x24,0x44,0x44,0x7E,0x04,0x04,0x1E,0x00,0x00,//4 20
	0x00,0x00,0x00,0x7E,0x40,0x40,0x40,0x58,0x64,0x02,0x02,0x42,0x44,0x38,0x00,0x00,//5 21
	0x00,0x00,0x00,0x1C,0x24,0x40,0x40,0x58,0x64,0x42,0x42,0x42,0x24,0x18,0x00,0x00,//6 22
	0x00,0x00,0x00,0x7E,0x42,0x44,0x08,0x08,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,//7 23
	0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x24,0x18,0x24,0x42,0x42,0x42,0x3C,0x00,0x00,//8 24
	0x00,0x00,0x00,0x18,0x24,0x42,0x42,0x42,0x26,0x1A,0x02,0x02,0x24,0x38,0x00,0x00,//9 25
	0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,//: 26
	0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x18,0x18,0x08,0x30,0x00,//; 27
	0x00,0x00,0x00,0x02,0x04,0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x04,0x02,0x00,0x00,//< 28
	0x00,0x00,0x00,0x00,0x00,0x00,0xFE,0x00,0x00,0x00,0xFE,0x00,0x00,0x00,0x00,0x00,//= 29
	0x00,0x00,0x00,0x40,0x20,0x10,0x08,0x04,0x02,0x04,0x08,0x10,0x20,0x40,0x00,0x00,//> 30
	0x00,0x00,0x00,0x3C,0x42,0x42,0x62,0x02,0x04,0x08,0x08,0x00,0x18,0x18,0x00,0x00,//? 31
	0x00,0x00,0x00,0x38,0x44,0x5A,0xAA,0xAA,0xAA,0xAA,0xB4,0x42,0x44,0x38,0x00,0x00,//@ 32
	0x00,0x00,0x00,0x10,0x10,0x18,0x28,0x28,0x24,0x3C,0x44,0x42,0x42,0xE7,0x00,0x00,//A 33
	0x00,0x00,0x00,0xF8,0x44,0x44,0x44,0x78,0x44,0x42,0x42,0x42,0x44,0xF8,0x00,0x00,//B 34
	0x00,0x00,0x00,0x3E,0x42,0x42,0x80,0x80,0x80,0x80,0x80,0x42,0x44,0x38,0x00,0x00,//C 35
	0x00,0x00,0x00,0xF8,0x44,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x44,0xF8,0x00,0x00,//D 36
	0x00,0x00,0x00,0xFE,0x42,0x48,0x48,0x78,0x48,0x48,0x40,0x42,0x42,0xFE,0x00,0x00,//E 37
	0x00,0x00,0x00,0xFE,0x42,0x48,0x48,0x78,0x48,0x48,0x40,0x40,0x40,0xE0,0x00,0x00,//F 38
	0x00,0x00,0x00,0x3C,0x44,0x44,0x80,0x80,0x80,0x8E,0x84,0x44,0x44,0x38,0x00,0x00,//G 39
	0x00,0x00,0x00,0xE7,0x42,0x42,0x42,0x42,0x7E,0x42,0x42,0x42,0x42,0xE7,0x00,0x00,//H 40
	0x00,0x00,0x00,0x7C,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x7C,0x00,0x00,//I 41
	0x00,0x00,0x00,0x3E,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x88,0xF0,//J 42
	0x00,0x00,0x00,0xEE,0x44,0x48,0x50,0x70,0x50,0x48,0x48,0x44,0x44,0xEE,0x00,0x00,//K 43
	0x00,0x00,0x00,0xE0,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x42,0xFE,0x00,0x00,//L 44
	0x00,0x00,0x00,0xEE,0x6C,0x6C,0x6C,0x6C,0x54,0x54,0x54,0x54,0x54,0xD6,0x00,0x00,//M 45
	0x00,0x00,0x00,0xC7,0x62,0x62,0x52,0x52,0x4A,0x4A,0x4A,0x46,0x46,0xE2,0x00,0x00,//N 46
	0x00,0x00,0x00,0x38,0x44,0x82,0x82,0x82,0x82,0x82,0x82,0x82,0x44,0x38,0x00,0x00,//O 47
	0x00,0x00,0x00,0xFC,0x42,0x42,0x42,0x42,0x7C,0x40,0x40,0x40,0x40,0xE0,0x00,0x00,//P 48
	0x00,0x00,0x00,0x38,0x44,0x82,0x82,0x82,0x82,0x82,0xB2,0xCA,0x4C,0x38,0x06,0x00,//Q 49
	0x00,0x00,0x00,0xF8,0x44,0x44,0x44,0x78,0x48,0x48,0x44,0x44,0x42,0xE3,0x00,0x00,//R 50
	0x00,0x00,0x00,0x3E,0x42,0x42,0x40,0x20,0x18,0x04,0x02,0x42,0x42,0x7C,0x00,0x00,//S 51
	0x00,0x00,0x00,0xFE,0x92,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,//T 52
	0x00,0x00,0x00,0xE7,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x3C,0x00,0x00,//U 53
	0x00,0x00,0x00,0xE7,0x42,0x42,0x44,0x24,0x24,0x28,0x28,0x18,0x10,0x10,0x00,0x00,//V 54
	0x00,0x00,0x00,0xD6,0x92,0x92,0x92,0x92,0xAA,0xAA,0x6C,0x44,0x44,0x44,0x00,0x00,//W 55
	0x00,0x00,0x00,0xE7,0x42,0x24,0x24,0x18,0x18,0x18,0x24,0x24,0x42,0xE7,0x00,0x00,//X 56
	0x00,0x00,0x00,0xEE,0x44,0x44,0x28,0x28,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,//Y 57
	0x00,0x00,0x00,0x7E,0x84,0x04,0x08,0x08,0x10,0x20,0x20,0x42,0x42,0xFC,0x00,0x00,//Z 58
	0x00,0x1E,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x1E,0x00,//[ 59
	0x00,0x00,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x04,0x04,0x02,0x02,0x02,0x01,0x01,//\ 60
	0x00,0x78,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x78,0x00,//] 61
	0x00,0x08,0x14,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//^ 62
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,//_ 63
	0x00,0x60,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//` 64
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x1E,0x22,0x42,0x42,0x3F,0x00,0x00,//a 65
	0x00,0x00,0x00,0xC0,0x40,0x40,0x40,0x58,0x64,0x42,0x42,0x42,0x64,0x58,0x00,0x00,//b 66
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x22,0x40,0x40,0x40,0x22,0x1C,0x00,0x00,//c 67
	0x00,0x00,0x00,0x06,0x02,0x02,0x02,0x1E,0x22,0x42,0x42,0x42,0x26,0x1B,0x00,0x00,//d 68
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x7E,0x40,0x40,0x42,0x3C,0x00,0x00,//e 69
	0x00,0x00,0x00,0x0F,0x11,0x10,0x10,0x7E,0x10,0x10,0x10,0x10,0x10,0x7C,0x00,0x00,//f 70
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x44,0x44,0x38,0x40,0x3C,0x42,0x42,0x3C,//g 71
	0x00,0x00,0x00,0xC0,0x40,0x40,0x40,0x5C,0x62,0x42,0x42,0x42,0x42,0xE7,0x00,0x00,//h 72
	0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x70,0x10,0x10,0x10,0x10,0x10,0x7C,0x00,0x00,//i 73
	0x00,0x00,0x00,0x0C,0x0C,0x00,0x00,0x1C,0x04,0x04,0x04,0x04,0x04,0x04,0x44,0x78,//j 74
	0x00,0x00,0x00,0xC0,0x40,0x40,0x40,0x4E,0x48,0x50,0x68,0x48,0x44,0xEE,0x00,0x00,//k 75
	0x00,0x00,0x00,0x70,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x7C,0x00,0x00,//l 76
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF6,0x49,0x49,0x49,0x49,0x49,0xED,0x00,0x00,//m 77
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDC,0x62,0x42,0x42,0x42,0x42,0xE7,0x00,0x00,//n 78
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x42,0x42,0x3C,0x00,0x00,//o 79
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD8,0x64,0x42,0x42,0x42,0x64,0x58,0x40,0xE0,//p 80
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x22,0x42,0x42,0x42,0x26,0x1A,0x02,0x07,//q 81
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xEE,0x32,0x20,0x20,0x20,0x20,0xF8,0x00,0x00,//r 82
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x42,0x40,0x3C,0x02,0x42,0x7C,0x00,0x00,//s 83
	0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x7C,0x10,0x10,0x10,0x10,0x10,0x0C,0x00,0x00,//t 84
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC6,0x42,0x42,0x42,0x42,0x46,0x3B,0x00,0x00,//u 85
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE7,0x42,0x24,0x24,0x28,0x10,0x10,0x00,0x00,//v 86
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD7,0x92,0x92,0xAA,0xAA,0x44,0x44,0x00,0x00,//w 87
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6E,0x24,0x18,0x18,0x18,0x24,0x76,0x00,0x00,//x 88
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE7,0x42,0x24,0x24,0x28,0x18,0x10,0x10,0xE0,//y 89
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x44,0x08,0x10,0x10,0x22,0x7E,0x00,0x00,//z 90
	0x00,0x03,0x04,0x04,0x04,0x04,0x04,0x08,0x04,0x04,0x04,0x04,0x04,0x04,0x03,0x00,//{ 91
	0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,//| 92
	0x00,0x60,0x10,0x10,0x10,0x10,0x10,0x08,0x10,0x10,0x10,0x10,0x10,0x10,0x60,0x00,//} 93
	0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x52,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//~ 94
  };


  // OLED Font Data Header
// Contains font definitions for:
// - 6x8 ASCII
// - 8x16 ASCII 
// - 16x16 Chinese
// - 24x24 Chinese
// - 32x32 Chinese
// - Bitmap images

//常用ASCII表
//偏移量32
//ASCII字符集
//偏移量32
//大小:6*8
//逐行式，顺向（高位在前）
/************************************6*8的点阵************************************/
const unsigned char F6x8[][8] = {
	{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00}, // sp
	{0x10,0x10,0x10,0x10,0x00,0x10,0x00,0x00}, // !
	{0x28,0x28,0x28,0x00,0x00,0x00,0x00,0x00}, // "
	{0x28,0x28,0x7C,0x28,0x7C,0x28,0x28,0x00}, // #
	{0x10,0x3C,0x50,0x38,0x14,0x78,0x10,0x00}, // $
	{0x00,0x4C,0x2C,0x10,0x08,0x60,0x60,0x00}, // %
	{0x30,0x48,0x50,0x20,0x54,0x48,0x34,0x00}, // &
	{0x30,0x10,0x20,0x00,0x00,0x00,0x00,0x00}, // '
	{0x08,0x10,0x20,0x20,0x20,0x10,0x08,0x00}, // (
	{0x20,0x10,0x08,0x08,0x08,0x10,0x20,0x00}, // )
	{0x00,0x10,0x54,0x38,0x54,0x10,0x00,0x00}, // *
	{0x00,0x10,0x10,0x7C,0x10,0x10,0x00,0x00}, // +
	{0x00,0x00,0x00,0x00,0x00,0x18,0x08,0x10}, // ,
	{0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00}, // -
	{0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00}, // .
	{0x00,0x04,0x08,0x10,0x20,0x40,0x00,0x00}, // /
	{0x38,0x44,0x4C,0x54,0x64,0x44,0x38,0x00}, // 0
	{0x10,0x30,0x10,0x10,0x10,0x10,0x38,0x00}, // 1
	{0x38,0x44,0x04,0x08,0x10,0x20,0x7C,0x00}, // 2
	{0x7C,0x08,0x10,0x08,0x04,0x44,0x38,0x00}, // 3 
	{0x08,0x18,0x28,0x48,0x7C,0x08,0x08,0x00}, // 4
	{0x7C,0x40,0x78,0x04,0x04,0x44,0x38,0x00}, // 5
	{0x18,0x20,0x40,0x78,0x44,0x44,0x38,0x00}, // 6
	{0x7C,0x04,0x08,0x10,0x20,0x20,0x20,0x00}, // 7
	{0x38,0x44,0x44,0x38,0x44,0x44,0x38,0x00}, // 8
	{0x38,0x44,0x44,0x3C,0x04,0x08,0x30,0x00}, // 9
	{0x00,0x30,0x30,0x00,0x30,0x30,0x00,0x00}, // :
	{0x00,0x30,0x30,0x00,0x30,0x10,0x20,0x00}, // ;
	{0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x00}, // <
	{0x00,0x00,0x7C,0x00,0x7C,0x00,0x00,0x00}, // =
	{0x20,0x10,0x08,0x04,0x08,0x10,0x20,0x00}, // >
	{0x38,0x44,0x04,0x08,0x10,0x00,0x10,0x00}, // ?
	{0x38,0x44,0x04,0x34,0x5C,0x44,0x38,0x00}, // @
	{0x10,0x28,0x44,0x44,0x7C,0x44,0x44,0x00}, // A
	{0x78,0x44,0x44,0x78,0x44,0x44,0x78,0x00}, // B
	{0x38,0x44,0x40,0x40,0x40,0x44,0x38,0x00}, // C
	{0x70,0x48,0x44,0x44,0x44,0x48,0x70,0x00}, // D
	{0x7C,0x40,0x40,0x78,0x40,0x40,0x7C,0x00}, // E
	{0x7C,0x40,0x40,0x78,0x40,0x40,0x40,0x00}, // F
	{0x38,0x44,0x40,0x5C,0x44,0x44,0x3C,0x00}, // G
	{0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x00}, // H
	{0x38,0x10,0x10,0x10,0x10,0x10,0x38,0x00}, // I
	{0x1C,0x08,0x08,0x08,0x08,0x48,0x30,0x00}, // J
	{0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x00}, // K
	{0x40,0x40,0x40,0x40,0x40,0x40,0x7C,0x00}, // L
	{0x44,0x6C,0x54,0x54,0x44,0x44,0x44,0x00}, // M
	{0x44,0x44,0x64,0x54,0x4C,0x44,0x44,0x00}, // N
	{0x38,0x44,0x44,0x44,0x44,0x44,0x38,0x00}, // O
	{0x78,0x44,0x44,0x78,0x40,0x40,0x40,0x00}, // P
	{0x38,0x44,0x44,0x44,0x54,0x48,0x34,0x00}, // Q
	{0x78,0x44,0x44,0x78,0x50,0x48,0x44,0x00}, // R
	{0x3C,0x40,0x40,0x38,0x04,0x04,0x78,0x00}, // S
	{0x7C,0x10,0x10,0x10,0x10,0x10,0x10,0x00}, // T
	{0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00}, // U
	{0x44,0x44,0x44,0x44,0x44,0x28,0x10,0x00}, // V
	{0x44,0x44,0x44,0x54,0x54,0x54,0x28,0x00}, // W
	{0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x00}, // X
	{0x44,0x44,0x44,0x28,0x10,0x10,0x10,0x00}, // Y
	{0x7C,0x04,0x08,0x10,0x20,0x40,0x7C,0x00}, // Z
	{0x38,0x20,0x20,0x20,0x20,0x20,0x38,0x00}, // [
	{0x00,0x40,0x20,0x10,0x08,0x04,0x00,0x00}, // '\'
	{0x38,0x08,0x08,0x08,0x08,0x08,0x38,0x00}, // ]
	{0x10,0x28,0x44,0x00,0x00,0x00,0x00,0x00}, // ^
	{0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00}, // _
	{0x20,0x10,0x08,0x00,0x00,0x00,0x00,0x00}, // '
	{0x00,0x00,0x38,0x04,0x3C,0x44,0x3C,0x00}, // a
	{0x40,0x40,0x58,0x64,0x44,0x44,0x78,0x00}, // b
	{0x00,0x00,0x38,0x40,0x40,0x44,0x38,0x00}, // c
	{0x04,0x04,0x34,0x4C,0x44,0x44,0x3C,0x00}, // d
	{0x00,0x00,0x38,0x44,0x7C,0x40,0x38,0x00}, // e
	{0x38,0x24,0x20,0x70,0x20,0x20,0x20,0x00}, // f
	{0x00,0x00,0x3C,0x44,0x44,0x3C,0x04,0x38}, // g
	{0x40,0x40,0x58,0x64,0x44,0x44,0x44,0x00}, // h
	{0x10,0x00,0x30,0x10,0x10,0x10,0x38,0x00}, // i
	{0x08,0x00,0x18,0x08,0x08,0x08,0x48,0x30}, // j
	{0x40,0x40,0x48,0x50,0x60,0x50,0x48,0x00}, // k
	{0x30,0x10,0x10,0x10,0x10,0x10,0x38,0x00}, // l
	{0x00,0x00,0x68,0x54,0x54,0x44,0x44,0x00}, // m
	{0x00,0x00,0x58,0x64,0x44,0x44,0x44,0x00}, // n
	{0x00,0x00,0x38,0x44,0x44,0x44,0x38,0x00}, // o
	{0x00,0x00,0x58,0x64,0x64,0x58,0x40,0x40}, // p
	{0x00,0x00,0x34,0x4C,0x4C,0x34,0x04,0x04}, // q
	{0x00,0x00,0x58,0x64,0x40,0x40,0x40,0x00}, // r
	{0x00,0x00,0x3C,0x40,0x38,0x04,0x78,0x00}, // s
	{0x20,0x20,0x70,0x20,0x20,0x24,0x18,0x00}, // t
	{0x00,0x00,0x44,0x44,0x44,0x4C,0x34,0x00}, // u
	{0x00,0x00,0x44,0x44,0x44,0x28,0x10,0x00}, // v
	{0x00,0x00,0x44,0x44,0x54,0x54,0x28,0x00}, // w
	{0x00,0x00,0x44,0x28,0x10,0x28,0x44,0x00}, // x
	{0x00,0x00,0x44,0x44,0x44,0x3C,0x04,0x38}, // y
	{0x00,0x00,0x7C,0x08,0x10,0x20,0x7C,0x00}, // z
	{0x10,0x20,0x20,0x40,0x20,0x20,0x10,0x00}, // {
	{0x00,0x10,0x10,0x10,0x10,0x10,0x10,0x00}, // |
	{0x20,0x10,0x10,0x08,0x10,0x10,0x20,0x00}, // }
	{0x00,0x00,0x00,0x20,0x54,0x08,0x00,0x00}, // ~
};

static unsigned char epd_bitmap_logo[] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0xff, 0x00, 0x0f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xff, 0x00, 0x3f, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0xc2, 0x00, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x80, 0x01, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0x03, 0xff, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xff, 0x00, 0x0f, 0xff, 0x8c, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xfe, 0x00, 0x1f, 0xfe, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xfe, 0x00, 0x3f, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xfc, 0x00, 0x7f, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xfc, 0x01, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf8, 0x03, 0xff, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf8, 0x07, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf0, 0x0f, 0xfc, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf0, 0x1f, 0xf8, 0x07, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xf0, 0x3f, 0xe0, 0x3f, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xe0, 0x7f, 0xc0, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xc0, 0xff, 0x81, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x83, 0xfe, 0x07, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x87, 0xfc, 0x1f, 0xff, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x0f, 0xf0, 0x7f, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x3f, 0xc1, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x7f, 0x87, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x71, 0xfe, 0x1f, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x07, 0xe7, 0xf8, 0xff, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x07, 0xbf, 0xc3, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x07, 0xfc, 0x3f, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x01, 0xff, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x07, 0xef, 0xfd, 0xcf, 0x3f, 0x9f, 0xbf, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x1f, 0xef, 0x7b, 0x83, 0x7f, 0xf7, 0xff, 0xc0, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x70, 0xee, 0x77, 0x8f, 0xe7, 0xf7, 0x30, 0xc0, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x6f, 0x7f, 0xf3, 0xfd, 0xe0, 0xe0, 0x30, 0xc0, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x6f, 0x9c, 0xe0, 0xf1, 0xe1, 0xf2, 0x7f, 0x80, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x3f, 0xfd, 0xe3, 0xf8, 0xfc, 0xfc, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

static unsigned char epd_bitmap_sellectmode [] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x01, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x80, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 
	0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 
	0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 0x00, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x4f, 0x09, 0xfa, 0x10, 0x71, 0x0b, 0xf8, 0x1a, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x6b, 0x09, 0x93, 0x7e, 0x5f, 0xe8, 0x90, 0x1a, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x2f, 0xdc, 0xf1, 0x42, 0x5c, 0x7f, 0xfb, 0xff, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x1b, 0x08, 0xf0, 0x7e, 0x77, 0xc9, 0x08, 0x18, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0xff, 0xeb, 0x1f, 0x40, 0x20, 0x0d, 0xfb, 0xf8, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x65, 0x1c, 0x41, 0x7e, 0x3f, 0xff, 0x08, 0x88, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x6d, 0x49, 0xf9, 0x42, 0x21, 0x1d, 0xf8, 0x88, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x79, 0xc8, 0x41, 0x7e, 0x25, 0xd8, 0x40, 0xe9, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x60, 0x0b, 0xfb, 0x00, 0x39, 0x6b, 0xff, 0x0d, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x9f, 0xf8, 0x42, 0xff, 0xc3, 0x08, 0xa0, 0x07, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0b, 0x08, 0x00, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0xc0, 0x00, 0x00, 
	0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 
	0x00, 0x00, 0x01, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 
	0x00, 0x00, 0x01, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf0, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x01, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x80, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x07, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

#endif

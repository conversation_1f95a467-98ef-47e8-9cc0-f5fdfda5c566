name: Release

on:
  release:
    types: [published]
  workflow_dispatch:

jobs:
  build_docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Doxygen Action
        uses: mattnotmitt/doxygen-action@v1.9.1
        with:
          working-directory: 'docs/'

      - name: Deploy
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs/doxydocs/html
".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLE2904.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEAddress.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEAdvertisedDevice.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEAdvertisementData.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEAdvertising.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEAttValue.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEBeacon.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLECharacteristic.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEClient.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEDescriptor.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEDevice.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEEddystoneTLM.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEExtAdvertising.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEHIDDevice.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLERemoteCharacteristic.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLERemoteDescriptor.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLERemoteService.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLERemoteValueAttribute.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEScan.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEServer.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEService.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEUUID.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/NimBLEUtils.cpp.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/esp_port/esp-hci/src/esp_nimble_hci.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/esp_port/port/src/esp_nimble_mem.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/aes_decrypt.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/aes_encrypt.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/cbc_mode.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/ccm_mode.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/cmac_mode.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/ctr_mode.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/ctr_prng.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/ecc.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/ecc_dh.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/ecc_dsa.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/ecc_platform_specific.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/hmac.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/hmac_prng.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/sha256.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/ext/tinycrypt/src/utils.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_adv.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_conn.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_conn_hci.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_ctrl.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_dtm.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_hci.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_hci_ev.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_hci_vs.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_iso.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_rand.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_resolv.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_rfmgmt.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_scan.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_scan_aux.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_sched.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_supp_cmd.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_sync.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_trace.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_utils.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/controller/src/ble_ll_whitelist.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/drivers/nrf51/src/ble_hw.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/drivers/nrf51/src/ble_phy.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/drivers/nrf52/src/ble_hw.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/drivers/nrf52/src/ble_phy.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/drivers/nrf52/src/ble_phy_trace.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/access.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/adv.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/adv_ext.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/adv_legacy.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/aes-ccm.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/app_keys.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/beacon.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/cdb.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/cfg.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/cfg_cli.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/cfg_srv.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/crypto.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/friend.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/glue.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/health_cli.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/health_srv.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/heartbeat.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/light_model.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/lpn.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/mesh.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/model_cli.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/model_srv.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/msg.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/net.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/nodes.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/pb_adv.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/pb_gatt.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/pb_gatt_srv.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/prov.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/prov_device.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/provisioner.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/proxy.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/proxy_msg.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/proxy_srv.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/rpl.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/settings.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/shell.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/subnet.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/mesh/src/transport.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/services/ans/src/ble_svc_ans.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/services/bas/src/ble_svc_bas.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/services/gap/src/ble_svc_gap.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/services/gatt/src/ble_svc_gatt.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/services/ias/src/ble_svc_ias.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_aes_ccm.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_att.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_att_clt.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_att_cmd.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_att_svr.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_ead.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_eddystone.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_gap.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_gattc.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_gattc_cache.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_gattc_cache_conn.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_gatts.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_gatts_lcl.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_adv.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_atomic.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_cfg.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_conn.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_flow.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_hci.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_hci_cmd.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_hci_evt.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_hci_util.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_id.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_log.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_mbuf.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_misc.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_mqueue.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_periodic_sync.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_pvcy.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_resolv.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_shutdown.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_startup.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_hs_stop.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_ibeacon.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_l2cap.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_l2cap_coc.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_l2cap_sig.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_l2cap_sig_cmd.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_sm.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_sm_alg.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_sm_cmd.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_sm_lgcy.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_sm_sc.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_store.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_store_util.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/src/ble_uuid.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/store/config/src/ble_store_config.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/store/config/src/ble_store_config_conf.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/store/config/src/ble_store_nvs.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/host/util/src/addr.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/transport/src/monitor.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/nimble/transport/src/transport.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/porting/nimble/src/endian.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/porting/nimble/src/hal_timer.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/porting/nimble/src/mem.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/porting/nimble/src/nimble_port.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/porting/nimble/src/os_cputime.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/porting/nimble/src/os_cputime_pwr2.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/porting/nimble/src/os_mbuf.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/porting/nimble/src/os_mempool.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/porting/nimble/src/os_msys_init.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/porting/npl/freertos/src/nimble_port_freertos.c.o" ".pio/build/freenove_esp32_s3_wroom/lib7ab/NimBLE-Arduino/nimble/porting/npl/freertos/src/npl_os_freertos.c.o"
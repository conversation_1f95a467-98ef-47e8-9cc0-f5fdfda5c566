#include "spi_tle5012b.h"
#include "spi_config.h"
#include "driver/spi_common.h"
#include <algorithm> // For std::min
#include <Arduino.h>


// TLE5012B与SPI关联的句柄，通过此来调用SPI总线上的TLE5012B设备
spi_device_handle_t TLE5012B_SPI = NULL;
EN_AngleData_t EN_angleData;
uint16_t* EN_quickCaliDataPtr;
const int32_t EN_RESOLUTION = ((int32_t) ((0x00000001U) << 14));

static uint16_t tle5012b_spi_send_and_recv_uint16(spi_device_handle_t spi, uint16_t senddata, gpio_num_t cs_io_num)
{
    uint8_t temp = 0;
    esp_err_t ret;
    spi_transaction_t t;
    memset(&t, 0, sizeof(t));       // 清空传输结构体

    uint8_t p_data[2];              // 由于ESP32大小端的问题，传输uint16数据需要进行转换调换uint8_t的顺序
    p_data[0] = senddata>>8;
    p_data[1] = senddata;

    t.length=8*2;                   // SPI发送TLE5012B的CMD长度：16Bit。2个字节。
    t.tx_buffer=p_data;             // 命令是经过偶运算的addr，直接&addr会引起大小端错误
    t.flags = SPI_TRANS_USE_RXDATA;
    ret=spi_device_polling_transmit(spi, &t);  // 开始传输
    assert(ret==ESP_OK);            // 应该没有问题
    
    // 将接收到的数据按大小端重新排列
    temp = *t.rx_data;
    *t.rx_data = *(t.rx_data+1);
    *(t.rx_data+1) = temp;

    return *(uint16_t*)t.rx_data; // 返回经过重新大小端排列的数据
}

static uint32_t tle5012b_spi_send_and_recv_uint32(spi_device_handle_t spi, uint32_t senddata, gpio_num_t cs_io_num)
{
    uint8_t temp = 0;
    esp_err_t ret;
    spi_transaction_t t;
    memset(&t, 0, sizeof(t));       // 清空传输结构体

    uint8_t p_data[4];              // 由于ESP32大小端的问题，传输uint32数据需要进行转换调换uint8_t的顺序
    p_data[0] = senddata>>24;
    p_data[1] = senddata>>16;
    p_data[2] = senddata>>8;
    p_data[3] = senddata;

    t.length=8*4;                   // SPI发送TLE5012B长度：32Bit。4个字节。
    t.tx_buffer=p_data;             // 命令是经过偶运算的addr，直接&addr会引起大小端错误
    t.flags = SPI_TRANS_USE_RXDATA;
    ret=spi_device_polling_transmit(spi, &t);  // 开始传输
    assert(ret==ESP_OK);            // 应该没有问题

    // 将接收到的数据按大小端重新排列
    temp = *t.rx_data;
    *t.rx_data = *(t.rx_data+3);
    *(t.rx_data+3) = temp;

    temp = *(t.rx_data+1);
    *(t.rx_data+1) = *(t.rx_data+2);
    *(t.rx_data+2) = temp;

    return *(uint32_t*)t.rx_data; // 返回经过重新大小端排列的数据
}

uint16_t tle5012b_read_register_once(spi_device_handle_t spi, uint16_t addr, gpio_num_t cs_io_num)
{
    uint16_t data;

    // 软件CSn
    gpio_set_level(cs_io_num, 0);
    // 发送读命令和寄存地址
    tle5012b_spi_send_and_recv_uint16(spi, addr, cs_io_num); // 数据帧内的原始数据
    
    // 开始SPI通信，接收同时读取的数据
    data = tle5012b_spi_send_and_recv_uint16(spi, 0x0000, cs_io_num); // 数据帧内的原始数据
    // 软件CSn - reduced delay for faster operation
    gpio_set_level(cs_io_num, 1);

    data &= 0x7FFF; // 清除掉Bit15的1

    return data;
}



uint16_t tle5012b_read_angle(spi_device_handle_t spi, gpio_num_t cs_io_num)
{
    return tle5012b_read_register_once(spi, CMD_READ_ANGLE, cs_io_num);
}

uint16_t tle5012b_read_speed(spi_device_handle_t spi, gpio_num_t cs_io_num)
{
    return tle5012b_read_register_once(spi, CMD_READ_SPEED, cs_io_num);
}

void tle5012b_write_register(spi_device_handle_t spi, uint16_t addr, uint16_t data, gpio_num_t cs_io_num)
{
    // 软件CSn
    gpio_set_level(cs_io_num, 0);
    // 发送写命令和寄存地址
    tle5012b_spi_send_and_recv_uint16(spi, addr | 0x8000, cs_io_num); // 设置bit15为1表示写操作
    // 发送要写入的数据
    tle5012b_spi_send_and_recv_uint16(spi, data, cs_io_num);
    // 软件CSn
    gpio_set_level(cs_io_num, 1);
}

float tle5012_to_angle(uint16_t data)
{
    float angle = data;
    angle = angle*360/32768;
    return angle;
}

void spi_tle5012b_init(spi_host_device_t host_id, uint32_t clk_speed, gpio_num_t cs_io_num)
{
    esp_err_t ret;

    // Initialize SPI config safely
    spi_device_interface_config_t devcfg;
    memset(&devcfg, 0, sizeof(devcfg));
    
    // Only set essential parameters:
    devcfg.mode = 3;                // TLE5012B requires SPI mode 1 (CPOL=0, CPHA=1)
    // Limit to max 8MHz per TLE5012B specs
    devcfg.clock_speed_hz = std::min(static_cast<int>(clk_speed), 8000000);
    devcfg.spics_io_num = -1;       // Manual CS control
    devcfg.queue_size = 7;          // Transaction queue size

    // 将TLE5012B外设与SPI总线关联
    ret=spi_bus_add_device(host_id, &devcfg, &TLE5012B_SPI);
    ESP_ERROR_CHECK(ret);

    // 配置软件cs引脚
    gpio_pad_select_gpio(cs_io_num);
    gpio_set_direction(cs_io_num, GPIO_MODE_OUTPUT);
    gpio_set_level(cs_io_num, 1);

    // 上电后至少延时等待tpon=10ms。才可以进行SPI通信。
    vTaskDelay(200 / portTICK_PERIOD_MS);
}


uint16_t ENUpdateAngle()
{

    EN_angleData.rawAngle = tle5012_to_angle(tle5012b_read_angle(TLE5012B_SPI,(gpio_num_t)12));   //将提取到的原始角度数据 rawAngle 存储到 EN_angleData.rawAngle 中
    //使用存储的校准数据（EN_quickCaliDataPtr[EN_angleData.rawAngle]）对原始角度进行修正，得到校准后的角度，并将其存储在 EN_angleData.rectifiedAngle 中
    EN_angleData.rectifiedAngle = EN_quickCaliDataPtr[EN_angleData.rawAngle];  
    return EN_angleData.rectifiedAngle;//返回修正后的角度
}


bool ENInit()  //初始化编码器的校准数据
{
  EN_quickCaliDataPtr = (uint16_t*) MT_ENCODER_BEGIN_ADDR;  //存储编码器数据的起始地址

  ENUpdateAngle();   //更新编码器位置

  // Check if the stored calibration data are valid
  EN_angleData.rectifyValid = true;
  for (uint32_t i = 0; i < EN_RESOLUTION; i++)
  {
    if (EN_quickCaliDataPtr[i] == 0xFFFF)
      EN_angleData.rectifyValid = false;
  }
  return EN_angleData.rectifyValid;
}

bool ENIsCalibrated()
{
  return EN_angleData.rectifyValid;
}

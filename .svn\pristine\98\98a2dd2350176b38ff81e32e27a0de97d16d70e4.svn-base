/**
 * @file qma6100p.h
 * @brief QMA6100P三轴加速度计驱动头文件
 * @details 基于I2C通信的QMA6100P加速度计驱动程序
 *          支持多种量程设置和数据读取功能
 */
#ifndef QMA6100P_H
#define QMA6100P_H

#include "driver/i2c.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"
#include <math.h>

#define I2C_MASTER_SCL_IO          48    ///< I2C时钟线引脚
#define I2C_MASTER_SDA_IO          47    ///< I2C数据线引脚
#define QMA6100P_INT_PIN           4     ///< QMA6100P中断引脚
#define I2C_MASTER_NUM             I2C_NUM_0        ///< I2C端口号
#define I2C_MASTER_FREQ_HZ         400000           ///< I2C通信频率
#define I2C_MASTER_TX_BUF_DISABLE  0                ///< 禁用I2C发送缓冲区
#define I2C_MASTER_RX_BUF_DISABLE  0                ///< 禁用I2C接收缓冲区

#define QMA6100P_ADDR              0x12             ///< QMA6100P I2C地址

extern float qma6100_x; ///< X轴加速度全局变量

/**
 * @brief QMA6100P原始数据结构体
 */
typedef struct {
    int16_t temp;   ///< 温度数据
    int16_t acc_x;  ///< X轴加速度原始数据
    int16_t acc_y;  ///< Y轴加速度原始数据
    int16_t acc_z;  ///< Z轴加速度原始数据
} QMA6100PRawData_t;

/**
 * @brief QMA6100P灵敏度枚举
 * @details 不同量程对应的灵敏度值
 */
typedef enum {
    QMA6100P_SENSITITY_2G = 244,    ///< ±2G量程灵敏度
    QMA6100P_SENSITITY_4G = 488,    ///< ±4G量程灵敏度
    QMA6100P_SENSITITY_6G = 360,    ///< ±6G量程灵敏度
    QMA6100P_SENSITITY_8G = 977,    ///< ±8G量程灵敏度
    QMA6100P_SENSITITY_16G = 1950,  ///< ±16G量程灵敏度
    QMA6100P_SENSITITY_32G = 3910   ///< ±32G量程灵敏度
} qma6100p_sensitity;

/**
 * @brief 初始化QMA6100P加速度计
 * @return ESP_OK表示成功，其他值表示失败
 * @details 配置I2C通信和QMA6100P寄存器
 */
esp_err_t qma6100p_init();

/**
 * @brief 启动QMA6100P定时器
 * @param timer 定时器句柄指针
 * @details 启动定时器用于定期读取加速度数据
 */
void qma6100p_start_timer(TimerHandle_t *timer);

/**
 * @brief 停止QMA6100P定时器
 * @param timer 定时器句柄
 */
void qma6100p_stop_timer(TimerHandle_t timer);

/**
 * @brief 获取QMA6100P加速度数据
 * @param x X轴加速度输出指针
 * @param y Y轴加速度输出指针
 * @param z Z轴加速度输出指针
 * @param g 重力加速度输出指针
 * @details 读取并转换加速度计的原始数据为实际物理值
 */
void qma6100p_get_data(float *x, float *y, float *z, float *g);

/**
 * @brief 读取QMA6100P原始数据
 * @details 从QMA6100P寄存器读取原始的加速度和温度数据
 */
void qma6100p_read_data();

#endif // QMA6100P_H

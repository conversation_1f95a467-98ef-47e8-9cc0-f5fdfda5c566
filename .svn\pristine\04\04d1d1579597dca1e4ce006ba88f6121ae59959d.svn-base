#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <WiFi.h>

/**
 * @brief WiFi管理类
 * @details 负责WiFi连接的初始化和管理
 */
class WiFiManager {
public:
    /**
     * @brief WiFi管理器构造函数
     * @param ssid WiFi网络名称
     * @param password WiFi密码
     */
    WiFiManager(const char* ssid, const char* password);

    /**
     * @brief 开始WiFi连接
     * @details 初始化WiFi并尝试连接到指定网络
     */
    void begin();

    /**
     * @brief WiFi任务函数
     * @param parameter FreeRTOS任务参数
     * @details 在独立任务中管理WiFi连接状态
     */
    void taskWiFi(void* parameter);

private:
    const char* _ssid;      ///< WiFi网络名称
    const char* _password;  ///< WiFi密码
};

#endif

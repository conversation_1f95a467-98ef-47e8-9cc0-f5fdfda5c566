#include <Arduino.h>
#include "driver/gpio.h"
#include "driver/uart.h"
#include "wifi_manager.h"
#include "ble_manager.h"
#include "motor_control.h"
#include "pid_controller.h"
#include "encoder.h"
#include "spi_config.h"
#include "spi_tle5012b.h"
#include "preferences_manager.h"
#include "serial_command.h"
#include <Wire.h>  // I2C库
#include <oled.h>
#include "key.h"
#include "qma6100p.h"

// #define BLE_DEVICE_NAME "ESP32-Stepper"
const char *WIFI_SSID = "OnePlus 10 Pro";
const char *WIFI_PASSWORD = "123456789";
uint8_t uart_256mocro_data[] = {
  0x05, 0x00, 0x80, 0x00, 0x00, 0x01, 0xC1, 0x7F
};
uint8_t uart_256mocro_data1[] = {
  0x05, 0x00, 0xec, 0x10, 0x01, 0x00, 0x53, 0x97
};
uint8_t uart_256mocro_data2[] = {
  0x05, 0x01, 0x80, 0x00, 0x00, 0x01, 0xC1, 0x93
};
uint8_t uart_256mocro_data3[] = {
  0x05, 0x01, 0xec, 0x10, 0x01, 0x00, 0x53, 0x7B
};

uint8_t uart_256mocro_data4[] = {
  0x05, 0x02, 0x80, 0x00, 0x00, 0x01, 0xC1, 0x09
};
uint8_t uart_256mocro_data5[] = {
  0x05, 0x02, 0xec, 0x10, 0x01, 0x00, 0x53, 0xE1
};

uint8_t uart_256mocro_data6[] = {
  0x05, 0x03, 0x80, 0x00, 0x00, 0x01, 0xC1, 0xE5
};
uint8_t uart_256mocro_data7[] = {
  0x05, 0x03, 0xec, 0x10, 0x01, 0x00, 0x53, 0x0D
};

extern uint16_t tle_angle;

MotorControl motor;
Encoder encoder;
PIDController pid(0.8, 0.005, 0.05);
SerialCommand command;
extern int mode_show;
extern bool motormode;
extern float motor_speed;
extern int motor_steps;
HardwareSerial MySerial_2(1); // UART2
HardwareSerial MySerial_1(0); // UART0
HardwareSerial MySerial_0(2); // UART1
// 接收缓冲区配置
#define MAX_RX_LEN 64
char rxBuffer[MAX_RX_LEN];
uint8_t bufferIndex = 0;
volatile bool stop_Bit = true; 

// extern mode_slect mode_show;
void send_256sub_command();
void send_uart2_receive_uart1();
void send_uart2();
void receive_uart1();
void send_uart1_receive();

void oledTask(void *pvParameters);
void commandTask(void* pvParameters);

// void networkTask(void *pvParameters) {
//     WiFiManager wifi(WIFI_SSID, WIFI_PASSWORD);
//     // BLEManager ble(BLE_DEVICE_NAME);

//     // ble.begin();

    
//     wifi.begin();
//     while(true) {
//         //printf("WiFi status: %s\n", WiFi.status() == WL_CONNECTED ? "Connected" : "Disconnected");
//         vTaskDelay(5000 / portTICK_PERIOD_MS);
//     }
// }


// HardwareSerial MySerial(3); // UART1
static int last_mode = -1;
void oledTask(void *pvParameters) 
{
  vTaskDelay(1000 / portTICK_PERIOD_MS); // 延迟1秒给WiFi/BLE初始化
  spi_master_init(SPI3_HOST,SPI_DMA_CH_AUTO,4092,SPI2_DEF_PIN_NUM_MISO,SPI2_DEF_PIN_NUM_MOSI,SPI2_DEF_PIN_NUM_CLK);
  OLED_Init_GPIO();
  spi_tle5012b_init(SPI3_HOST,1000000,TLE5012B_SOFT_CS0);
  OLED_Init();
  OLED_Clear(0x00);  
    mode_show = 1;
    while(true) {
      if(mode_show != last_mode) { // 仅模式变化时刷新
        last_mode = mode_show;

        switch (mode_show)
        {
          case 1: show_start(); break;
          case 2: GPIO.out_w1tc = (1 << 10); // 置高
                  show_stellar_mode();
                  OLED_ShowNum(60,40,0,10,8,1);
                  //  OLED_ShowNum(60,25,0,10,8,1);
                  // motor.setspeedmode();
                  // motor.setOutput(20.15926);  
                  break;
          case 3: GPIO.out_w1tc = (1 << 10); // 置高
                  show_sun_mode();
                  OLED_ShowNum(60,40,0,10,8,1);
                  //  OLED_ShowNum(60,25,0,10,8,1);
                  // motor.setspeedmode();
                  // motor.setOutput(-10.0595832); 
                  break;  
          case 4: GPIO.out_w1tc = (1 << 10); // 置高
                  show_moon_mode();
                  OLED_ShowNum(60,40,0,10,8,1);
                  // OLED_ShowNum(60,25,0,10,8,1);
                  // motor.setpositionmode();
                  // motor.setPosition(100);
                  // motor.updatePositionControl();  
                  break; 
        
        default:  show_stop_mode();
                  OLED_ShowNum(60,40,0,10,8,1);
                  // OLED_ShowNum(60,25,0,10,8,1);
                  motor.setspeedmode();
                  // motor.setOutput(0);         
                  break;
        }
 
      }
      if(mode_show == 2 | mode_show == 3 | mode_show == 4 | mode_show == 5) {
        OLED_ShowNum(60,40,tle_angle,10,8,1);
        // OLED_ShowNum(60,25,abs(curr_speed),10,8,1);
        OLED_Display();
      }
      if (qma6100_x > 0) {OLED_Set_Flip(1);} // 倒过来显示
      else {OLED_Set_Flip(0);} // 正常显示
      

       tle_angle = tle5012b_read_angle(TLE5012B_SPI, TLE5012B_SOFT_CS0); 
       vTaskDelay(50 / portTICK_PERIOD_MS); // 确保SPI操作完成
       vTaskDelay(1);
    }
    
}

void commandTask(void* pvParameters) {
  while (1) {
    command.process(pid, encoder, motor);
    motor.updatePositionControl();
    // Serial.println("speeed \n");
    // Serial.print(curr_speed);
    vTaskDelay(pdMS_TO_TICKS(50)); // 缩短为50ms间隔
  }
}


void setup() {
    //command.setup();
    Serial.begin(115200);
    send_256sub_command();
    send_uart2_receive_uart1();
    send_uart1_receive();
    receive_uart1();
    send_uart2();

    vTaskDelay(1000 / portTICK_PERIOD_MS); // Send every second
    //printf("System initialized\n");
   

    // Redirect stdout
    fflush(stdout);
    setvbuf(stdout, NULL, _IONBF, 0);
    //printf("System starting...\n");
    
    // Initialize motor control
    motor.init();
    esp_err_t ret = qma6100p_init();
    if (ret != ESP_OK) {
        //Serial.print("QMA6100P initialization failed");
        // while(1);
    }
    
    

  // Start OLED task on core 1 
xTaskCreatePinnedToCore(
  oledTask,
  "oled_task",
  8192,
  NULL,
  2,
  NULL,
  1
);

if(stop_Bit){
    // Start network task on core 0 
xTaskCreatePinnedToCore(
    bleTask,      // 任务函数
    "BLE_Task",   // 任务名称
    10000,        // 堆栈大小
    NULL,         // 参数
    3,            // 优先级
    NULL,         // 任务句柄
    0             // 核心号 (0 或 1)
  );
}

// xTaskCreatePinnedToCore(
//         taskWiFi,
//         "taskWiFi",
//         4096,  // Increased stack size to prevent crashes
//         NULL,
//         2,
//         NULL,
//         0
//     );
//printf("Setup complete\n");

    // 创建按键检测任务
    xTaskCreatePinnedToCore(
        key_task,
        "key_task",
        4096,
        NULL,
        1,  // 优先级
        NULL,
        0   // 核心0
    );

    // 创建按键检测任务
    xTaskCreatePinnedToCore(
        commandTask,
        "CommandTask",
        4096,
        NULL,
        4,  // 优先级
        NULL,
        1   // 核心0
    );
}

void loop() {
   
  
  // while(1)
  // {
    // motor.updatePositionControl();
    // Serial.println("speeed");
    // Serial.print(curr_speed);
  //   command.process(pid,encoder,motor);
  //   vTaskDelay(pdMS_TO_TICKS(100)); // 更推荐这样写
  // }
  // //delay(100);
    //  qma6100p_read_data();
    //  MySerial_0.print(curr_speed);
    if(motormode)
    {
      motor.setpositionmode();
      motor.setPosition(motor_steps);
      motor.updatePositionControl();
    }
    else{
      motor.setspeedmode();
      motor.setOutput(motor_speed);
    }
    // Serial.println("speeed\n");
    // Serial.print(motor_speed);
    vTaskDelay(150 / portTICK_PERIOD_MS);
}



 


void send_256sub_command()
{
    MySerial_2.begin(115200, SERIAL_8N1, -1, 46);
    //printf("UART1 initialized on pin 16\n");
    delay(500);
    for(int i=0;i<8;i++)
    {
      //MySerial.write(&uart_256mocro_data[i], sizeof(uart_256mocro_data[i]));
      if(i==0)
      {
        MySerial_2.write(uart_256mocro_data,sizeof(uart_256mocro_data));
      }
      else if(i==1){
        MySerial_2.write(uart_256mocro_data1,sizeof(uart_256mocro_data1));
      }
      else if(i==2){
        MySerial_2.write(uart_256mocro_data2,sizeof(uart_256mocro_data2));
      }
      else if(i==3){
        MySerial_2.write(uart_256mocro_data3,sizeof(uart_256mocro_data3));
      }
      else if(i==4){
        MySerial_2.write(uart_256mocro_data4,sizeof(uart_256mocro_data4));
      }
      else if(i==5){
        MySerial_2.write(uart_256mocro_data5,sizeof(uart_256mocro_data5));
      }
      else if(i==6){
        MySerial_2.write(uart_256mocro_data6,sizeof(uart_256mocro_data6));
      }
      else if(i==7){
        MySerial_2.write(uart_256mocro_data7,sizeof(uart_256mocro_data7));
      }

     // MySerial.write(uart_256mocro_data,sizeof(uart_256mocro_data));
      delay(100);
      //MySerial.write(uart_256mocro_data, sizeof(uart_256mocro_data));

    }
}

void send_uart2_receive_uart1()
{

  MySerial_0.begin(38400, SERIAL_8N1, 43, 44); // 指定 TX=44, RX=43
 
}


void send_uart1_receive()
{

  MySerial_1.begin(38400, SERIAL_8N1, 17, 18); // 指定 RX=18, TX=17
  
}

void send_uart2()
{
  MySerial_0.println("q"); // 测试发送指令
}

void receive_uart1()
{
  while (MySerial_1.available() > 0) {
    char c = MySerial_1.read();
    
    // 处理换行符或缓冲区满
    if (c == '\n' || bufferIndex >= MAX_RX_LEN-1) {
      rxBuffer[bufferIndex] = '\0'; // 终止字符串
      
    
      
      // 检查关键指令
      if (strstr(rxBuffer, "stop_wifi") != NULL) {
        stop_Bit = false;
        // Serial.println("Trigger STOP command!");
      }
      
      bufferIndex = 0; // 重置缓冲区
    } else {
      rxBuffer[bufferIndex++] = c;
    }
  }
}
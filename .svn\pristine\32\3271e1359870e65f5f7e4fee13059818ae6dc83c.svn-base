name: Build

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - master

jobs:
  build_esp_pio_legacy:
    strategy:
      fail-fast: false
      matrix:
        example:
          - "examples/NimBLE_Client"
          - "examples/NimBLE_Server"
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"
      - name: Install platformio
        run: |
          python -m pip install --upgrade pip
          pip install platformio
      - name: Build esp PIO
        run: |
          mkdir -p example/lib
          rsync -Rr . example/lib/
          mkdir example/src
          cat > example/platformio.ini << EOF
            [env]
            platform = espressif32
            framework = arduino

            [env:esp32dev]
            board = esp32dev

            [env:esp32c3]
            board = esp32-c3-devkitm-1

            [env:esp32s3]
            board = esp32-s3-devkitc-1
          EOF
          cp ${{ matrix.example }}/* example/src/
          platformio run -d example

  build_bt5_esp_pio_legacy:
    strategy:
      fail-fast: false
      matrix:
        example:
          - "examples/Bluetooth_5/NimBLE_extended_client"
          - "examples/Bluetooth_5/NimBLE_extended_server"
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"
      - name: Install platformio
        run: |
          python -m pip install --upgrade pip
          pip install platformio
      - name: Build esp PIO
        run: |
          mkdir -p example/lib
          rsync -Rr . example/lib/
          mkdir example/src
          cat > example/platformio.ini << EOF
            [env]
            platform = espressif32
            framework = arduino

            [env:esp32c3]
            board = esp32-c3-devkitm-1
            build_flags = '-DCONFIG_BT_NIMBLE_EXT_ADV=1'

            [env:esp32s3]
            board = esp32-s3-devkitc-1
            build_flags = '-DCONFIG_BT_NIMBLE_EXT_ADV=1'
          EOF
          cp ${{ matrix.example }}/* example/src/
          platformio run -d example

  build_esp_pioarduino:
    strategy:
      fail-fast: false
      matrix:
        example:
          - "examples/NimBLE_Client"
          - "examples/NimBLE_Server"
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"
      - name: Install platformio
        run: |
          python -m pip install --upgrade pip
          pip install platformio
      - name: Build esp PIO
        run: |
          mkdir -p example/lib
          rsync -Rr . example/lib/
          mkdir example/src
          cat > example/platformio.ini << EOF
            [env]
            platform = https://github.com/pioarduino/platform-espressif32/releases/download/stable/platform-espressif32.zip
            framework = arduino

            [env:esp32dev]
            board = esp32dev

            [env:esp32c3]
            board = esp32-c3-devkitm-1

            [env:esp32s3]
            board = esp32-s3-devkitc-1

            [env:esp32c6]
            board = esp32-c6-devkitc-1

            [env:esp32h2]
            board = esp32-h2-devkitm-1
          EOF
          cp ${{ matrix.example }}/* example/src/
          platformio run -d example

  build_bt5_esp_pioarduino:
    strategy:
      fail-fast: false
      matrix:
        example:
          - "examples/Bluetooth_5/NimBLE_extended_client"
          - "examples/Bluetooth_5/NimBLE_extended_server"
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"
      - name: Install platformio
        run: |
          python -m pip install --upgrade pip
          pip install platformio
      - name: Build esp PIO
        run: |
          mkdir -p example/lib
          rsync -Rr . example/lib/
          mkdir example/src
          cat > example/platformio.ini << EOF
            [env]
            platform = https://github.com/pioarduino/platform-espressif32/releases/download/stable/platform-espressif32.zip
            framework = arduino

            [env:esp32c3]
            board = esp32-c3-devkitm-1
            build_flags = '-DCONFIG_BT_NIMBLE_EXT_ADV=1'

            [env:esp32s3]
            board = esp32-s3-devkitc-1
            build_flags = '-DCONFIG_BT_NIMBLE_EXT_ADV=1'

            [env:esp32c6]
            board = esp32-c6-devkitc-1
            build_flags = '-DCONFIG_BT_NIMBLE_EXT_ADV=1'

            [env:esp32h2]
            board = esp32-h2-devkitm-1
            build_flags = '-DCONFIG_BT_NIMBLE_EXT_ADV=1'
          EOF
          cp ${{ matrix.example }}/* example/src/
            platformio run -d example

  build_arduino-esp32:
    strategy:
      fail-fast: false
      matrix:
        example:
          - "examples/NimBLE_Client"
          - "examples/NimBLE_Server"
        variant:
          - esp32
          - esp32c3
          - esp32s3
          - esp32c6
          - esp32h2
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Build arduino-esp32
        uses: arduino/compile-sketches@v1.1.0
        with:
          cli-version: latest
          platforms: |
            - name: "esp32:esp32"
              source-url: "https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json"
              version: latest
          fqbn: "esp32:esp32:${{ matrix.variant }}"
          sketch-paths: ${{ matrix.example }}

  build_bt5_arduino-esp32:
    strategy:
      fail-fast: false
      matrix:
        example:
          - "examples/Bluetooth_5/NimBLE_extended_client"
          - "examples/Bluetooth_5/NimBLE_extended_server"
        variant:
          - esp32c3
          - esp32s3
          - esp32c6
          - esp32h2
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set config for BT5
        run: |
          sed -i '0,/#include \"nimconfig_rename.h\"/a #define CONFIG_BT_NIMBLE_EXT_ADV 1' ./src/nimconfig.h
      - name: Build BT5 arduino-esp32
        uses: arduino/compile-sketches@v1.1.0
        with:
          cli-version: latest
          platforms: |
            - name: "esp32:esp32"
              source-url: "https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json"
              version: latest
          fqbn: "esp32:esp32:${{ matrix.variant }}"
          sketch-paths: ${{ matrix.example }}

  build_n-able-Arduino:
    strategy:
      fail-fast: false
      matrix:
        example:
          - "examples/NimBLE_Client"
          - "examples/NimBLE_Server"
        variant:
          - Generic_nRF51822:chip=xxaa
          - Generic_nRF52832
          - Generic_nRF52833
          - Generic_nRF52840
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Build n-able Arduino
        uses: arduino/compile-sketches@v1.1.0
        with:
          cli-version: latest
          platforms: |
            - name: "h2zero:arm-ble"
              source-url: "https://h2zero.github.io/n-able-Arduino/package_n-able_boards_index.json"
              version: latest
          fqbn: "h2zero:arm-ble:${{ matrix.variant }}"
          sketch-paths: ${{ matrix.example }}

  build_bt5_n-able-Arduino:
    strategy:
      fail-fast: false
      matrix:
        example:
          - "examples/Bluetooth_5/NimBLE_extended_client"
          - "examples/Bluetooth_5/NimBLE_extended_server"
        variant:
          - Generic_nRF52840
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set config for BT5
        run: |
          echo "-DCONFIG_BT_NIMBLE_EXT_ADV=1" > ${{ matrix.example }}/build_opt.h
      - name: Build BT5 n-able Arduino
        uses: arduino/compile-sketches@v1.1.0
        with:
          cli-version: latest
          platforms: |
            - name: "h2zero:arm-ble"
              source-url: "https://h2zero.github.io/n-able-Arduino/package_n-able_boards_index.json"
              version: latest
          fqbn: "h2zero:arm-ble:${{ matrix.variant }}"
          sketch-paths: ${{ matrix.example }}

  build_n-able-pio:
    strategy:
      fail-fast: false
      matrix:
        example:
          - "examples/NimBLE_Client"
          - "examples/NimBLE_Server"
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"
      - name: Install platformio
        run: |
          python -m pip install --upgrade pip
          pip install platformio
      - name: Build esp PIO
        run: |
          mkdir -p example/lib
          rsync -Rr . example/lib/
          mkdir example/src
          cat > example/platformio.ini << EOF
            [env]
            platform = https://github.com/h2zero/platform-n-able.git#1.1.1
            framework = arduino

            [env:generic_nrf51822_xxaa
            board = generic_nrf51822_xxaa

            [env:generic_nrf52832]
            board = generic_nrf52832

            [env:generic_nrf52833]
            board = generic_nrf52833

            [env:generic_nrf52840]
            board = generic_nrf52840
          EOF
          cp ${{ matrix.example }}/* example/src/
          platformio run -d example

  build_bt5_n-able-pio:
    strategy:
      fail-fast: false
      matrix:
        example:
          - "examples/Bluetooth_5/NimBLE_extended_client"
          - "examples/Bluetooth_5/NimBLE_extended_server"
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"
      - name: Install platformio
        run: |
          python -m pip install --upgrade pip
          pip install platformio
      - name: Build esp PIO
        run: |
          mkdir -p example/lib
          rsync -Rr . example/lib/
          mkdir example/src
          cat > example/platformio.ini << EOF
            [env]
            platform = https://github.com/h2zero/platform-n-able.git#1.1.1
            framework = arduino

            [env:generic_nrf52840]
            board = generic_nrf52840
            build_flags = '-DCONFIG_BT_NIMBLE_EXT_ADV=1'
          EOF
          cp ${{ matrix.example }}/* example/src/
          platformio run -d example

  build_docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Doxygen Action
        uses: mattnotmitt/doxygen-action@v1.9.8
        with:
          working-directory: 'docs/'

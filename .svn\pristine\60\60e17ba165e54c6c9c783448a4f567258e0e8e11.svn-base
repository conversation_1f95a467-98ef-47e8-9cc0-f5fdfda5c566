.pio/build/esp32-s3-devkitm-1/libcff/NimBLE-Arduino/NimBLERemoteService.cpp.o: \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLERemoteService.cpp \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimconfig.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/qio_qspi/include/sdkconfig.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimconfig_rename.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLERemoteService.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEClient.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEAddress.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/include/nimble/ble.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/syscfg/syscfg.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/esp_port/port/include/esp_nimble_cfg.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/os/os.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/os/../syscfg/syscfg.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/include/nimble/nimble_npl.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/FreeRTOS.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos/FreeRTOSConfig.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_compiler.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/FreeRTOSConfig_arch.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/xtensa_config.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/hal.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa-versions.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core-isa.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core-matmap.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/tie.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/system.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/xtensa_context.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa_context.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/corebits.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime-frames.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_rom/include/esp_rom_sys.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/reset_reasons.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_rom/include/esp32s3/rom/ets_sys.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/soc.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_assert.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_bit_defs.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/projdefs.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/portable.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/deprecated_definitions.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/portmacro.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/specreg.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime-core-state.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xt_instr_macros.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/soc/spinlock.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/soc/cpu.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_cpu.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/cpu_hal.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_err.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_compiler.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/soc_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/brownout_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/cpu_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/i2c_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/ledc_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/mpu_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/twai_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/cpu_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/esp32s3/include/hal/cpu_ll.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/extreg.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_attr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/soc/compare_set.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/include/soc/soc_memory_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_system/include/esp_private/crosscore_int.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_timer/include/esp_timer.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/newlib/platform_include/esp_newlib.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/heap/include/esp_heap_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/heap/include/multi_heap.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_system/include/esp_system.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_idf_version.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_mac.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_chip_info.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_random.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/portbenchmark.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa_api.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa_context.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/portmacro_deprecated.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/mpu_wrappers.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/queue.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/task.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/list.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos/task_snapshot.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/task.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/semphr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/queue.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/timers.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/npl/freertos/include/nimble/npl_freertos.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/os/endian.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/os/queue.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/os/os_error.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/os/os.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/os/os_mbuf.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/os/os_mempool.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEUUID.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_uuid.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEUtils.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_gap.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/include/nimble/hci_common.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/include/nimble/ble.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_att.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/os/queue.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_eddystone.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_gap.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_gatt.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_uuid.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_adv.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_id.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/include/nimble/ble.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_hci.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_log.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/modlog/modlog.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/modlog/../log_common/log_common.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/modlog/../log_common/ignore.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/modlog/../log/log.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/log/include/esp_log.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/log/include/esp_log_internal.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/log/log.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/logcfg/logcfg.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/logcfg/../modlog/modlog.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/logcfg/../log_common/log_common.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_mbuf.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_stop.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_ibeacon.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_l2cap.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/include/nimble/nimble_opt.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/include/nimble/nimble_opt_auto.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_sm.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_store.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEConnInfo.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEAttValue.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Arduino.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp_arduino_version.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_sleep.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/touch_sensor_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/gpio_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/include/soc/gpio_periph.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/io_mux_reg.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/gpio_struct.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/gpio_reg.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/gpio_sig_map.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/event_groups.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/timers.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-log.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-matrix.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-uart.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/uart_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-gpio.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/variants/esp32s3/pins_arduino.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-touch.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-dac.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/driver/include/driver/gpio.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_intr_alloc.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_rom/include/esp32s3/rom/gpio.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-adc.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-spi.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-i2c.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-ledc.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-rmt.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-sigmadelta.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-timer.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-bt.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-psram.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-rgb-led.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-cpu.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp8266-compat.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/stdlib_noniso.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/binary.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WCharacter.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WString.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/pgmspace.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Stream.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Print.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Printable.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/IPAddress.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WString.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Printable.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Client.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Server.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Udp.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Stream.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/IPAddress.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/HardwareSerial.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/HWCDC.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_event/include/esp_event.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_event/include/esp_event_base.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_event/include/esp_event_legacy.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_wifi/include/esp_wifi_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_wifi/include/esp_private/esp_wifi_types_private.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_interface.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_event/include/esp_event_base.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_netif/include/esp_netif.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_ip_addr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_defaults.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_netif_glue.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_eth/include/esp_eth.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_com.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/eth_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_mac.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_phy.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/tcpip_adapter/include/tcpip_adapter.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/tcpip_adapter/include/tcpip_adapter_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip_addr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/opt.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/lwipopts.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/newlib/platform_include/sys/ioctl.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_system/include/esp_task.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos/FreeRTOSConfig.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/netif/dhcp_state.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/sntp/sntp_get_set_time.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/debug.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/arch.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch/cc.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch/sys_arch.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch/vfs_lwip.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/def.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip4_addr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip6_addr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/def.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip6_zone.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/include/apps/dhcpserver/dhcpserver.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/err.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_sta_list.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/USBCDC.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Esp.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/spi_flash/include/esp_partition.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/spi_flash/include/esp_flash.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/spi_flash_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/esp_flash_err.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/spi_flash/include/esp_spi_flash.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/spi_flash/include/esp_spi_flash_counters.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32/spiram.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/io_pin_remap.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Arduino.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLELog.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include/syscfg/syscfg.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/console/console.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEAdvertisedDevice.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEScan.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_adv.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLERemoteCharacteristic.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLERemoteDescriptor.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEDevice.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEAdvertising.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEServer.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEService.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLECharacteristic.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLEDescriptor.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLE2904.h \
 .pio/libdeps/esp32-s3-devkitm-1/NimBLE-Arduino/src/NimBLESecurity.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/include/esp32c3/include/esp_bt.h

/**
 * @file preferences_manager.h
 * @brief 参数管理器头文件
 * @details 基于ESP32 NVS存储的参数管理类，用于持久化存储系统配置参数
 */
#ifndef PREFERENCES_MANAGER_H
#define PREFERENCES_MANAGER_H

#include "Preferences.h"

/**
 * @brief 参数管理器类
 * @details 封装ESP32 Preferences库，提供简化的参数存储和读取接口
 */
class PreferencesManager {
public:
    /**
     * @brief 开始参数管理会话
     * @details 初始化NVS存储，准备进行参数读写操作
     */
    void begin();

    /**
     * @brief 结束参数管理会话
     * @details 关闭NVS存储，释放相关资源
     */
    void end();

    /**
     * @brief 读取浮点型参数
     * @param key 参数键名
     * @param defaultValue 默认值（当参数不存在时返回）
     * @return 参数值或默认值
     */
    float getFloat(const char* key, float defaultValue);

    /**
     * @brief 写入浮点型参数
     * @param key 参数键名
     * @param value 要写入的参数值
     */
    void putFloat(const char* key, float value);

private:
    Preferences prefs; ///< ESP32 Preferences实例
};

#endif

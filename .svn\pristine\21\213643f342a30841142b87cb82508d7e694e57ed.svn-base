#include "serial_command.h"
#include "preferences_manager.h"
#include <Arduino.h>
#include "motor_control.h"

PreferencesManager prefsManager;
extern uint16_t tle_angle;
extern float curr_speed;

void SerialCommand::setup() {
    Serial.begin(115200);
    Serial.println("Commands: p[val] i[val] d[val] c (calibrate)");
}

void SerialCommand::process(PIDController& pid, Encoder& encoder, MotorControl& motor) {
    if(Serial.available()) {
        char cmd = Serial.read();
        switch(cmd) {
            case 'p':
            case 'i':
            case 'd': {
                float value = Serial.parseFloat();
                handlePIDCommand(cmd, value, pid);
                break;
            }
            case 'c':
                handleCalibrateCommand(encoder, motor);
                break;
            case 's':
                handleStatusCommand(pid, encoder, motor);
                break;
            case 't':
                position_control_motorCommand(pid, encoder, motor);;
                break;    
        }
    }
}

void SerialCommand::handlePIDCommand(char cmd, float value, PIDController& pid) {
    prefsManager.begin();
    switch(cmd) {
        case 'p':
            prefsManager.putFloat("kp", value);
            pid.setKp(value);
            break;
        case 'i':
            prefsManager.putFloat("ki", value);
            pid.setKi(value);
            break;
        case 'd':
            prefsManager.putFloat("kd", value);
            pid.setKd(value);
            break;
    }
    prefsManager.end();
    Serial.print("Set "); Serial.print(cmd); 
    Serial.print(" to: "); Serial.println(value);
}

void SerialCommand::handleCalibrateCommand(Encoder& encoder, MotorControl& motor) {
    encoder.calibrate(motor);
}


void SerialCommand::handleStatusCommand(PIDController& pid, Encoder& encoder, MotorControl& motor) {
    float position = tle5012_to_angle(tle_angle);
    // float speed = motor.getCurrentSpeed();
    // float output = motor.getCurrentOutput();
    // float target = pid.getTarget();

    Serial.println("---- Motor Status ----");
    Serial.print("Position (rad): "); Serial.println(position, 6);
    // Serial.print("Speed (steps/s): "); Serial.println(speed, 2);
    // Serial.print("Output: "); Serial.println(output, 3);
    // Serial.print("Target: "); Serial.println(target, 6);
    Serial.println("----------------------");
}

void SerialCommand::position_control_motorCommand(PIDController& pid, Encoder& encoder, MotorControl& motor)
{

    motor.setpositionmode();
    motor.setPosition(100);
    motor.updatePositionControl();
    Serial.println("speeed");
    Serial.print(curr_speed);

}


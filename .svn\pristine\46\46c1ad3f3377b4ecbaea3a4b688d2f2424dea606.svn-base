#include "motor_control.h"
#include <Arduino.h>
#include <driver/timer.h>
#include "encoder.h"

// TMC2226控制引脚定义
#define STEP_PIN 13
#define DIR_PIN 9
#define STEP_DELAY_US 100  // 步进脉冲宽度(微秒)

// 硬件定时器配置
hw_timer_t *timer = NULL;
portMUX_TYPE timerMux = portMUX_INITIALIZER_UNLOCKED;
volatile uint32_t stepInterval = 0;
volatile uint32_t stepCount = 0;
extern uint16_t tle_angle;
float curr_speed = 0;

void IRAM_ATTR onTimer() {
  // 更高效的GPIO操作 - 直接设置/清除寄存器
  if (GPIO.out & (1 << STEP_PIN)) {
    GPIO.out_w1tc = (1 << STEP_PIN); // 清除STEP_PIN
  } else {
    GPIO.out_w1ts = (1 << STEP_PIN); // 设置STEP_PIN
    portENTER_CRITICAL_ISR(&timerMux);
    stepCount++;
    portEXIT_CRITICAL_ISR(&timerMux);
  }
}

void MotorControl::init() {
  // 配置GPIO为输出模式并设置初始状态
  GPIO.enable_w1ts = (1 << STEP_PIN) | (1 << DIR_PIN) | (1 << 10) | (1 << 11);
  GPIO.out_w1tc = (1 << STEP_PIN);  // STEP_PIN初始低电平
  GPIO.out_w1ts = (1 << DIR_PIN);
  GPIO.out_w1tc = (1 << 10);       // GPIO10初始低电平
  GPIO.out_w1tc = (1 << 11);       // GPIO11初始低电平
  //GPIO.out_w1tc = (1 << 38);       // GPIO38初始低电平
  pinMode(38, OUTPUT);
  digitalWrite(38, 0);
  


  // 初始化硬件定时器 (定时器0, 80MHz/16=5MHz, 0.2us分辨率)
  timer = timerBegin(0, 8, true);
  timerAttachInterrupt(timer, &onTimer, true);

  // 初始化编码器和PID控制器
  encoder = new Encoder();
  encoder->init();
  pidController = PIDController(1.0, 0.1, 0.05); // 默认PID参数
}

/**
 * @brief 设置电机输出(速度+方向)
 * @param output 输出值(-1.0~1.0)
 *   - 正数: 正转
 *   - 负数: 反转
 *   - 绝对值大小表示速度
 */
void MotorControl::setOutput(float output) {
  static int counter = 0;
  unsigned long currentTime = micros();
  float deltaTime = (currentTime - lastUpdateTime) / 1000000.0f;
  lastUpdateTime = currentTime;
  
  // 设置电机方向 - 直接寄存器操作
  if(output >= 0) {
    GPIO.out_w1ts = (1 << DIR_PIN); // 置高
  } else {
    GPIO.out_w1tc = (1 << DIR_PIN); // 置低
  }
  
  // 临界区保护 - 修改步进参数
  portENTER_CRITICAL(&timerMux);
  // 计算目标速度
  targetSpeed = abs(output) * 200; // 速度=output*200 steps/s
  
  // 应用加速度限制
  if(targetSpeed > currentSpeed) {
    currentSpeed += maxAcceleration * deltaTime;
    if(currentSpeed > targetSpeed) currentSpeed = targetSpeed;
  } else if(targetSpeed < currentSpeed) {
    currentSpeed -= maxAcceleration * deltaTime;
    if(currentSpeed < targetSpeed) currentSpeed = targetSpeed;
  }
  portEXIT_CRITICAL(&timerMux);
  
  // 每100步打印一次调试信息
  // if(counter++ % 100 == 0) {
  //   printf("Motor output: %.2f, DIR: %d, CurrentSpeed: %.1f, STEP pulses: %lu\n", 
  //          output, (GPIO.out & (1 << DIR_PIN)) ? 1 : 0, currentSpeed, stepCount);
  // }
  
  // 设置步进频率
  if(currentSpeed > 10) { // 最小速度阈值
    timerAlarmWrite(timer, 1000000/currentSpeed, true);
    timerAlarmEnable(timer);
  } else {
    timerAlarmDisable(timer);
    currentSpeed = 0;
  }
}

/**
 * @brief 电机输出接口(兼容原有代码)
 * @param theta 角度(未使用)
 * @param effort 输出力度(0-255)
 *   - 转换为-1.0~1.0后调用setOutput
 */
// void MotorControl::output(float theta, uint8_t effort) {
//   // 将0-255的effort转换为-1.0~1.0的输出
//   setOutput(effort/255.0f); 
// }

// 位置控制相关实现
void MotorControl::setPosition(float target) {
  targetPosition = target;
}

void MotorControl::setControlMode(ControlMode mode) {
  currentMode = mode;
  if (mode == POSITION_MODE) {
    pidController.reset();
  }
}

void MotorControl::updatePositionControl() {
  if (currentMode == POSITION_MODE) {
    float currentPos = tle5012_to_angle(tle_angle);
    // float currentPos = encoder->getPosition();
    float output = pidController.calculate(currentPos - targetPosition);
    curr_speed = output;
    setOutput(output);
  }
}

float MotorControl::getCurrentPosition() const {
  // return encoder ? encoder->getPosition() : 0;
  return encoder ? tle5012_to_angle(tle5012b_read_angle(TLE5012B_SPI,(gpio_num_t)12)) : 0;
}

float MotorControl::getTargetPosition() const {
  return targetPosition;
}

void MotorControl::setpositionmode()
{

  setControlMode(POSITION_MODE);
}
  
void MotorControl::setspeedmode()
{

  setControlMode(SPEED_MODE);
}
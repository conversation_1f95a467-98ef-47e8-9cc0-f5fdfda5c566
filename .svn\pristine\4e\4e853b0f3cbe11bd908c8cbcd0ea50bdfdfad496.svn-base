#include "ble_manager.h"
#include <Arduino.h>
#include <stdio.h>

class BLEManager::MyServerCallbacks: public NimBLEServerCallbacks {
    BLEManager* _manager;
public:
    MyServerCallbacks(BLEManager* manager) : _manager(manager) {}

    void onConnect(NimBLEServer* pServer) {
        _manager->_deviceConnected = true;
        printf("\nBLE: Client connected callback triggered");
        printf("BLE: Number of connected clients: ");
        //Serial.println(pServer->getConnectedCount());
        printf("BLE: Connection established successfully");
    }

    void onDisconnect(NimBLEServer* pServer) {
        _manager->_deviceConnected = false;
        printf("\nBLE: Client disconnected callback triggered");
        printf("BLE: Attempting to restart advertising...");
        if (NimBLEDevice::getAdvertising()->start()) {
            printf("BLE: Advertising restarted successfully");
        } else {
            printf("BLE: Failed to restart advertising!");
        }
    }
};

BLEManager::BLEManager(const char* deviceName) 
    : _deviceName(deviceName), _pServer(nullptr), _deviceConnected(false) {}

void BLEManager::begin() {
    xTaskCreatePinnedToCore(
        [](void* param) { ((BLEManager*)param)->taskBLE(nullptr); },
        "taskBLE",
        4096,  // Increased stack size to prevent crashes
        this,
        2,
        nullptr,
        1
    );
}

void BLEManager::taskBLE(void* parameter) {
    printf("Initializing BLE...\n");
    if (!NimBLEDevice::init(_deviceName)) {
        printf("BLE initialization failed!\n");
        vTaskDelete(NULL);
        return;
    }
    _pServer = NimBLEDevice::createServer();
    if (!_pServer) {
        printf("Failed to create BLE server!\n");
        vTaskDelete(NULL);
        return;
    }
    _pServer->setCallbacks(new MyServerCallbacks(this));
    printf("BLE callbacks registered successfully\n");

    // Create BLE Service
    NimBLEService *pService = _pServer->createService("1234");
    
    // Create BLE Characteristic
    NimBLECharacteristic *pCharacteristic = pService->createCharacteristic(
        "ABCD",
        NIMBLE_PROPERTY::READ |
        NIMBLE_PROPERTY::WRITE |
        NIMBLE_PROPERTY::NOTIFY
    );

    pService->start();
    NimBLEAdvertising *pAdvertising = NimBLEDevice::getAdvertising();
    pAdvertising->addServiceUUID(pService->getUUID());
    
    // Configure advertisement data
    NimBLEAdvertisementData advertisementData;
    advertisementData.setFlags(BLE_HS_ADV_F_DISC_GEN | BLE_HS_ADV_F_BREDR_UNSUP);
    advertisementData.setName(_deviceName);
    pAdvertising->setAdvertisementData(advertisementData);
    
    // Configure scan response data
    NimBLEAdvertisementData scanResponseData;
    scanResponseData.setCompleteServices(pService->getUUID());
    pAdvertising->setScanResponseData(scanResponseData);
    
    if (!pAdvertising->start()) {
        printf("Failed to start BLE advertising!\n");
        vTaskDelete(NULL);
        return;
    }
    printf("BLE Advertising Started successfully. Device name: %s\n", _deviceName);

    while(1) {
        if(_deviceConnected) {
            printf("BLE: Device connected\n");
            vTaskDelay(1000);
        } else {
            printf("BLE: Advertising status - %s\n", 
                NimBLEDevice::getAdvertising()->isAdvertising() ? "Active" : "Inactive");
            if (!NimBLEDevice::getAdvertising()->isAdvertising()) {
                printf("BLE: Restarting advertising...\n");
                NimBLEDevice::startAdvertising();
            }
            vTaskDelay(1000);
        }
    }
}

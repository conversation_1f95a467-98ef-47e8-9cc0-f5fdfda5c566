#include "encoder.h"
#include "motor_control.h"
#include <driver/pcnt.h>
#include <math.h>
#include <Preferences.h>

// Calibration speed parameters
#define EC_AUTO_CALIB_SPEED 2
#define EC_FINE_TUNE_CALIB_SPEED 1
// cali_Error_t cali_errorCode;
bool cali_isTriggered;
uint32_t cali_goPosition;
int32_t cali_rcdX, cali_rcdY;
uint32_t cali_resultNum;

Encoder::Encoder() : 
    pcnt_unit(PCNT_UNIT_0),
    calibrationCurrent(DEFAULT_CALIBRATION_CURRENT),
    caliState(CALI_DISABLE) {
    calibration.offset = 0;
    calibration.scale = 1.0f;
    calibration.calibrated = false;
}

void Encoder::init(float caliCurrent) {
    this->calibrationCurrent = caliCurrent;
    
    cali_isTriggered = false;
    // cali_errorCode = CALI_NO_ERROR;
    caliState = CALI_DISABLE;
    cali_goPosition = 0;
    cali_rcdX = 0;
    cali_rcdY = 0;
    cali_resultNum = 0; 


    // Initialize preferences
    prefs.begin("encoder_calib");
    loadCalibrationFromFlash();

    // Configure PCNT unit
    pcnt_config_t pcnt_config = {
        .pulse_gpio_num = ENCODER_A_PIN,
        .ctrl_gpio_num = ENCODER_B_PIN,
        .lctrl_mode = PCNT_MODE_REVERSE,
        .hctrl_mode = PCNT_MODE_KEEP,
        .pos_mode = PCNT_COUNT_INC,
        .neg_mode = PCNT_COUNT_DEC,
        .counter_h_lim = PCNT_HIGH_LIMIT,
        .counter_l_lim = PCNT_LOW_LIMIT,
        .unit = pcnt_unit,
        .channel = PCNT_CHANNEL_0
    };
    pcnt_unit_config(&pcnt_config);
    pcnt_counter_pause(pcnt_unit);
    pcnt_counter_clear(pcnt_unit);
    pcnt_counter_resume(pcnt_unit);
}

float Encoder::getPosition() {
    int16_t count;
    pcnt_get_counter_value(pcnt_unit, &count);
    float position = count / 4000.0f * 2 * M_PI; // 4000 pulses/revolution
    
    if(calibration.calibrated) {
        position = position * calibration.scale + calibration.offset;
    }
    return position;
}

void Encoder::reset() {
    pcnt_counter_clear(pcnt_unit);
}

void Encoder::calibrate(MotorControl& motor) {
    ENUpdateAngle();
    switch(caliState) {
        case CALI_DISABLE:
            // Start calibration process
            motor.setControlMode(MotorControl::POSITION_MODE);
            caliParams.goPosition = MOTOR_ONE_CIRCLE_SUBDIVIDE_STEPS;
            caliParams.sampleCount = 0;
            caliState = CALI_FORWARD_PREPARE;
            // cali_errorCode = CALI_NO_ERROR;
            break;
            
        case CALI_FORWARD_PREPARE:
            // Move motor forward to starting position
            caliParams.goPosition += EC_AUTO_CALIB_SPEED;
            motor.setPosition(caliParams.goPosition);
            
            if(caliParams.goPosition >= 2 * MOTOR_ONE_CIRCLE_SUBDIVIDE_STEPS) {
                caliParams.goPosition = MOTOR_ONE_CIRCLE_SUBDIVIDE_STEPS;
                caliState = CALI_FORWARD_MEASURE;
            }
            break;
            
        case CALI_FORWARD_MEASURE:
            // Take measurements while moving forward
            if((caliParams.goPosition % SOFT_DIVIDE_NUM) == 0) {
                uint16_t angle = tle5012b_read_angle(TLE5012B_SPI, TLE5012B_SOFT_CS0);
                caliParams.sampleDataRaw[caliParams.sampleCount++] = angle;
                
                if(caliParams.sampleCount >= EC_SAMPLE_COUNTS_PER_STEP) {
                    float avg = calculateAverage(caliParams.sampleDataRaw, EC_SAMPLE_COUNTS_PER_STEP);
                    calibration.forwardData[(caliParams.goPosition - MOTOR_ONE_CIRCLE_SUBDIVIDE_STEPS) / SOFT_DIVIDE_NUM] = avg;
                    caliParams.sampleCount = 0;
                    caliParams.goPosition += EC_FINE_TUNE_CALIB_SPEED;
                }
            } else {
                caliParams.goPosition += EC_FINE_TUNE_CALIB_SPEED;
            }
            
            motor.setPosition(caliParams.goPosition);
            
            if(caliParams.goPosition > 2 * MOTOR_ONE_CIRCLE_SUBDIVIDE_STEPS) {
                caliState = CALI_BACKWARD_RETURN;
            }
            break;
            
        case CALI_BACKWARD_RETURN:
            // Return motor to starting position
            caliParams.goPosition -= EC_AUTO_CALIB_SPEED;
            motor.setPosition(caliParams.goPosition);
            
            if(caliParams.goPosition <= MOTOR_ONE_CIRCLE_SUBDIVIDE_STEPS) {
                caliState = CALI_BACKWARD_GAP_DISMISS;
            }
            break;
            
        case CALI_BACKWARD_GAP_DISMISS:
            // Move slightly past start position to ensure gap is passed
            caliParams.goPosition -= EC_FINE_TUNE_CALIB_SPEED;
            motor.setPosition(caliParams.goPosition);
            
            if(caliParams.goPosition <= MOTOR_ONE_CIRCLE_SUBDIVIDE_STEPS - SOFT_DIVIDE_NUM) {
                caliState = CALI_BACKWARD_MEASURE;
                caliParams.sampleCount = 0;
            }
            break;
            
        case CALI_BACKWARD_MEASURE:
            // Take measurements while moving backward
            if((caliParams.goPosition % SOFT_DIVIDE_NUM) == 0) {
                uint16_t angle = tle5012b_read_angle(TLE5012B_SPI, TLE5012B_SOFT_CS0);
                caliParams.sampleDataRaw[caliParams.sampleCount++] = angle;
                
                if(caliParams.sampleCount >= EC_SAMPLE_COUNTS_PER_STEP) {
                    float avg = calculateAverage(caliParams.sampleDataRaw, EC_SAMPLE_COUNTS_PER_STEP);
                    calibration.backwardData[(MOTOR_ONE_CIRCLE_SUBDIVIDE_STEPS - caliParams.goPosition) / SOFT_DIVIDE_NUM] = avg;
                    caliParams.sampleCount = 0;
                    caliParams.goPosition -= EC_FINE_TUNE_CALIB_SPEED;
                }
            } else {
                caliParams.goPosition -= EC_FINE_TUNE_CALIB_SPEED;
            }
            
            motor.setPosition(caliParams.goPosition);
            
            if(caliParams.goPosition < 0) {
                caliState = CALI_CALCULATING;
            }
            break;
            
        case CALI_CALCULATING:
            // Finalize calibration using both forward and backward data
            calculateCalibrationParams();
            calibration.calibrated = true;
            saveCalibrationToFlash();
            motor.setControlMode(MotorControl::SPEED_MODE);
            caliState = CALI_DISABLE;
            break;
            
        default:
            break;
    }
}

bool Encoder::saveCalibrationToFlash() {
    if(!calibration.calibrated) return false;
    
    prefs.putFloat("offset", calibration.offset);
    prefs.putFloat("scale", calibration.scale);
    prefs.putBool("calibrated", true);
    return true;
}

bool Encoder::loadCalibrationFromFlash() {
    if(prefs.isKey("calibrated")) {
        calibration.offset = prefs.getFloat("offset");
        calibration.scale = prefs.getFloat("scale");
        calibration.calibrated = prefs.getBool("calibrated");
        return true;
    }
    return false;
}

float Encoder::calculateAverage(uint16_t* data, uint8_t count) {
    uint32_t sum = 0;
    for(uint8_t i = 0; i < count; i++) {
        sum += data[i];
    }
    return sum / (float)count;
}

void Encoder::calculateCalibrationParams() {
    // Implement calibration parameter calculation
    // based on forward/backward data
    calibration.offset = 0;
    calibration.scale = 1.0f;
}

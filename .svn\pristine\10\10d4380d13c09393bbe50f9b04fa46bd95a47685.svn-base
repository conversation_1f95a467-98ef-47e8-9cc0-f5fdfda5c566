#ifndef PID_CONTROLLER_H
#define PID_CONTROLLER_H

class PIDController {
public:
    PIDController(float kp, float ki, float kd);
    float calculate(float error);
    void reset();
    void setKp(float kp);
    void setKi(float ki);
    void setKd(float kd);
    float getTarget() const;

private:
    float kp;
    float ki;
    float kd;
    float integral;
    float prev_error;
    float target;
};

#endif

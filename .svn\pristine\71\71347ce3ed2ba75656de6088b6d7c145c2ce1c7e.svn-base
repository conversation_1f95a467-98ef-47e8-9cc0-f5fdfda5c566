#include "spi_config.h"
#include "driver/spi_master.h"
#include "esp_err.h"
#include "esp_log.h"

void spi_master_init(spi_host_device_t host_id, int dma_chan, uint32_t max_tran_size, gpio_num_t miso_io_num, gpio_num_t mosi_io_num, gpio_num_t clk_io_num)
{
    esp_err_t ret;
    // 配置 MISO、MOSI、CLK、CS 的引脚，和DMA最大传输字节数
    spi_bus_config_t buscfg;
    buscfg.miso_io_num = miso_io_num;          // MISO引脚定义
    buscfg.mosi_io_num = mosi_io_num;          // MOSI引脚定义
    buscfg.sclk_io_num = clk_io_num;           // CLK引脚定义
    buscfg.quadwp_io_num = -1;
    buscfg.quadhd_io_num = -1;
    buscfg.max_transfer_sz = max_tran_size;    // 最大传输字节数

    // 初始化SPI总线
    ret=spi_bus_initialize(host_id, &buscfg, dma_chan);
    ESP_ERROR_CHECK(ret);
}

#include "key.h"
#include "oled.h"

#define BUTTON_PIN 1
#define DEBOUNCE_DELAY_MS 50

static unsigned long lastDebounceTime = 0;
static int buttonState = LOW;
static int lastButtonState = LOW;

void key_init() {
    pinMode(BUTTON_PIN, INPUT);
}

void key_task(void *pvParameters) {
    key_init();
    
    while(true) {
        int reading = digitalRead(BUTTON_PIN);
        
        if (reading != lastButtonState) {
            lastDebounceTime = millis();
        }

        if ((millis() - lastDebounceTime) > DEBOUNCE_DELAY_MS) {
            if (reading != buttonState) {
                buttonState = reading;
                if (buttonState == LOW) { // 按键释放
                    // reset_oled_counter();
                    change_mode();
                }
            }
        }
        lastButtonState = reading;
        
        vTaskDelay(10 / portTICK_PERIOD_MS);
    }
}

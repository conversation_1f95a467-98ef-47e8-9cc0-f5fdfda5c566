#ifndef ENCODER_H
#define ENCODER_H

#include <driver/pcnt.h>
#include <Preferences.h>
#include "spi_tle5012b.h"

// Forward declarations
class MotorControl;

// Calibration parameters
#define SOFT_DIVIDE_NUM 256                    ///< 软件细分数
#define EC_SAMPLE_COUNTS_PER_STEP 16          ///< 每步采样次数
#define MOTOR_ONE_CIRCLE_SUBDIVIDE_STEPS 51200 ///< 电机一圈细分步数
#define DEFAULT_CALIBRATION_CURRENT 500       ///< 默认校准电流(mA)

/**
 * @brief 编码器类
 * @details 基于TLE5012B磁性角度传感器和脉冲计数器(PCNT)实现的编码器系统
 *          支持自动校准、位置读取和数据持久化存储
 */
class Encoder {
public:
    /**
     * @brief 编码器构造函数
     * @details 初始化编码器参数和校准状态
     */
    Encoder();

    /**
     * @brief 初始化编码器
     * @param caliCurrent 校准电流值，默认500mA
     * @details 配置PCNT单元、GPIO引脚和校准参数
     */
    void init(float caliCurrent = DEFAULT_CALIBRATION_CURRENT);

    /**
     * @brief 获取当前位置
     * @return 当前编码器位置值
     * @details 结合TLE5012B角度传感器和PCNT计数器读取精确位置
     */
    float getPosition();

    /**
     * @brief 重置编码器位置
     * @details 将编码器位置计数器清零
     */
    void reset();

    /**
     * @brief 执行编码器校准
     * @param motor 电机控制器引用
     * @details 通过控制电机运动来校准编码器的偏移和比例参数
     */
    void calibrate(MotorControl& motor);

    /**
     * @brief 保存校准数据到Flash
     * @return 保存成功返回true，失败返回false
     * @details 将校准参数持久化存储到ESP32的NVS存储区
     */
    bool saveCalibrationToFlash();

    /**
     * @brief 从Flash加载校准数据
     * @return 加载成功返回true，失败返回false
     * @details 从ESP32的NVS存储区读取校准参数
     */
    bool loadCalibrationFromFlash();

private:
    /**
     * @brief 计算数据平均值
     * @param data 数据数组指针
     * @param count 数据个数
     * @return 平均值
     */
    float calculateAverage(uint16_t* data, uint8_t count);

    /**
     * @brief 计算校准参数
     * @details 根据校准过程中采集的数据计算偏移和比例参数
     */
    void calculateCalibrationParams();
    
    pcnt_unit_t pcnt_unit;
    static constexpr int PCNT_HIGH_LIMIT = 32767;
    static constexpr int PCNT_LOW_LIMIT = -32768;
    static constexpr gpio_num_t ENCODER_A_PIN = GPIO_NUM_4;
    static constexpr gpio_num_t ENCODER_B_PIN = GPIO_NUM_5;
    
    enum CalibrationState {
        CALI_DISABLE,
        CALI_FORWARD_PREPARE,
        CALI_FORWARD_MEASURE,
        CALI_BACKWARD_RETURN,
        CALI_BACKWARD_GAP_DISMISS,
        CALI_BACKWARD_MEASURE,
        CALI_CALCULATING
    } caliState;

    typedef enum{
        CALI_NO_ERROR = 0x00,
        CALI_ERROR_AVERAGE_DIR,
        CALI_ERROR_AVERAGE_CONTINUTY,
        CALI_ERROR_PHASE_STEP,
        CALI_ERROR_ANALYSIS_QUANTITY,
    } cali_Error_t;
    
    struct CalibrationData {
        float offset;
        float scale;
        bool calibrated;
        float forwardData[SOFT_DIVIDE_NUM];
        float backwardData[SOFT_DIVIDE_NUM];
    } calibration;
    
    struct {
        uint16_t sampleCount;
        uint16_t goPosition;
        uint16_t sampleDataRaw[EC_SAMPLE_COUNTS_PER_STEP];
    } caliParams;
    
    Preferences prefs;
    float calibrationCurrent;
};
#endif

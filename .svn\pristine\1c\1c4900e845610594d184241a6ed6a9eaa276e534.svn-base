#include "pid_controller.h"

PIDController::PIDController(float kp, float ki, float kd) 
    : kp(kp), ki(ki), kd(kd), integral(0), prev_error(0), target(0) {
}

float PIDController::calculate(float position) {
    float error = target - position;
    integral += error;
    float derivative = error - prev_error;
    prev_error = error;
    
    return kp * error + ki * integral + kd * derivative;
}

void PIDController::reset() {
    integral = 0;
    prev_error = 0;
}

void PIDController::setKp(float kp) {
    this->kp = kp;
}

void PIDController::setKi(float ki) {
    this->ki = ki;
}

void PIDController::setKd(float kd) {
    this->kd = kd;
}

float PIDController::getTarget() const {
    return target;
}

#ifndef BLE_MANAGER_H
#define BLE_MANAGER_H

#include <NimBLEDevice.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

#define BLE_DEVICE_NAME "ESP32-Stepper"
#define SERVICE_UUID "00001234-0000-1000-8000-00805f9b34fb"
#define CHARACTERISTIC_UUID "0000abcd-0000-1000-8000-00805f9b34fb"
extern bool motormode;
extern float motor_speed;
extern int motor_steps;
// 前向声明
class BLEManager;

// 回调类定义
class BLEServerCallbacksImpl : public BLEServerCallbacks {
    BLEManager& manager;
public:
    BLEServerCallbacksImpl(BLEManager& m) : manager(m) {}
    void onConnect(BLEServer* pServer) override;
    void onDisconnect(BLEServer* pServer) override;
};

class BLECharacteristicCallbacksImpl : public BLECharacteristicCallbacks {
    BLEManager& manager;
public:
    BLECharacteristicCallbacksImpl(BLEManager& m) : manager(m) {}
    void onWrite(BLECharacteristic* pCharacteristic) override;
};

// BLE管理类
class BLEManager {
public:
    static BLEManager& getInstance();
    void init();
    void start();
    void stop();
    bool isConnected() const;
    void sendNotification(const std::string& value);
    void setConnected(bool connected);

private:
    BLEManager();
    void setupBLEServer();
    void setupBLEService();
    void setupBLECharacteristic();

    BLEServer* pServer;
    BLEService* pService;
    BLECharacteristic* pCharacteristic;
    bool deviceConnected;
    BLEServerCallbacksImpl serverCallbacks;
    BLECharacteristicCallbacksImpl charCallbacks;
};

// 声明 BLE 任务函数
void bleTask(void* parameter);

#endif

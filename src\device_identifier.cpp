#include "ble_manager.h"
#include <WiFi.h>
#include <Arduino.h>
#include <esp_system.h>  // 用于esp_read_mac函数

// 静态成员变量定义
String DeviceIdentifier::macSuffix = "";

void DeviceIdentifier::initMacSuffix() {
    if (macSuffix.length() == 0) {
        // 获取WiFi MAC地址
        uint8_t mac[6];
        esp_read_mac(mac, ESP_MAC_WIFI_STA);
        
        // 使用MAC地址的后4位（2字节）创建后缀
        // 格式：A1B2 (使用最后两个字节)
        macSuffix = String(mac[4], HEX);
        macSuffix.toUpperCase();
        if (macSuffix.length() == 1) macSuffix = "0" + macSuffix;
        
        String secondByte = String(mac[5], HEX);
        secondByte.toUpperCase();
        if (secondByte.length() == 1) secondByte = "0" + secondByte;
        
        macSuffix += secondByte;
    }
}

String DeviceIdentifier::getDeviceSuffix() {
    initMacSuffix();
    return macSuffix;
}

String DeviceIdentifier::getUniqueDeviceName(const String& baseName) {
    initMacSuffix();
    return baseName + "-" + macSuffix;
}

String DeviceIdentifier::getUniqueVersionResponse() {
    initMacSuffix();
    
    // 基础版本号：032D0C
    // 将MAC后缀的4个字符转换为版本号的一部分
    // 保持Skywatcher协议兼容性，但使版本号唯一
    
    // 获取MAC地址的完整6字节
    uint8_t mac[6];
    esp_read_mac(mac, ESP_MAC_WIFI_STA);
    
    // 使用MAC地址的后3个字节生成唯一版本号
    // 格式：=XXXXXX\r (6位十六进制)
    uint32_t version = (mac[3] << 16) | (mac[4] << 8) | mac[5];
    
    char versionStr[16];
    snprintf(versionStr, sizeof(versionStr), "=%06X\r", version);
    
    return String(versionStr);
}

String DeviceIdentifier::getUniqueHostname(const String& baseName) {
    initMacSuffix();
    return baseName + "-" + macSuffix;
}

; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-s3-devkitm-1]
platform = espressif32
board = esp32-s3-devkitm-1
framework = arduino
board_build.flash_size = 16MB
board_build.psram_size = 8MB
monitor_speed = 115200
upload_speed = 921600
build_flags = 
    -DARDUINO_USB_MODE=1
    -DARDUINO_USB_CDC_ON_BOOT=1
lib_deps = 
    h2zero/NimBLE-Arduino@1.4.1
    adafruit/Adafruit SSD1306@^2.5.7 
    adafruit/Adafruit GFX Library@^1.11.5


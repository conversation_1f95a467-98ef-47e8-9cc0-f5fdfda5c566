#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSansOblique9pt7bBitmaps[] PROGMEM = {
    0x10, 0x84, 0x22, 0x10, 0x84, 0x42, 0x10, 0x08, 0x00, 0xDE, 0xE5, 0x20,
    0x06, 0x40, 0x88, 0x13, 0x06, 0x43, 0xFE, 0x32, 0x04, 0x40, 0x98, 0x32,
    0x1F, 0xF0, 0x98, 0x22, 0x04, 0xC0, 0x02, 0x01, 0xF8, 0x6B, 0x99, 0x33,
    0x40, 0x68, 0x0F, 0x00, 0xF8, 0x07, 0xC1, 0x1B, 0x23, 0x64, 0x4E, 0x98,
    0xFC, 0x04, 0x00, 0x80, 0x3C, 0x08, 0xCC, 0x23, 0x18, 0x86, 0x32, 0x0C,
    0x64, 0x19, 0x90, 0x1E, 0x40, 0x01, 0x1E, 0x02, 0x66, 0x09, 0x8C, 0x23,
    0x18, 0x86, 0x62, 0x07, 0x80, 0x0F, 0x06, 0x63, 0x18, 0xC6, 0x3F, 0x07,
    0x03, 0xC1, 0xB3, 0xC7, 0xB0, 0xCC, 0x33, 0x3E, 0x79, 0x80, 0xFA, 0x04,
    0x10, 0x60, 0x83, 0x04, 0x18, 0x30, 0xC1, 0x83, 0x06, 0x0C, 0x18, 0x10,
    0x30, 0x20, 0x08, 0x18, 0x10, 0x30, 0x60, 0xC1, 0x83, 0x06, 0x18, 0x30,
    0x41, 0x82, 0x0C, 0x10, 0x40, 0x19, 0x73, 0x16, 0x48, 0x04, 0x04, 0x02,
    0x1F, 0xF0, 0x80, 0x80, 0x40, 0x20, 0x6D, 0x28, 0xF0, 0xC0, 0x01, 0x02,
    0x04, 0x04, 0x08, 0x08, 0x10, 0x10, 0x20, 0x20, 0x40, 0x40, 0x80, 0x0F,
    0x19, 0xC8, 0x6C, 0x36, 0x1A, 0x0F, 0x05, 0x86, 0xC3, 0x61, 0xB1, 0x9C,
    0x87, 0x80, 0x08, 0xCD, 0xE3, 0x18, 0xC4, 0x23, 0x18, 0xC4, 0x00, 0x07,
    0x83, 0x1C, 0x41, 0x98, 0x30, 0x06, 0x01, 0x80, 0x60, 0x38, 0x1C, 0x06,
    0x01, 0x80, 0x20, 0x0F, 0xF8, 0x0F, 0x86, 0x73, 0x0C, 0x83, 0x00, 0xC0,
    0x60, 0xE0, 0x06, 0x01, 0xB0, 0x6C, 0x13, 0x8C, 0x7C, 0x00, 0x00, 0x80,
    0xC0, 0xE0, 0xA0, 0x90, 0x98, 0x8C, 0x86, 0xFF, 0x81, 0x01, 0x80, 0xC0,
    0x60, 0x0F, 0xC3, 0x00, 0x40, 0x08, 0x03, 0x00, 0x7F, 0x1C, 0x70, 0x06,
    0x00, 0xC0, 0x1B, 0x06, 0x71, 0x87, 0xE0, 0x0F, 0x86, 0x73, 0x0D, 0x80,
    0x60, 0x1F, 0xCF, 0x3B, 0x86, 0xC1, 0xB0, 0x6C, 0x33, 0x98, 0x3C, 0x00,
    0x7F, 0xC0, 0x20, 0x10, 0x0C, 0x06, 0x01, 0x00, 0x80, 0x60, 0x10, 0x0C,
    0x02, 0x01, 0x80, 0x40, 0x00, 0x0F, 0x86, 0x73, 0x0C, 0xC3, 0x30, 0xCC,
    0x61, 0xE1, 0x86, 0x41, 0xB0, 0x6C, 0x13, 0x8C, 0x3E, 0x00, 0x0F, 0x06,
    0x73, 0x0D, 0x83, 0x60, 0xD8, 0x77, 0x3C, 0xFE, 0x01, 0x80, 0x6C, 0x33,
    0x98, 0x7C, 0x00, 0x30, 0x00, 0x00, 0x00, 0xC0, 0x18, 0x00, 0x00, 0x00,
    0x0C, 0x62, 0x11, 0x00, 0x00, 0x01, 0xC3, 0x8F, 0x0C, 0x07, 0x00, 0xE0,
    0x1E, 0x01, 0x00, 0x7F, 0xC0, 0x00, 0x03, 0xFE, 0x40, 0x3C, 0x03, 0x80,
    0x70, 0x18, 0x78, 0xE1, 0xC0, 0x00, 0x00, 0x1F, 0x30, 0xD0, 0x78, 0x30,
    0x30, 0x30, 0x30, 0x30, 0x30, 0x00, 0x00, 0x00, 0x06, 0x00, 0x00, 0xFE,
    0x00, 0xC0, 0xE0, 0xC0, 0x18, 0x61, 0xD3, 0x31, 0x9C, 0xD8, 0xC2, 0x36,
    0x31, 0x8F, 0x18, 0x67, 0xC6, 0x11, 0xB1, 0x8C, 0xCC, 0x67, 0x63, 0x0E,
    0xF0, 0x60, 0x00, 0x1C, 0x00, 0x01, 0x81, 0x00, 0x1F, 0xC0, 0x01, 0xC0,
    0x1C, 0x03, 0xC0, 0x24, 0x06, 0x60, 0x46, 0x0C, 0x61, 0x86, 0x1F, 0xE3,
    0x06, 0x20, 0x26, 0x03, 0x40, 0x30, 0x1F, 0xE1, 0x87, 0x30, 0x33, 0x03,
    0x30, 0x23, 0x06, 0x3F, 0xC6, 0x06, 0x60, 0x66, 0x06, 0x60, 0x66, 0x0C,
    0x7F, 0x80, 0x07, 0xC1, 0x86, 0x30, 0x32, 0x03, 0x60, 0x04, 0x00, 0xC0,
    0x0C, 0x00, 0xC0, 0x6C, 0x06, 0xC0, 0xC6, 0x18, 0x3E, 0x00, 0x1F, 0xE0,
    0xC1, 0x84, 0x06, 0x60, 0x33, 0x01, 0x98, 0x0C, 0x80, 0x64, 0x02, 0x60,
    0x33, 0x01, 0x98, 0x18, 0x81, 0x87, 0xF0, 0x00, 0x1F, 0xF1, 0x80, 0x10,
    0x03, 0x00, 0x30, 0x03, 0x00, 0x3F, 0xE2, 0x00, 0x60, 0x06, 0x00, 0x60,
    0x04, 0x00, 0x7F, 0xC0, 0x1F, 0xF1, 0x80, 0x10, 0x03, 0x00, 0x30, 0x03,
    0x00, 0x3F, 0xC2, 0x00, 0x60, 0x06, 0x00, 0x60, 0x04, 0x00, 0x40, 0x00,
    0x07, 0xE0, 0xE1, 0x8C, 0x06, 0xC0, 0x36, 0x00, 0x60, 0x03, 0x07, 0xF8,
    0x02, 0xC0, 0x36, 0x01, 0x98, 0x1C, 0xE1, 0xC1, 0xF2, 0x00, 0x18, 0x08,
    0xC0, 0xC4, 0x06, 0x60, 0x33, 0x01, 0x18, 0x18, 0xFF, 0xC4, 0x06, 0x60,
    0x23, 0x01, 0x18, 0x18, 0x80, 0xC4, 0x06, 0x00, 0x33, 0x32, 0x26, 0x66,
    0x44, 0xCC, 0xC0, 0x00, 0xC0, 0x60, 0x18, 0x06, 0x01, 0x80, 0x60, 0x30,
    0x0C, 0x03, 0x30, 0xCC, 0x63, 0x18, 0x7C, 0x00, 0x18, 0x18, 0x60, 0xC1,
    0x0E, 0x0C, 0x60, 0x33, 0x00, 0xD8, 0x03, 0xF0, 0x0C, 0xC0, 0x61, 0x81,
    0x86, 0x06, 0x0C, 0x10, 0x30, 0x40, 0x60, 0x18, 0x0C, 0x04, 0x06, 0x03,
    0x01, 0x80, 0xC0, 0x40, 0x60, 0x30, 0x18, 0x08, 0x07, 0xF8, 0x18, 0x06,
    0x18, 0x0E, 0x18, 0x0E, 0x34, 0x1E, 0x34, 0x36, 0x34, 0x34, 0x24, 0x64,
    0x24, 0x6C, 0x64, 0xCC, 0x64, 0x8C, 0x65, 0x88, 0x43, 0x08, 0x43, 0x18,
    0x18, 0x08, 0xE0, 0x47, 0x06, 0x6C, 0x33, 0x61, 0x99, 0x08, 0x8C, 0xC4,
    0x66, 0x61, 0xB3, 0x0D, 0x18, 0x38, 0x81, 0xC4, 0x06, 0x00, 0x07, 0xC0,
    0xC3, 0x8C, 0x0E, 0xC0, 0x36, 0x01, 0xE0, 0x0F, 0x00, 0x78, 0x03, 0xC0,
    0x36, 0x01, 0xB8, 0x18, 0xE1, 0x81, 0xF0, 0x00, 0x1F, 0xE1, 0x83, 0x10,
    0x33, 0x03, 0x30, 0x33, 0x06, 0x3F, 0xC2, 0x00, 0x60, 0x06, 0x00, 0x60,
    0x04, 0x00, 0x40, 0x00, 0x07, 0xC0, 0xC3, 0x8C, 0x0E, 0xC0, 0x36, 0x01,
    0xE0, 0x0F, 0x00, 0x78, 0x03, 0xC0, 0x36, 0x09, 0xB8, 0x78, 0xE3, 0x81,
    0xF6, 0x00, 0x10, 0x1F, 0xF0, 0xC0, 0xC4, 0x06, 0x60, 0x33, 0x01, 0x18,
    0x18, 0xFF, 0x04, 0x0C, 0x60, 0x63, 0x03, 0x18, 0x18, 0x80, 0xC4, 0x06,
    0x00, 0x07, 0xC1, 0x87, 0x30, 0x33, 0x03, 0x30, 0x03, 0xC0, 0x0F, 0xC0,
    0x1E, 0x00, 0x6C, 0x06, 0xC0, 0x46, 0x0C, 0x3F, 0x00, 0xFF, 0xC3, 0x00,
    0xC0, 0x20, 0x18, 0x06, 0x01, 0x80, 0x60, 0x10, 0x0C, 0x03, 0x00, 0xC0,
    0x20, 0x00, 0x30, 0x13, 0x03, 0x20, 0x36, 0x03, 0x60, 0x26, 0x06, 0x60,
    0x64, 0x06, 0xC0, 0x6C, 0x04, 0xC0, 0xCE, 0x18, 0x3E, 0x00, 0xC0, 0x78,
    0x0B, 0x03, 0x20, 0xC4, 0x18, 0xC6, 0x18, 0x83, 0x30, 0x64, 0x0D, 0x80,
    0xA0, 0x1C, 0x03, 0x00, 0xC1, 0x83, 0xC1, 0x83, 0xC3, 0x86, 0xC2, 0x86,
    0xC6, 0x84, 0xC4, 0x8C, 0xCC, 0xC8, 0xC8, 0xD8, 0xD8, 0xD0, 0xD0, 0xF0,
    0x70, 0xE0, 0x60, 0xE0, 0x60, 0xE0, 0x0C, 0x0C, 0x30, 0x60, 0x63, 0x01,
    0x98, 0x02, 0xC0, 0x0E, 0x00, 0x38, 0x01, 0xE0, 0x0C, 0x80, 0x33, 0x01,
    0x8C, 0x0C, 0x18, 0x60, 0x60, 0xC0, 0x66, 0x0C, 0x60, 0xC2, 0x18, 0x33,
    0x03, 0x60, 0x1C, 0x01, 0x80, 0x18, 0x01, 0x80, 0x18, 0x01, 0x00, 0x30,
    0x00, 0x1F, 0xF0, 0x07, 0x00, 0xE0, 0x0C, 0x01, 0x80, 0x30, 0x06, 0x00,
    0xC0, 0x18, 0x03, 0x00, 0x60, 0x0C, 0x00, 0xFF, 0xC0, 0x0E, 0x10, 0x20,
    0x41, 0x02, 0x04, 0x08, 0x20, 0x40, 0x81, 0x04, 0x08, 0x10, 0x20, 0xE0,
    0xAA, 0xA9, 0x55, 0x40, 0x0E, 0x08, 0x10, 0x20, 0x41, 0x02, 0x04, 0x08,
    0x20, 0x40, 0x81, 0x04, 0x08, 0x10, 0xE0, 0x0C, 0x18, 0x51, 0xA2, 0x4C,
    0x50, 0x80, 0xFF, 0xE0, 0xC8, 0x80, 0x0F, 0x86, 0x33, 0x0C, 0x03, 0x03,
    0xDF, 0xEE, 0x0B, 0x02, 0xC1, 0x9F, 0xE0, 0x10, 0x04, 0x01, 0x00, 0xDC,
    0x39, 0x88, 0x32, 0x0D, 0x83, 0x40, 0xD0, 0x64, 0x1B, 0x8C, 0xBC, 0x00,
    0x1F, 0x18, 0xD8, 0x6C, 0x0C, 0x06, 0x03, 0x01, 0x86, 0x66, 0x3E, 0x00,
    0x00, 0x20, 0x08, 0x01, 0x0F, 0x23, 0x14, 0xC1, 0x18, 0x26, 0x04, 0xC0,
    0x98, 0x23, 0x04, 0x71, 0x87, 0xD0, 0x0F, 0x0C, 0x76, 0x0D, 0x83, 0xFF,
    0xF0, 0x0C, 0x03, 0x06, 0x63, 0x0F, 0x80, 0x1C, 0xC2, 0x1E, 0x20, 0x84,
    0x10, 0x41, 0x04, 0x20, 0x80, 0x0F, 0x46, 0x33, 0x0C, 0xC1, 0x60, 0xD8,
    0x26, 0x09, 0x86, 0x71, 0x8F, 0xE0, 0x10, 0x04, 0xC2, 0x1F, 0x00, 0x10,
    0x04, 0x01, 0x00, 0x9F, 0x39, 0x88, 0x22, 0x09, 0x02, 0x40, 0x90, 0x44,
    0x12, 0x04, 0x81, 0x00, 0x10, 0x02, 0x22, 0x64, 0x44, 0x48, 0x80, 0x04,
    0x00, 0x01, 0x08, 0x20, 0x82, 0x08, 0x41, 0x04, 0x10, 0x42, 0x08, 0xE0,
    0x10, 0x08, 0x04, 0x04, 0x32, 0x31, 0x20, 0xA0, 0xB8, 0x6C, 0x22, 0x11,
    0x90, 0xC8, 0x30, 0x11, 0x22, 0x22, 0x64, 0x44, 0x48, 0x80, 0x2F, 0x3C,
    0x63, 0x8C, 0x86, 0x19, 0x08, 0x44, 0x10, 0x88, 0x21, 0x10, 0x82, 0x21,
    0x04, 0x82, 0x11, 0x04, 0x20, 0x00, 0x0B, 0xF3, 0x18, 0x82, 0x20, 0x90,
    0x24, 0x09, 0x04, 0x41, 0x20, 0x48, 0x10, 0x0F, 0x0C, 0x76, 0x0D, 0x83,
    0xC0, 0xF0, 0x3C, 0x1B, 0x06, 0xE3, 0x0F, 0x00, 0x17, 0xC3, 0x1C, 0x41,
    0x98, 0x32, 0x06, 0x40, 0xC8, 0x33, 0x06, 0x71, 0x8B, 0xC1, 0x00, 0x20,
    0x08, 0x01, 0x00, 0x00, 0x1E, 0xCC, 0x66, 0x09, 0x82, 0xC0, 0xB0, 0x4C,
    0x13, 0x04, 0x63, 0x0F, 0xC0, 0x20, 0x08, 0x02, 0x00, 0x80, 0x2C, 0x60,
    0x81, 0x04, 0x08, 0x10, 0x20, 0x81, 0x00, 0x1E, 0x33, 0x63, 0x60, 0x70,
    0x1E, 0x03, 0xC3, 0xC6, 0x7C, 0x22, 0xF2, 0x44, 0x44, 0xCC, 0xCE, 0x21,
    0x20, 0x90, 0x48, 0x24, 0x12, 0x13, 0x09, 0x84, 0xE6, 0x3E, 0x00, 0xC1,
    0xE1, 0xB0, 0xC8, 0xC4, 0x43, 0x61, 0xA0, 0xF0, 0x70, 0x18, 0x00, 0xC7,
    0x1E, 0x38, 0xB3, 0xCD, 0x96, 0x4C, 0xB6, 0x6D, 0xB1, 0x4D, 0x0E, 0x78,
    0x63, 0x83, 0x1C, 0x00, 0x10, 0xC3, 0x10, 0x24, 0x07, 0x80, 0xE0, 0x1C,
    0x07, 0x81, 0x90, 0x23, 0x08, 0x20, 0x30, 0x46, 0x18, 0x42, 0x08, 0xC1,
    0x10, 0x24, 0x07, 0x80, 0xE0, 0x1C, 0x03, 0x00, 0x60, 0x08, 0x03, 0x01,
    0xC0, 0x00, 0x3F, 0x80, 0x80, 0x80, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0,
    0x7F, 0x00, 0x18, 0x88, 0x42, 0x10, 0x88, 0xC3, 0x18, 0x88, 0x42, 0x18,
    0xE0, 0x11, 0x22, 0x22, 0x24, 0x44, 0x4C, 0x88, 0x88, 0x00, 0x38, 0xC2,
    0x10, 0x88, 0xC6, 0x18, 0x88, 0x42, 0x10, 0x88, 0xC0, 0x70, 0x4E, 0x41,
    0xC0};

const GFXglyph FreeSansOblique9pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 5, 0, 1},         // 0x20 ' '
    {0, 5, 13, 5, 2, -12},      // 0x21 '!'
    {9, 5, 4, 6, 3, -12},       // 0x22 '"'
    {12, 11, 13, 10, 1, -12},   // 0x23 '#'
    {30, 11, 16, 10, 1, -13},   // 0x24 '$'
    {52, 15, 13, 16, 2, -12},   // 0x25 '%'
    {77, 10, 13, 12, 2, -12},   // 0x26 '&'
    {94, 2, 4, 3, 3, -12},      // 0x27 '''
    {95, 7, 17, 6, 2, -12},     // 0x28 '('
    {110, 7, 17, 6, -1, -12},   // 0x29 ')'
    {125, 6, 5, 7, 3, -12},     // 0x2A '*'
    {129, 9, 8, 11, 2, -7},     // 0x2B '+'
    {138, 3, 5, 5, 1, -1},      // 0x2C ','
    {140, 4, 1, 6, 2, -4},      // 0x2D '-'
    {141, 2, 1, 5, 2, 0},       // 0x2E '.'
    {142, 8, 13, 5, 0, -12},    // 0x2F '/'
    {155, 9, 13, 10, 2, -12},   // 0x30 '0'
    {170, 5, 13, 10, 4, -12},   // 0x31 '1'
    {179, 11, 13, 10, 1, -12},  // 0x32 '2'
    {197, 10, 13, 10, 1, -12},  // 0x33 '3'
    {214, 9, 13, 10, 1, -12},   // 0x34 '4'
    {229, 11, 13, 10, 1, -12},  // 0x35 '5'
    {247, 10, 13, 10, 2, -12},  // 0x36 '6'
    {264, 10, 13, 10, 2, -12},  // 0x37 '7'
    {281, 10, 13, 10, 1, -12},  // 0x38 '8'
    {298, 10, 13, 10, 1, -12},  // 0x39 '9'
    {315, 4, 9, 5, 2, -8},      // 0x3A ':'
    {320, 5, 12, 5, 1, -8},     // 0x3B ';'
    {328, 9, 9, 11, 2, -8},     // 0x3C '<'
    {339, 10, 4, 11, 1, -5},    // 0x3D '='
    {344, 9, 9, 11, 1, -7},     // 0x3E '>'
    {355, 9, 13, 10, 3, -12},   // 0x3F '?'
    {370, 18, 16, 18, 1, -12},  // 0x40 '@'
    {406, 12, 13, 12, 0, -12},  // 0x41 'A'
    {426, 12, 13, 12, 1, -12},  // 0x42 'B'
    {446, 12, 13, 13, 2, -12},  // 0x43 'C'
    {466, 13, 13, 13, 1, -12},  // 0x44 'D'
    {488, 12, 13, 12, 1, -12},  // 0x45 'E'
    {508, 12, 13, 11, 1, -12},  // 0x46 'F'
    {528, 13, 13, 14, 2, -12},  // 0x47 'G'
    {550, 13, 13, 13, 1, -12},  // 0x48 'H'
    {572, 4, 13, 5, 2, -12},    // 0x49 'I'
    {579, 10, 13, 9, 1, -12},   // 0x4A 'J'
    {596, 14, 13, 12, 1, -12},  // 0x4B 'K'
    {619, 9, 13, 10, 1, -12},   // 0x4C 'L'
    {634, 16, 13, 15, 1, -12},  // 0x4D 'M'
    {660, 13, 13, 13, 1, -12},  // 0x4E 'N'
    {682, 13, 13, 14, 2, -12},  // 0x4F 'O'
    {704, 12, 13, 12, 1, -12},  // 0x50 'P'
    {724, 13, 14, 14, 2, -12},  // 0x51 'Q'
    {747, 13, 13, 13, 1, -12},  // 0x52 'R'
    {769, 12, 13, 12, 1, -12},  // 0x53 'S'
    {789, 10, 13, 11, 3, -12},  // 0x54 'T'
    {806, 12, 13, 13, 2, -12},  // 0x55 'U'
    {826, 11, 13, 12, 3, -12},  // 0x56 'V'
    {844, 16, 13, 17, 3, -12},  // 0x57 'W'
    {870, 14, 13, 12, 0, -12},  // 0x58 'X'
    {893, 12, 13, 12, 3, -12},  // 0x59 'Y'
    {913, 12, 13, 11, 1, -12},  // 0x5A 'Z'
    {933, 7, 17, 5, 0, -12},    // 0x5B '['
    {948, 2, 13, 5, 3, -12},    // 0x5C '\'
    {952, 7, 17, 5, 0, -12},    // 0x5D ']'
    {967, 7, 7, 8, 2, -12},     // 0x5E '^'
    {974, 11, 1, 10, -1, 3},    // 0x5F '_'
    {976, 3, 3, 6, 3, -12},     // 0x60 '`'
    {978, 10, 10, 10, 1, -9},   // 0x61 'a'
    {991, 10, 13, 10, 1, -12},  // 0x62 'b'
    {1008, 9, 10, 9, 1, -9},    // 0x63 'c'
    {1020, 11, 13, 10, 1, -12}, // 0x64 'd'
    {1038, 10, 10, 10, 1, -9},  // 0x65 'e'
    {1051, 6, 13, 5, 1, -12},   // 0x66 'f'
    {1061, 10, 14, 10, 0, -9},  // 0x67 'g'
    {1079, 10, 13, 10, 1, -12}, // 0x68 'h'
    {1096, 4, 13, 4, 1, -12},   // 0x69 'i'
    {1103, 6, 17, 4, -1, -12},  // 0x6A 'j'
    {1116, 9, 13, 9, 1, -12},   // 0x6B 'k'
    {1131, 4, 13, 4, 1, -12},   // 0x6C 'l'
    {1138, 15, 10, 15, 1, -9},  // 0x6D 'm'
    {1157, 10, 11, 10, 1, -10}, // 0x6E 'n'
    {1171, 10, 10, 10, 1, -9},  // 0x6F 'o'
    {1184, 11, 14, 10, 0, -9},  // 0x70 'p'
    {1204, 10, 14, 10, 1, -9},  // 0x71 'q'
    {1222, 7, 10, 6, 1, -9},    // 0x72 'r'
    {1231, 8, 10, 9, 1, -9},    // 0x73 's'
    {1241, 4, 12, 5, 2, -11},   // 0x74 't'
    {1247, 9, 10, 10, 2, -9},   // 0x75 'u'
    {1259, 9, 10, 9, 2, -9},    // 0x76 'v'
    {1271, 13, 10, 13, 2, -9},  // 0x77 'w'
    {1288, 11, 10, 9, 0, -9},   // 0x78 'x'
    {1302, 11, 14, 9, 0, -9},   // 0x79 'y'
    {1322, 9, 10, 9, 1, -9},    // 0x7A 'z'
    {1334, 5, 17, 6, 2, -12},   // 0x7B '{'
    {1345, 4, 17, 5, 1, -12},   // 0x7C '|'
    {1354, 5, 17, 6, 0, -12},   // 0x7D '}'
    {1365, 9, 3, 11, 2, -7}};   // 0x7E '~'

const GFXfont FreeSansOblique9pt7b PROGMEM = {
    (uint8_t *)FreeSansOblique9pt7bBitmaps,
    (GFXglyph *)FreeSansOblique9pt7bGlyphs, 0x20, 0x7E, 22};

// Approx. 2041 bytes

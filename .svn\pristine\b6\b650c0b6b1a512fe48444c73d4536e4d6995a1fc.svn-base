/**
 * @file spi_config.h
 * @brief SPI配置头文件
 * @details ESP32-S3 SPI主机模式配置和引脚定义
 */
#ifndef __SPI_CONFIG_H__
#define __SPI_CONFIG_H__

#include "driver/spi_master.h"
#include "driver/gpio.h"

// SPI默认DMA通道(ESP32S3使用自动分配)
#define SPI_DMA_CH_AUTO     0                ///< SPI DMA自动分配通道
#define LCD_DEF_DMA_CHAN    SPI_DMA_CH_AUTO  ///< LCD默认DMA通道
// SPI默认DMA最大传输字节数
#define LCD_DMA_MAX_SIZE    4092             ///< LCD DMA最大传输大小

// SPI2默认引脚定义 (ESP32-S3专用)
#define SPI2_DEF_PIN_NUM_MISO   GPIO_NUM_37  ///< SPI2默认MISO引脚
#define SPI2_DEF_PIN_NUM_MOSI   GPIO_NUM_35  ///< SPI2默认MOSI引脚
#define SPI2_DEF_PIN_NUM_CLK    GPIO_NUM_36  ///< SPI2默认CLK引脚

/**
 * @brief  配置SPIx主机模式，配置DMA通道、DMA字节大小，及 MISO、MOSI、CLK的引脚。
 *      - （注意：普通GPIO最大只能30MHz，而IOMUX默认的SPI-IO，CLK最大可以设置到80MHz）
 *      - 例：spi_master_init(SPI2_HOST, LCD_DEF_DMA_CHAN, LCD_DMA_MAX_SIZE, SPI2_DEF_PIN_NUM_MISO, SPI2_DEF_PIN_NUM_MOSI, SPI2_DEF_PIN_NUM_CLK);
 * 
 * @param  host_id SPI端口号。SPI1_HOST / SPI2_HOST / SPI3_HOST
 * @param  dma_chan 使用的DMA通道
 * @param  max_tran_size DMA最大的传输字节数（会根据此值给DMA分配内存，值越大分配给DMA的内存就越大，单次可用DMA传输的内容就越多）
 * @param  miso_io_num MISO端口号。除仅能做输入 和 6、7、8、9、10、11之外的任意端口，但仅IOMUX默认的SPI-IO才能达到最高80MHz上限。
 * @param  mosi_io_num MOSI端口号
 * @param  clk_io_num CLK端口号
 * 
 * @return
 *     - none
 */
void spi_master_init(spi_host_device_t host_id, int dma_chan, uint32_t max_tran_size, gpio_num_t miso_io_num, gpio_num_t mosi_io_num, gpio_num_t clk_io_num);

#endif

#ifndef BLE_MANAGER_H
#define BLE_MANAGER_H

#include <NimBLEDevice.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

#define BLE_DEVICE_NAME "ESP32-Stepper"                      ///< BLE设备名称
#define SERVICE_UUID "00001234-0000-1000-8000-00805f9b34fb" ///< BLE服务UUID
#define CHARACTERISTIC_UUID "0000abcd-0000-1000-8000-00805f9b34fb" ///< BLE特征UUID

extern bool motormode;    ///< 电机模式标志
extern float motor_speed; ///< 电机速度
extern int motor_steps;   ///< 电机步数

// 前向声明
class BLEManager;

/**
 * @brief BLE服务器回调类
 * @details 处理BLE连接和断开连接事件
 */
class BLEServerCallbacksImpl : public BLEServerCallbacks {
    BLEManager& manager; ///< BLE管理器引用
public:
    /**
     * @brief 构造函数
     * @param m BLE管理器引用
     */
    BLEServerCallbacksImpl(BLEManager& m) : manager(m) {}

    /**
     * @brief 客户端连接回调
     * @param pServer BLE服务器指针
     */
    void onConnect(BLEServer* pServer) override;

    /**
     * @brief 客户端断开连接回调
     * @param pServer BLE服务器指针
     */
    void onDisconnect(BLEServer* pServer) override;
};

/**
 * @brief BLE特征回调类
 * @details 处理BLE特征写入事件
 */
class BLECharacteristicCallbacksImpl : public BLECharacteristicCallbacks {
    BLEManager& manager; ///< BLE管理器引用
public:
    /**
     * @brief 构造函数
     * @param m BLE管理器引用
     */
    BLECharacteristicCallbacksImpl(BLEManager& m) : manager(m) {}

    /**
     * @brief 特征写入回调
     * @param pCharacteristic BLE特征指针
     * @details 处理客户端写入的控制命令
     */
    void onWrite(BLECharacteristic* pCharacteristic) override;
};

/**
 * @brief BLE管理类
 * @details 单例模式的BLE管理器，负责BLE服务器的初始化、启动、停止和数据通信
 */
class BLEManager {
public:
    /**
     * @brief 获取BLE管理器单例实例
     * @return BLE管理器引用
     */
    static BLEManager& getInstance();

    /**
     * @brief 初始化BLE管理器
     * @details 设置BLE服务器、服务和特征
     */
    void init();

    /**
     * @brief 启动BLE服务
     * @details 开始BLE广播，等待客户端连接
     */
    void start();

    /**
     * @brief 停止BLE服务
     * @details 停止BLE广播和服务
     */
    void stop();

    /**
     * @brief 检查BLE连接状态
     * @return 连接状态，true表示已连接
     */
    bool isConnected() const;

    /**
     * @brief 发送通知数据
     * @param value 要发送的数据字符串
     * @details 向已连接的客户端发送通知数据
     */
    void sendNotification(const std::string& value);

    /**
     * @brief 设置连接状态
     * @param connected 连接状态
     */
    void setConnected(bool connected);

private:
    /**
     * @brief 私有构造函数（单例模式）
     */
    BLEManager();

    /**
     * @brief 设置BLE服务器
     */
    void setupBLEServer();

    /**
     * @brief 设置BLE服务
     */
    void setupBLEService();

    /**
     * @brief 设置BLE特征
     */
    void setupBLECharacteristic();

    BLEServer* pServer;                                ///< BLE服务器指针
    BLEService* pService;                              ///< BLE服务指针
    BLECharacteristic* pCharacteristic;                ///< BLE特征指针
    bool deviceConnected;                              ///< 设备连接状态
    BLEServerCallbacksImpl serverCallbacks;            ///< 服务器回调实例
    BLECharacteristicCallbacksImpl charCallbacks;      ///< 特征回调实例
};

/**
 * @brief BLE任务函数
 * @param parameter FreeRTOS任务参数
 * @details BLE通信的主任务函数，运行在独立的FreeRTOS任务中
 */
void bleTask(void* parameter);

#endif

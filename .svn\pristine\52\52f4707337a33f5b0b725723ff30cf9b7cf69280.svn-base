#ifndef MOTOR_CONTROL_H
#define MOTOR_CONTROL_H

#include <Arduino.h>
#include "driver/ledc.h"
#include "pid_controller.h"
extern float curr_speed;
class Encoder;  // 前向声明

class MotorControl {
public:
    enum ControlMode {
        SPEED_MODE,
        POSITION_MODE
    };

    void init();
    void setOutput(float output);
    // void output(float theta, uint8_t effort);
    
    // 位置控制接口
    void setPosition(float target);
    void setControlMode(ControlMode mode);
    void updatePositionControl();
    float getCurrentPosition() const;
    float getTargetPosition() const;
    void setpositionmode();
    void setspeedmode();
    
private:
    static const int16_t sin_table[];
    static const uint8_t PWM_FREQ;
    static const ledc_timer_bit_t PWM_RESOLUTION;
    static const ledc_channel_t PWM_CHANNEL_A;
    static const ledc_channel_t PWM_CHANNEL_B;
    
    ControlMode currentMode = SPEED_MODE;
    float targetPosition = 0;
    float currentSpeed = 0;
    float targetSpeed = 0;
    float maxAcceleration = 500.0f; // steps/s^2
    unsigned long lastUpdateTime = 0;
    PIDController pidController = PIDController(1.0, 0.1, 0.05); // PID参数可调整
    Encoder* encoder = nullptr;
};

#endif

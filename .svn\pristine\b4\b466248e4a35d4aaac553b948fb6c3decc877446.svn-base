#ifndef SERIAL_COMMAND_H
#define SERIAL_COMMAND_H

#include "pid_controller.h"
#include "motor_control.h"
#include "encoder.h"

/**
 * @brief 串口命令处理类
 * @details 负责处理通过串口接收的各种控制命令，包括PID参数设置、
 *          电机控制、编码器校准和Skywatcher协议命令
 */
class SerialCommand {
public:
    /**
     * @brief 初始化串口命令系统
     * @details 配置串口参数并初始化相关GPIO引脚
     */
    static void setup();

    /**
     * @brief 处理串口命令
     * @details 读取并解析串口数据，执行相应的控制命令
     * @param pid PID控制器引用
     * @param encoder 编码器引用
     * @param motor 电机控制器引用
     */
    static void process(PIDController& pid, Encoder& encoder, MotorControl& motor);

private:
    /**
     * @brief 处理PID参数设置命令
     * @param cmd 命令字符（p/i/d）
     * @param value 参数值
     * @param pid PID控制器引用
     */
    static void handlePIDCommand(char cmd, float value, PIDController& pid);

    /**
     * @brief 处理编码器校准命令
     * @param encoder 编码器引用
     * @param motor 电机控制器引用
     */
    static void handleCalibrateCommand(Encoder& encoder, MotorControl& motor);

    /**
     * @brief 处理状态查询命令
     * @param pid PID控制器引用
     * @param encoder 编码器引用
     * @param motor 电机控制器引用
     */
    static void handleStatusCommand(PIDController& pid, Encoder& encoder, MotorControl& motor);

    /**
     * @brief 处理位置控制电机命令
     * @param pid PID控制器引用
     * @param encoder 编码器引用
     * @param motor 电机控制器引用
     */
    static void position_control_motorCommand(PIDController& pid, Encoder& encoder, MotorControl& motor);

    /**
     * @brief 处理Skywatcher协议命令（String版本）
     * @param command 命令字符串
     * @param pid PID控制器引用
     * @param encoder 编码器引用
     * @param motor 电机控制器引用
     */
    static void processSkywatcherCommand(String command, PIDController& pid, Encoder& encoder, MotorControl& motor);

    /**
     * @brief 处理Skywatcher协议命令（快速版本）
     * @param command 命令字符数组
     * @param pid PID控制器引用
     * @param encoder 编码器引用
     * @param motor 电机控制器引用
     */
    static void processSkywatcherCommandFast(char* command, PIDController& pid, Encoder& encoder, MotorControl& motor);

    /**
     * @brief 将长整型转换为十六进制字符串
     * @param value 要转换的长整型值
     * @return 十六进制字符串
     */
    static String longToHexString(long value);

    /**
     * @brief 将十六进制字符串转换为长整型
     * @param hexStr 十六进制字符串
     * @return 转换后的长整型值
     */
    static long hexStringToLong(String hexStr);
};

#endif

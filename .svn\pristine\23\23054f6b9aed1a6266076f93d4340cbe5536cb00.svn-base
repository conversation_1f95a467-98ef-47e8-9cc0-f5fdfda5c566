#pragma once
#include <Adafruit_GFX.h>

const uint8_t FreeSerifBoldItalic18pt7bBitmaps[] PROGMEM = {
    0x01, 0xC0, 0x7C, 0x0F, 0x81, 0xF0, 0x3E, 0x07, 0x80, 0xF0, 0x3C, 0x07,
    0x80, 0xE0, 0x1C, 0x03, 0x00, 0x60, 0x0C, 0x03, 0x00, 0x60, 0x08, 0x00,
    0x00, 0x00, 0x00, 0x07, 0x81, 0xF8, 0x3F, 0x07, 0xE0, 0x78, 0x00, 0x38,
    0x1D, 0xE0, 0xF7, 0x83, 0xDC, 0x0E, 0x70, 0x39, 0xC0, 0xE6, 0x03, 0x18,
    0x0C, 0x40, 0x23, 0x01, 0x80, 0x00, 0x38, 0x60, 0x07, 0x0E, 0x00, 0x70,
    0xC0, 0x06, 0x1C, 0x00, 0xE1, 0xC0, 0x0E, 0x38, 0x01, 0xC3, 0x81, 0xFF,
    0xFF, 0x1F, 0xFF, 0xE1, 0xFF, 0xFE, 0x03, 0x86, 0x00, 0x30, 0xE0, 0x07,
    0x0E, 0x00, 0x71, 0xC0, 0x0E, 0x1C, 0x0F, 0xFF, 0xF8, 0xFF, 0xFF, 0x0F,
    0xFF, 0xF0, 0x1C, 0x30, 0x01, 0x87, 0x00, 0x38, 0x70, 0x03, 0x0E, 0x00,
    0x70, 0xE0, 0x07, 0x0C, 0x00, 0xE1, 0xC0, 0x00, 0x00, 0x08, 0x00, 0x0C,
    0x00, 0x7E, 0x00, 0xFF, 0xC0, 0xF3, 0x70, 0x71, 0x9C, 0x70, 0xC6, 0x38,
    0x43, 0x1C, 0x61, 0x0F, 0x30, 0x87, 0xD8, 0x03, 0xF8, 0x00, 0xFE, 0x00,
    0x3F, 0x80, 0x0F, 0xE0, 0x03, 0xF8, 0x01, 0xFC, 0x00, 0xDF, 0x10, 0x47,
    0x88, 0x63, 0xCC, 0x31, 0xE6, 0x10, 0xF3, 0x98, 0x71, 0xCC, 0x78, 0x7E,
    0x78, 0x07, 0xF8, 0x03, 0xF0, 0x01, 0x80, 0x00, 0xC0, 0x00, 0x03, 0xC0,
    0x18, 0x01, 0xFE, 0x0F, 0x00, 0x7C, 0xFF, 0xC0, 0x1F, 0x0F, 0x98, 0x07,
    0xC1, 0x06, 0x00, 0xF8, 0x21, 0x80, 0x3E, 0x04, 0x30, 0x07, 0xC1, 0x8C,
    0x00, 0xF0, 0x21, 0x80, 0x1E, 0x0C, 0x60, 0x03, 0xC1, 0x0C, 0x00, 0x78,
    0xC3, 0x03, 0xC7, 0xF8, 0x61, 0xFC, 0x7C, 0x18, 0x7C, 0xC0, 0x06, 0x1F,
    0x08, 0x00, 0xC7, 0xC1, 0x00, 0x30, 0xF0, 0x20, 0x06, 0x3E, 0x04, 0x01,
    0x87, 0xC1, 0x00, 0x30, 0xF0, 0x20, 0x0C, 0x1E, 0x0C, 0x03, 0x03, 0xC1,
    0x00, 0x60, 0x3C, 0xC0, 0x18, 0x07, 0xF8, 0x03, 0x00, 0x7C, 0x00, 0x00,
    0x0F, 0x80, 0x00, 0x1F, 0xF0, 0x00, 0x1E, 0x38, 0x00, 0x0E, 0x0E, 0x00,
    0x0F, 0x07, 0x00, 0x07, 0x83, 0x80, 0x03, 0xC3, 0x80, 0x01, 0xE3, 0x80,
    0x00, 0xF7, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x7F, 0x0F,
    0xF0, 0xE7, 0x81, 0xE0, 0xE3, 0xE0, 0xE0, 0xE1, 0xF0, 0x60, 0xE0, 0x7C,
    0x60, 0xF0, 0x3E, 0x20, 0x78, 0x1F, 0xB0, 0x3C, 0x07, 0xF0, 0x1F, 0x03,
    0xF0, 0x0F, 0x80, 0xFC, 0x03, 0xF0, 0x7F, 0x8D, 0xFF, 0xEF, 0xFC, 0x7F,
    0xE3, 0xFC, 0x0F, 0xC0, 0x78, 0x00, 0x3B, 0xDE, 0xE7, 0x39, 0x8C, 0x46,
    0x00, 0x00, 0x60, 0x18, 0x06, 0x01, 0x80, 0x60, 0x1C, 0x07, 0x01, 0xE0,
    0x38, 0x0F, 0x01, 0xC0, 0x38, 0x0F, 0x01, 0xE0, 0x38, 0x07, 0x00, 0xE0,
    0x1C, 0x03, 0x80, 0x70, 0x0E, 0x00, 0xC0, 0x18, 0x03, 0x00, 0x60, 0x06,
    0x00, 0xC0, 0x08, 0x00, 0x80, 0x10, 0x00, 0x06, 0x00, 0x40, 0x04, 0x00,
    0x80, 0x18, 0x01, 0x00, 0x30, 0x06, 0x00, 0xC0, 0x1C, 0x03, 0x80, 0x70,
    0x0E, 0x01, 0xC0, 0x38, 0x07, 0x01, 0xE0, 0x3C, 0x07, 0x00, 0xE0, 0x3C,
    0x07, 0x00, 0xE0, 0x38, 0x06, 0x01, 0xC0, 0x70, 0x18, 0x06, 0x01, 0x80,
    0x00, 0x07, 0x00, 0x38, 0x01, 0xC1, 0x8E, 0x3E, 0x23, 0xF9, 0x3F, 0xEB,
    0xE0, 0xE0, 0xFF, 0xF7, 0x93, 0xF8, 0x9F, 0x8E, 0x60, 0x70, 0x03, 0x80,
    0x08, 0x00, 0x01, 0xC0, 0x00, 0xE0, 0x00, 0x70, 0x00, 0x38, 0x00, 0x1C,
    0x00, 0x0E, 0x00, 0x07, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0,
    0x70, 0x00, 0x38, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x07, 0x00, 0x03, 0x80,
    0x01, 0xC0, 0x00, 0x1C, 0x7C, 0xF9, 0xF1, 0xE1, 0xC3, 0x0C, 0x30, 0xC2,
    0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xE0, 0x7B, 0xFF, 0xFF, 0x78, 0x00, 0x1C,
    0x00, 0xE0, 0x03, 0x80, 0x1E, 0x00, 0x70, 0x01, 0xC0, 0x0E, 0x00, 0x38,
    0x01, 0xC0, 0x07, 0x00, 0x38, 0x00, 0xE0, 0x07, 0x80, 0x1C, 0x00, 0x70,
    0x03, 0x80, 0x0E, 0x00, 0x70, 0x01, 0xC0, 0x0E, 0x00, 0x38, 0x01, 0xC0,
    0x07, 0x00, 0x1C, 0x00, 0xE0, 0x00, 0x00, 0xF0, 0x07, 0x30, 0x1C, 0x30,
    0x78, 0x60, 0xE0, 0xE3, 0xC1, 0xCF, 0x83, 0x9E, 0x0F, 0x3C, 0x1E, 0xF8,
    0x3D, 0xE0, 0x7B, 0xC1, 0xFF, 0x83, 0xFF, 0x07, 0xBC, 0x0F, 0x78, 0x3E,
    0xF0, 0x7D, 0xE0, 0xF3, 0x81, 0xE7, 0x07, 0x8E, 0x0F, 0x0C, 0x3C, 0x18,
    0x70, 0x19, 0xC0, 0x1E, 0x00, 0x00, 0x06, 0x01, 0xF8, 0x1F, 0xF0, 0x03,
    0xE0, 0x07, 0x80, 0x1F, 0x00, 0x3E, 0x00, 0x7C, 0x00, 0xF0, 0x03, 0xE0,
    0x07, 0xC0, 0x0F, 0x80, 0x1E, 0x00, 0x7C, 0x00, 0xF8, 0x01, 0xE0, 0x07,
    0xC0, 0x0F, 0x80, 0x1F, 0x00, 0x3C, 0x00, 0xF8, 0x01, 0xF0, 0x03, 0xE0,
    0x0F, 0xC0, 0xFF, 0xF0, 0x00, 0xF8, 0x01, 0xFC, 0x03, 0xFE, 0x06, 0x3F,
    0x08, 0x1F, 0x18, 0x0F, 0x00, 0x0F, 0x00, 0x0F, 0x00, 0x0F, 0x00, 0x0E,
    0x00, 0x1E, 0x00, 0x1C, 0x00, 0x38, 0x00, 0x30, 0x00, 0x70, 0x00, 0xC0,
    0x01, 0x80, 0x03, 0x00, 0x06, 0x02, 0x0C, 0x06, 0x08, 0x0C, 0x1F, 0xFC,
    0x3F, 0xFC, 0x7F, 0xF8, 0xFF, 0xF8, 0x00, 0xF0, 0x07, 0xF8, 0x1F, 0xF0,
    0x61, 0xF0, 0x81, 0xE0, 0x03, 0xC0, 0x07, 0x80, 0x0E, 0x00, 0x3C, 0x00,
    0xE0, 0x07, 0xC0, 0x3F, 0xC0, 0x1F, 0x80, 0x0F, 0x80, 0x1F, 0x00, 0x1E,
    0x00, 0x3C, 0x00, 0x78, 0x00, 0xF0, 0x01, 0xC0, 0x07, 0x9C, 0x0E, 0x3C,
    0x38, 0x7F, 0xE0, 0x7E, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x70, 0x00, 0x3C,
    0x00, 0x1E, 0x00, 0x0F, 0x80, 0x07, 0xE0, 0x02, 0xF8, 0x01, 0x3C, 0x00,
    0x9F, 0x00, 0x47, 0xC0, 0x31, 0xE0, 0x18, 0x78, 0x0C, 0x3E, 0x06, 0x0F,
    0x83, 0x03, 0xC1, 0x80, 0xF0, 0x7F, 0xFF, 0x1F, 0xFF, 0xCF, 0xFF, 0xF0,
    0x03, 0xE0, 0x00, 0xF8, 0x00, 0x3C, 0x00, 0x0F, 0x00, 0x07, 0xC0, 0x01,
    0xFF, 0x00, 0xFF, 0x80, 0xFF, 0xC0, 0x7F, 0xE0, 0x60, 0x00, 0x30, 0x00,
    0x10, 0x00, 0x1F, 0x00, 0x0F, 0xE0, 0x0F, 0xF8, 0x07, 0xFE, 0x00, 0x3F,
    0x00, 0x07, 0xC0, 0x01, 0xE0, 0x00, 0xF0, 0x00, 0x38, 0x00, 0x1C, 0x00,
    0x0E, 0x00, 0x06, 0x00, 0x03, 0x00, 0x03, 0x87, 0x83, 0x83, 0xE3, 0x81,
    0xFF, 0x80, 0x3F, 0x00, 0x00, 0x00, 0x03, 0x80, 0x0F, 0x80, 0x1F, 0x00,
    0x3E, 0x00, 0x3E, 0x00, 0x3E, 0x00, 0x3E, 0x00, 0x3E, 0x00, 0x1F, 0x00,
    0x1F, 0xF0, 0x1F, 0xFE, 0x0F, 0xCF, 0x07, 0xC3, 0xC7, 0xE1, 0xE3, 0xE0,
    0xF1, 0xF0, 0x78, 0xF8, 0x3C, 0x78, 0x3E, 0x3C, 0x1F, 0x1E, 0x0F, 0x0F,
    0x0F, 0x83, 0x87, 0x81, 0xE7, 0x80, 0x7F, 0x80, 0x0F, 0x80, 0x00, 0x3F,
    0xFF, 0x3F, 0xFE, 0x3F, 0xFE, 0x7F, 0xFC, 0x60, 0x1C, 0x80, 0x38, 0x80,
    0x30, 0x00, 0x70, 0x00, 0x60, 0x00, 0xE0, 0x01, 0xC0, 0x01, 0xC0, 0x03,
    0x80, 0x03, 0x80, 0x07, 0x00, 0x0E, 0x00, 0x0E, 0x00, 0x1C, 0x00, 0x1C,
    0x00, 0x38, 0x00, 0x38, 0x00, 0x70, 0x00, 0xF0, 0x00, 0xE0, 0x00, 0x00,
    0xF8, 0x00, 0xFF, 0x00, 0xE1, 0xC0, 0xE0, 0xF0, 0xF0, 0x38, 0x78, 0x1C,
    0x3C, 0x0E, 0x1F, 0x07, 0x0F, 0x87, 0x07, 0xE7, 0x01, 0xFF, 0x00, 0x7E,
    0x00, 0x1F, 0x80, 0x3F, 0xE0, 0x73, 0xF0, 0x70, 0xFC, 0x70, 0x3E, 0x70,
    0x0F, 0x38, 0x07, 0x9C, 0x03, 0xCE, 0x01, 0xE7, 0x00, 0xE1, 0xC0, 0xE0,
    0x70, 0xE0, 0x0F, 0xC0, 0x00, 0x00, 0xF8, 0x01, 0xFF, 0x01, 0xF3, 0xC1,
    0xF0, 0xE1, 0xF0, 0x70, 0xF0, 0x3C, 0xF8, 0x1E, 0x7C, 0x0F, 0x3C, 0x0F,
    0x9E, 0x07, 0xCF, 0x03, 0xE7, 0x83, 0xF3, 0xC1, 0xF0, 0xF1, 0xF8, 0x3F,
    0xF8, 0x0F, 0xFC, 0x00, 0x7C, 0x00, 0x7C, 0x00, 0x7E, 0x00, 0x3E, 0x00,
    0x3C, 0x00, 0x7C, 0x00, 0x7C, 0x00, 0xF0, 0x00, 0xC0, 0x00, 0x00, 0x07,
    0x83, 0xF0, 0xFC, 0x3F, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x78, 0x3F, 0x0F, 0xC3, 0xF0, 0x78, 0x00, 0x03, 0xC0, 0xFC,
    0x1F, 0x83, 0xF0, 0x3C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x0C, 0x03, 0xC0, 0x7C, 0x0F, 0x80, 0xF0, 0x0E, 0x01, 0x80, 0x30, 0x0C,
    0x03, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0x7C, 0x00,
    0x7F, 0x00, 0x7F, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFE, 0x00, 0xFE, 0x00,
    0x3E, 0x00, 0x0F, 0xC0, 0x01, 0xFC, 0x00, 0x1F, 0xE0, 0x01, 0xFE, 0x00,
    0x0F, 0xE0, 0x00, 0xFF, 0x00, 0x0F, 0xC0, 0x00, 0xF0, 0x00, 0x04, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00,
    0x38, 0x00, 0x0F, 0x80, 0x03, 0xF8, 0x00, 0x3F, 0x80, 0x03, 0xFC, 0x00,
    0x3F, 0xC0, 0x01, 0xFC, 0x00, 0x1F, 0xC0, 0x01, 0xF0, 0x00, 0xFC, 0x00,
    0xFE, 0x01, 0xFE, 0x01, 0xFE, 0x01, 0xFC, 0x03, 0xFC, 0x00, 0xFC, 0x00,
    0x3C, 0x00, 0x08, 0x00, 0x00, 0x07, 0xC0, 0xFF, 0x0E, 0x3C, 0x70, 0xF3,
    0xC7, 0x8C, 0x3C, 0x01, 0xE0, 0x1F, 0x00, 0xF0, 0x07, 0x80, 0x78, 0x07,
    0x80, 0x30, 0x03, 0x00, 0x10, 0x01, 0x80, 0x08, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x07, 0x80, 0x7E, 0x03, 0xF0, 0x1F, 0x80, 0x78, 0x00, 0x00, 0x3F,
    0x80, 0x00, 0xFF, 0xF8, 0x01, 0xF0, 0x1E, 0x01, 0xE0, 0x03, 0x81, 0xC0,
    0x00, 0xE1, 0xC0, 0x18, 0x38, 0xE0, 0x3F, 0xCC, 0xE0, 0x3C, 0xE7, 0x70,
    0x3C, 0x71, 0xF0, 0x1C, 0x30, 0xF8, 0x1E, 0x38, 0x7C, 0x0E, 0x1C, 0x3E,
    0x0F, 0x0E, 0x1F, 0x07, 0x0E, 0x0F, 0x83, 0x87, 0x0D, 0xC1, 0xC7, 0x86,
    0x70, 0xE5, 0xC6, 0x38, 0x7C, 0xFE, 0x1C, 0x1C, 0x3E, 0x07, 0x00, 0x00,
    0x01, 0xC0, 0x00, 0x00, 0x78, 0x00, 0x40, 0x1F, 0x00, 0xE0, 0x03, 0xFF,
    0xE0, 0x00, 0x3F, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x03, 0x00, 0x00,
    0x0E, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x7C, 0x00, 0x00, 0xF8, 0x00, 0x03,
    0xF0, 0x00, 0x0F, 0xE0, 0x00, 0x17, 0xC0, 0x00, 0x67, 0x80, 0x00, 0x8F,
    0x00, 0x03, 0x1F, 0x00, 0x0C, 0x3E, 0x00, 0x10, 0x7C, 0x00, 0x60, 0xF8,
    0x00, 0x81, 0xF0, 0x03, 0xFF, 0xE0, 0x0F, 0xFF, 0xE0, 0x18, 0x07, 0xC0,
    0x60, 0x0F, 0x81, 0xC0, 0x1F, 0x03, 0x00, 0x3E, 0x0E, 0x00, 0x7C, 0x3C,
    0x00, 0xFC, 0xFE, 0x0F, 0xFE, 0x07, 0xFF, 0xE0, 0x01, 0xFF, 0xFC, 0x01,
    0xF8, 0x7E, 0x01, 0xF8, 0x3F, 0x01, 0xF0, 0x3F, 0x01, 0xF0, 0x3F, 0x01,
    0xF0, 0x3F, 0x03, 0xE0, 0x3F, 0x03, 0xE0, 0x7E, 0x03, 0xE0, 0xFC, 0x03,
    0xE3, 0xF0, 0x07, 0xFF, 0x80, 0x07, 0xC3, 0xE0, 0x07, 0xC1, 0xF8, 0x0F,
    0xC0, 0xF8, 0x0F, 0x80, 0xFC, 0x0F, 0x80, 0xFC, 0x0F, 0x80, 0xFC, 0x1F,
    0x80, 0xFC, 0x1F, 0x01, 0xFC, 0x1F, 0x01, 0xF8, 0x1F, 0x03, 0xF0, 0x3F,
    0x0F, 0xE0, 0x7F, 0xFF, 0xC0, 0xFF, 0xFE, 0x00, 0x00, 0x1F, 0x82, 0x01,
    0xFF, 0xE8, 0x07, 0xE0, 0xF0, 0x3F, 0x80, 0xE0, 0xFE, 0x00, 0xC1, 0xF8,
    0x01, 0x87, 0xE0, 0x02, 0x1F, 0x80, 0x04, 0x3F, 0x00, 0x00, 0xFC, 0x00,
    0x01, 0xF8, 0x00, 0x07, 0xF0, 0x00, 0x0F, 0xE0, 0x00, 0x1F, 0x80, 0x00,
    0x3F, 0x00, 0x00, 0x7E, 0x00, 0x00, 0xFC, 0x00, 0x01, 0xF8, 0x00, 0x03,
    0xF0, 0x00, 0x03, 0xE0, 0x01, 0x07, 0xE0, 0x06, 0x07, 0xE0, 0x18, 0x07,
    0xE0, 0xE0, 0x07, 0xFF, 0x00, 0x01, 0xF8, 0x00, 0x07, 0xFF, 0xE0, 0x01,
    0xFF, 0xFE, 0x00, 0x1F, 0x87, 0xE0, 0x07, 0xE0, 0x7C, 0x01, 0xF0, 0x1F,
    0x80, 0x7C, 0x03, 0xE0, 0x1F, 0x00, 0xF8, 0x0F, 0x80, 0x3F, 0x03, 0xE0,
    0x0F, 0xC0, 0xF8, 0x03, 0xF0, 0x3E, 0x00, 0xFC, 0x1F, 0x00, 0x3F, 0x07,
    0xC0, 0x0F, 0xC1, 0xF0, 0x07, 0xF0, 0xFC, 0x01, 0xF8, 0x3E, 0x00, 0x7E,
    0x0F, 0x80, 0x3F, 0x83, 0xE0, 0x0F, 0xC1, 0xF8, 0x07, 0xF0, 0x7C, 0x01,
    0xF8, 0x1F, 0x00, 0xFC, 0x07, 0xC0, 0x7E, 0x03, 0xF0, 0x7E, 0x01, 0xFF,
    0xFF, 0x00, 0xFF, 0xFE, 0x00, 0x00, 0x07, 0xFF, 0xFE, 0x03, 0xFF, 0xFC,
    0x07, 0xE0, 0x78, 0x0F, 0xC0, 0x60, 0x1F, 0x00, 0x40, 0x3E, 0x00, 0x80,
    0x7C, 0x01, 0x01, 0xF8, 0x10, 0x03, 0xE0, 0x60, 0x07, 0xC3, 0x80, 0x0F,
    0xFF, 0x00, 0x3F, 0xFE, 0x00, 0x7C, 0x38, 0x00, 0xF8, 0x30, 0x03, 0xF0,
    0x60, 0x07, 0xC0, 0x80, 0x0F, 0x81, 0x00, 0x1F, 0x00, 0x10, 0x7E, 0x00,
    0x60, 0xF8, 0x01, 0xC1, 0xF0, 0x07, 0x03, 0xE0, 0x1E, 0x0F, 0xC0, 0xFC,
    0x3F, 0xFF, 0xF8, 0xFF, 0xFF, 0xE0, 0x07, 0xFF, 0xFE, 0x03, 0xFF, 0xFC,
    0x07, 0xE0, 0x78, 0x0F, 0xC0, 0x60, 0x1F, 0x00, 0x40, 0x3E, 0x00, 0x80,
    0x7C, 0x01, 0x01, 0xF8, 0x20, 0x03, 0xE0, 0xC0, 0x07, 0xC3, 0x80, 0x0F,
    0xFE, 0x00, 0x3F, 0xFC, 0x00, 0x7C, 0x38, 0x00, 0xF8, 0x30, 0x03, 0xF0,
    0x60, 0x07, 0xC0, 0x80, 0x0F, 0x81, 0x00, 0x1F, 0x00, 0x00, 0x7E, 0x00,
    0x00, 0xF8, 0x00, 0x01, 0xF0, 0x00, 0x03, 0xE0, 0x00, 0x0F, 0xC0, 0x00,
    0x3F, 0x80, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0x1F, 0xC2, 0x00, 0xFF, 0xF6,
    0x01, 0xF8, 0x3C, 0x03, 0xE0, 0x1C, 0x0F, 0xC0, 0x0C, 0x0F, 0xC0, 0x08,
    0x1F, 0x80, 0x08, 0x3F, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x7E, 0x00, 0x00,
    0x7E, 0x00, 0x00, 0x7E, 0x00, 0x00, 0xFE, 0x00, 0x00, 0xFC, 0x03, 0xFF,
    0xFC, 0x00, 0xFC, 0xFC, 0x00, 0xF8, 0xFC, 0x00, 0xF8, 0xFC, 0x00, 0xF8,
    0xFC, 0x00, 0xF0, 0x7C, 0x01, 0xF0, 0x7E, 0x01, 0xF0, 0x3E, 0x01, 0xF0,
    0x1F, 0x83, 0xE0, 0x0F, 0xFF, 0x80, 0x01, 0xFC, 0x00, 0x07, 0xFF, 0x3F,
    0xF8, 0x0F, 0xE0, 0x7F, 0x00, 0x7E, 0x01, 0xF8, 0x03, 0xF0, 0x0F, 0x80,
    0x1F, 0x00, 0x7C, 0x00, 0xF8, 0x07, 0xE0, 0x07, 0xC0, 0x3E, 0x00, 0x7E,
    0x01, 0xF0, 0x03, 0xE0, 0x0F, 0x80, 0x1F, 0x00, 0xF8, 0x00, 0xF8, 0x07,
    0xC0, 0x0F, 0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xF0, 0x03, 0xE0, 0x1F, 0x00,
    0x3F, 0x00, 0xF8, 0x01, 0xF0, 0x07, 0xC0, 0x0F, 0x80, 0x7E, 0x00, 0x7C,
    0x03, 0xE0, 0x07, 0xE0, 0x1F, 0x00, 0x3E, 0x00, 0xF8, 0x01, 0xF0, 0x0F,
    0xC0, 0x0F, 0x80, 0x7C, 0x00, 0xFC, 0x03, 0xE0, 0x0F, 0xE0, 0x3F, 0x80,
    0xFF, 0xC7, 0xFF, 0x00, 0x07, 0xFE, 0x03, 0xF8, 0x07, 0xE0, 0x0F, 0xC0,
    0x1F, 0x00, 0x3E, 0x00, 0x7C, 0x01, 0xF0, 0x03, 0xE0, 0x07, 0xC0, 0x0F,
    0x80, 0x3E, 0x00, 0x7C, 0x00, 0xF8, 0x03, 0xF0, 0x07, 0xC0, 0x0F, 0x80,
    0x1F, 0x00, 0x7C, 0x00, 0xF8, 0x01, 0xF0, 0x03, 0xE0, 0x0F, 0xC0, 0x3F,
    0x80, 0xFF, 0xC0, 0x00, 0x3F, 0xF0, 0x01, 0xFE, 0x00, 0x0F, 0xC0, 0x00,
    0xF8, 0x00, 0x0F, 0x80, 0x00, 0xF8, 0x00, 0x1F, 0x80, 0x01, 0xF0, 0x00,
    0x1F, 0x00, 0x01, 0xF0, 0x00, 0x3E, 0x00, 0x03, 0xE0, 0x00, 0x3E, 0x00,
    0x07, 0xE0, 0x00, 0x7C, 0x00, 0x07, 0xC0, 0x00, 0x7C, 0x00, 0x0F, 0xC0,
    0x00, 0xF8, 0x00, 0x0F, 0x80, 0x00, 0xF8, 0x00, 0x1F, 0x00, 0x61, 0xF0,
    0x0F, 0x3F, 0x00, 0xE7, 0xE0, 0x07, 0xFC, 0x00, 0x3F, 0x00, 0x00, 0x07,
    0xFF, 0x3F, 0x80, 0xFE, 0x07, 0x80, 0x7E, 0x03, 0x00, 0x3F, 0x03, 0x00,
    0x1F, 0x03, 0x00, 0x0F, 0x83, 0x00, 0x07, 0xC3, 0x00, 0x07, 0xE3, 0x00,
    0x03, 0xE3, 0x00, 0x01, 0xF3, 0x00, 0x00, 0xFB, 0x80, 0x00, 0xFB, 0xC0,
    0x00, 0x7F, 0xE0, 0x00, 0x3E, 0xF8, 0x00, 0x3F, 0x7C, 0x00, 0x1F, 0x1F,
    0x00, 0x0F, 0x8F, 0x80, 0x07, 0xC7, 0xE0, 0x07, 0xE1, 0xF0, 0x03, 0xE0,
    0xFC, 0x01, 0xF0, 0x3E, 0x00, 0xF8, 0x1F, 0x00, 0xFC, 0x07, 0xC0, 0xFE,
    0x07, 0xF0, 0xFF, 0xCF, 0xFC, 0x00, 0x07, 0xFF, 0x00, 0x07, 0xF0, 0x00,
    0x1F, 0x80, 0x00, 0x7E, 0x00, 0x01, 0xF0, 0x00, 0x07, 0xC0, 0x00, 0x1F,
    0x00, 0x00, 0xF8, 0x00, 0x03, 0xE0, 0x00, 0x0F, 0x80, 0x00, 0x3E, 0x00,
    0x01, 0xF0, 0x00, 0x07, 0xC0, 0x00, 0x1F, 0x00, 0x00, 0xFC, 0x00, 0x03,
    0xE0, 0x00, 0x0F, 0x80, 0x00, 0x3E, 0x00, 0x11, 0xF0, 0x00, 0xC7, 0xC0,
    0x06, 0x1F, 0x00, 0x38, 0x7C, 0x01, 0xE3, 0xF0, 0x3F, 0x9F, 0xFF, 0xFC,
    0xFF, 0xFF, 0xF0, 0x07, 0xF8, 0x00, 0x7F, 0x80, 0xFC, 0x00, 0x3F, 0x80,
    0x3E, 0x00, 0x3F, 0x80, 0x1F, 0x00, 0x3F, 0x80, 0x1F, 0x80, 0x1F, 0xC0,
    0x0F, 0xE0, 0x1B, 0xE0, 0x07, 0xF0, 0x0D, 0xF0, 0x02, 0xF8, 0x0D, 0xF0,
    0x03, 0x7C, 0x0C, 0xF8, 0x01, 0xBE, 0x06, 0x7C, 0x00, 0xDF, 0x06, 0x7C,
    0x00, 0xCF, 0x83, 0x3E, 0x00, 0x67, 0xC3, 0x1F, 0x00, 0x31, 0xE3, 0x0F,
    0x80, 0x38, 0xF9, 0x8F, 0x80, 0x18, 0x7D, 0x87, 0xC0, 0x0C, 0x3F, 0x83,
    0xE0, 0x06, 0x1F, 0xC1, 0xF0, 0x06, 0x0F, 0xC1, 0xF0, 0x03, 0x07, 0xC0,
    0xF8, 0x01, 0x83, 0xE0, 0x7C, 0x01, 0xC0, 0xE0, 0x7E, 0x00, 0xE0, 0x70,
    0x3F, 0x00, 0xF8, 0x30, 0x3F, 0x80, 0xFF, 0x10, 0x7F, 0xF0, 0x00, 0x07,
    0xF0, 0x0F, 0xE0, 0x3E, 0x00, 0x78, 0x07, 0xE0, 0x06, 0x00, 0x7C, 0x00,
    0xC0, 0x1F, 0xC0, 0x10, 0x03, 0xF8, 0x06, 0x00, 0x6F, 0x80, 0xC0, 0x19,
    0xF0, 0x10, 0x03, 0x3F, 0x02, 0x00, 0x63, 0xE0, 0xC0, 0x0C, 0x7C, 0x18,
    0x03, 0x07, 0xC2, 0x00, 0x60, 0xF8, 0x40, 0x0C, 0x0F, 0x98, 0x03, 0x81,
    0xF3, 0x00, 0x60, 0x3F, 0x40, 0x0C, 0x03, 0xF8, 0x01, 0x80, 0x7F, 0x00,
    0x60, 0x07, 0xC0, 0x0C, 0x00, 0xF8, 0x01, 0x80, 0x0F, 0x00, 0x70, 0x01,
    0xE0, 0x0E, 0x00, 0x18, 0x03, 0xE0, 0x03, 0x00, 0x02, 0x00, 0x60, 0x00,
    0x00, 0x1F, 0xC0, 0x00, 0xFF, 0xC0, 0x07, 0xC3, 0xE0, 0x1F, 0x03, 0xC0,
    0x7C, 0x03, 0xC1, 0xF0, 0x07, 0x87, 0xE0, 0x0F, 0x8F, 0x80, 0x1F, 0x3F,
    0x00, 0x3E, 0x7C, 0x00, 0x7D, 0xF8, 0x01, 0xFB, 0xE0, 0x03, 0xF7, 0xC0,
    0x07, 0xDF, 0x80, 0x1F, 0xBF, 0x00, 0x3F, 0x7C, 0x00, 0x7C, 0xF8, 0x01,
    0xF9, 0xF0, 0x03, 0xE3, 0xE0, 0x0F, 0xC7, 0xC0, 0x1F, 0x07, 0x80, 0x7C,
    0x0F, 0x81, 0xF0, 0x0F, 0x87, 0xC0, 0x0F, 0xFE, 0x00, 0x07, 0xF0, 0x00,
    0x07, 0xFF, 0xE0, 0x03, 0xFF, 0xF0, 0x07, 0xE3, 0xF0, 0x0F, 0x83, 0xE0,
    0x1F, 0x07, 0xE0, 0x3E, 0x0F, 0xC0, 0x7C, 0x1F, 0x81, 0xF0, 0x3F, 0x03,
    0xE0, 0xFE, 0x07, 0xC1, 0xF8, 0x0F, 0x87, 0xF0, 0x3E, 0x1F, 0xC0, 0x7F,
    0xFE, 0x00, 0xFF, 0xF0, 0x03, 0xE0, 0x00, 0x07, 0xC0, 0x00, 0x0F, 0x80,
    0x00, 0x1F, 0x00, 0x00, 0x7C, 0x00, 0x00, 0xF8, 0x00, 0x01, 0xF0, 0x00,
    0x03, 0xE0, 0x00, 0x0F, 0xC0, 0x00, 0x3F, 0x80, 0x00, 0xFF, 0xC0, 0x00,
    0x00, 0x1F, 0xC0, 0x00, 0xFF, 0xC0, 0x07, 0xC3, 0xE0, 0x1F, 0x03, 0xC0,
    0x7C, 0x03, 0xC1, 0xF0, 0x07, 0x87, 0xE0, 0x0F, 0x8F, 0x80, 0x1F, 0x3F,
    0x00, 0x3E, 0x7C, 0x00, 0x7D, 0xF8, 0x01, 0xFB, 0xF0, 0x03, 0xF7, 0xC0,
    0x07, 0xDF, 0x80, 0x0F, 0xBF, 0x00, 0x3F, 0x7C, 0x00, 0x7C, 0xF8, 0x01,
    0xF9, 0xF0, 0x03, 0xE3, 0xE0, 0x07, 0xC7, 0xC0, 0x1F, 0x07, 0x80, 0x7C,
    0x0F, 0x01, 0xF0, 0x0F, 0x07, 0x80, 0x07, 0xFE, 0x00, 0x03, 0x80, 0x00,
    0x0C, 0x00, 0x00, 0x3C, 0x00, 0x20, 0xFF, 0xC1, 0x87, 0xFF, 0xFE, 0x1E,
    0xFF, 0xF8, 0x00, 0x1F, 0xC0, 0x00, 0x07, 0xFF, 0xE0, 0x01, 0xFF, 0xFC,
    0x01, 0xF8, 0x7E, 0x01, 0xF8, 0x3F, 0x01, 0xF8, 0x3F, 0x01, 0xF0, 0x3F,
    0x01, 0xF0, 0x3F, 0x03, 0xF0, 0x3F, 0x03, 0xE0, 0x7E, 0x03, 0xE0, 0xFE,
    0x03, 0xE1, 0xF8, 0x07, 0xFF, 0xF0, 0x07, 0xFF, 0x80, 0x07, 0xDF, 0xC0,
    0x0F, 0xCF, 0xC0, 0x0F, 0xCF, 0xC0, 0x0F, 0x8F, 0xE0, 0x0F, 0x87, 0xE0,
    0x1F, 0x87, 0xE0, 0x1F, 0x03, 0xF0, 0x1F, 0x03, 0xF0, 0x1F, 0x03, 0xF0,
    0x3F, 0x01, 0xF8, 0x7F, 0x01, 0xF8, 0xFF, 0xE1, 0xFE, 0x00, 0xF8, 0x40,
    0xFF, 0xB0, 0x38, 0x3C, 0x1C, 0x07, 0x0F, 0x01, 0xC3, 0xC0, 0x20, 0xF0,
    0x08, 0x3E, 0x02, 0x0F, 0xC0, 0x03, 0xF8, 0x00, 0x7F, 0x00, 0x0F, 0xE0,
    0x01, 0xFC, 0x00, 0x3F, 0x80, 0x07, 0xE0, 0x00, 0xFC, 0x00, 0x1F, 0x00,
    0x03, 0xC4, 0x00, 0xF1, 0x00, 0x3C, 0x60, 0x0F, 0x38, 0x07, 0x8F, 0x83,
    0xC2, 0x3F, 0xE0, 0x83, 0xF0, 0x00, 0x3F, 0xFF, 0xF9, 0xFF, 0xFF, 0xCF,
    0x1F, 0x1E, 0x70, 0xF8, 0x77, 0x0F, 0x83, 0x30, 0x7C, 0x09, 0x03, 0xE0,
    0x40, 0x3F, 0x02, 0x01, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x7C, 0x00, 0x07,
    0xC0, 0x00, 0x3E, 0x00, 0x01, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0xF8, 0x00,
    0x07, 0xC0, 0x00, 0x3E, 0x00, 0x03, 0xF0, 0x00, 0x1F, 0x00, 0x00, 0xF8,
    0x00, 0x07, 0xC0, 0x00, 0x7E, 0x00, 0x07, 0xF0, 0x00, 0xFF, 0xF0, 0x00,
    0x7F, 0xF0, 0xFF, 0x1F, 0xC0, 0x3E, 0x1F, 0x80, 0x1C, 0x1F, 0x80, 0x18,
    0x1F, 0x00, 0x18, 0x1F, 0x00, 0x18, 0x1F, 0x00, 0x30, 0x3F, 0x00, 0x30,
    0x3E, 0x00, 0x30, 0x3E, 0x00, 0x30, 0x7E, 0x00, 0x60, 0x7C, 0x00, 0x60,
    0x7C, 0x00, 0x60, 0x7C, 0x00, 0xC0, 0x7C, 0x00, 0xC0, 0xF8, 0x00, 0xC0,
    0xF8, 0x00, 0xC0, 0xF8, 0x01, 0x80, 0xF8, 0x01, 0x80, 0xF8, 0x03, 0x80,
    0xF8, 0x03, 0x00, 0x7C, 0x06, 0x00, 0x7E, 0x1E, 0x00, 0x3F, 0xF8, 0x00,
    0x0F, 0xE0, 0x00, 0xFF, 0xE0, 0x7F, 0x3F, 0x80, 0x1C, 0x1F, 0x80, 0x18,
    0x1F, 0x80, 0x18, 0x1F, 0x80, 0x30, 0x1F, 0x80, 0x30, 0x0F, 0x80, 0x60,
    0x0F, 0x80, 0x40, 0x0F, 0x80, 0xC0, 0x0F, 0x81, 0x80, 0x0F, 0x81, 0x00,
    0x0F, 0xC3, 0x00, 0x0F, 0xC6, 0x00, 0x07, 0xC6, 0x00, 0x07, 0xCC, 0x00,
    0x07, 0xC8, 0x00, 0x07, 0xD8, 0x00, 0x07, 0xF0, 0x00, 0x07, 0xF0, 0x00,
    0x07, 0xE0, 0x00, 0x03, 0xC0, 0x00, 0x03, 0xC0, 0x00, 0x03, 0x80, 0x00,
    0x03, 0x00, 0x00, 0x03, 0x00, 0x00, 0xFF, 0xCF, 0xF8, 0x7E, 0x7F, 0x07,
    0xE0, 0x38, 0x7C, 0x07, 0x80, 0x60, 0xF8, 0x0F, 0x00, 0x81, 0xF0, 0x1E,
    0x03, 0x03, 0xE0, 0x3E, 0x04, 0x07, 0xE0, 0xFC, 0x18, 0x07, 0xC1, 0xF8,
    0x20, 0x0F, 0x87, 0xF0, 0xC0, 0x1F, 0x0B, 0xE1, 0x00, 0x3E, 0x37, 0xC6,
    0x00, 0x7C, 0x47, 0x88, 0x00, 0xF9, 0x8F, 0x30, 0x01, 0xF2, 0x1F, 0x40,
    0x03, 0xEC, 0x3E, 0x80, 0x03, 0xF0, 0x7F, 0x00, 0x07, 0xE0, 0xFC, 0x00,
    0x0F, 0x81, 0xF8, 0x00, 0x1F, 0x03, 0xE0, 0x00, 0x3C, 0x07, 0xC0, 0x00,
    0x78, 0x07, 0x00, 0x00, 0xF0, 0x0E, 0x00, 0x00, 0xC0, 0x18, 0x00, 0x01,
    0x80, 0x30, 0x00, 0x02, 0x00, 0x40, 0x00, 0x0F, 0xFE, 0x3F, 0x81, 0xFC,
    0x07, 0x80, 0x7C, 0x03, 0x00, 0x3F, 0x03, 0x00, 0x0F, 0x83, 0x80, 0x07,
    0xC1, 0x80, 0x03, 0xE1, 0x80, 0x00, 0xF9, 0x80, 0x00, 0x7D, 0x80, 0x00,
    0x3F, 0x80, 0x00, 0x0F, 0x80, 0x00, 0x07, 0xC0, 0x00, 0x03, 0xE0, 0x00,
    0x01, 0xF8, 0x00, 0x01, 0xFC, 0x00, 0x00, 0xBE, 0x00, 0x00, 0xCF, 0x00,
    0x00, 0xC7, 0xC0, 0x00, 0xC3, 0xE0, 0x00, 0xC1, 0xF0, 0x00, 0xC0, 0x7C,
    0x00, 0xE0, 0x3E, 0x00, 0xE0, 0x1F, 0x00, 0xF8, 0x1F, 0xE0, 0xFF, 0x1F,
    0xF8, 0x00, 0xFF, 0xC3, 0xF9, 0xF8, 0x07, 0x87, 0xC0, 0x38, 0x3E, 0x01,
    0x81, 0xF0, 0x18, 0x07, 0xC0, 0x80, 0x3E, 0x0C, 0x01, 0xF0, 0xC0, 0x07,
    0xC4, 0x00, 0x3E, 0x60, 0x01, 0xF6, 0x00, 0x07, 0xA0, 0x00, 0x3F, 0x00,
    0x01, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0xFC, 0x00, 0x07, 0xC0, 0x00, 0x3E,
    0x00, 0x01, 0xF0, 0x00, 0x1F, 0x00, 0x00, 0xF8, 0x00, 0x07, 0xC0, 0x00,
    0x7E, 0x00, 0x07, 0xF0, 0x00, 0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xF8, 0x3F,
    0xFF, 0xC3, 0xE0, 0x7E, 0x1C, 0x07, 0xE0, 0xC0, 0x3E, 0x0C, 0x03, 0xF0,
    0x40, 0x3F, 0x00, 0x03, 0xF0, 0x00, 0x1F, 0x80, 0x01, 0xF8, 0x00, 0x1F,
    0x80, 0x00, 0xF8, 0x00, 0x0F, 0xC0, 0x00, 0xFC, 0x00, 0x0F, 0xC0, 0x00,
    0x7E, 0x00, 0x07, 0xE0, 0x00, 0x7E, 0x00, 0x83, 0xE0, 0x0C, 0x3F, 0x00,
    0xC3, 0xF0, 0x0E, 0x1F, 0x00, 0xF1, 0xF8, 0x1F, 0x9F, 0xFF, 0xF8, 0xFF,
    0xFF, 0xC0, 0x01, 0xFC, 0x0F, 0xE0, 0x3C, 0x00, 0xE0, 0x03, 0x80, 0x1E,
    0x00, 0x78, 0x01, 0xC0, 0x07, 0x00, 0x3C, 0x00, 0xF0, 0x03, 0x80, 0x0E,
    0x00, 0x38, 0x01, 0xE0, 0x07, 0x00, 0x1C, 0x00, 0x70, 0x03, 0xC0, 0x0F,
    0x00, 0x38, 0x00, 0xE0, 0x07, 0x80, 0x1E, 0x00, 0x70, 0x01, 0xC0, 0x0F,
    0x00, 0x3C, 0x00, 0xFF, 0x03, 0xF8, 0x00, 0xE0, 0x38, 0x07, 0x01, 0xC0,
    0x70, 0x0C, 0x03, 0x80, 0xE0, 0x38, 0x07, 0x01, 0xC0, 0x70, 0x0C, 0x03,
    0x80, 0xE0, 0x38, 0x07, 0x01, 0xC0, 0x70, 0x0C, 0x03, 0x80, 0xE0, 0x38,
    0x07, 0x01, 0xC0, 0x03, 0xFC, 0x0F, 0xF0, 0x03, 0x80, 0x0E, 0x00, 0x38,
    0x01, 0xE0, 0x07, 0x80, 0x1C, 0x00, 0x70, 0x03, 0xC0, 0x0F, 0x00, 0x38,
    0x00, 0xE0, 0x07, 0x80, 0x1E, 0x00, 0x70, 0x01, 0xC0, 0x0F, 0x00, 0x3C,
    0x00, 0xE0, 0x03, 0x80, 0x0E, 0x00, 0x78, 0x01, 0xE0, 0x07, 0x00, 0x1C,
    0x00, 0xF0, 0x03, 0xC0, 0xFE, 0x03, 0xF8, 0x00, 0x03, 0xC0, 0x03, 0xC0,
    0x07, 0xE0, 0x07, 0xE0, 0x0E, 0x70, 0x0E, 0x70, 0x1C, 0x78, 0x1C, 0x38,
    0x3C, 0x3C, 0x38, 0x1C, 0x78, 0x1E, 0x70, 0x0E, 0xF0, 0x0E, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xE1, 0xE3, 0xC1, 0xC1, 0xC0, 0xC0, 0x00,
    0xF7, 0x80, 0xFD, 0xE0, 0x7C, 0xF0, 0x3C, 0x3C, 0x1E, 0x0F, 0x0F, 0x83,
    0x83, 0xC1, 0xE1, 0xE0, 0x78, 0x78, 0x1C, 0x3E, 0x0F, 0x0F, 0x03, 0xC3,
    0xC1, 0xF0, 0xF0, 0xFC, 0xFE, 0x6F, 0x6F, 0xF3, 0xF1, 0xF8, 0xF8, 0x3C,
    0x1C, 0x00, 0x01, 0xE0, 0x1F, 0xC0, 0x07, 0xC0, 0x07, 0xC0, 0x07, 0x80,
    0x07, 0x80, 0x0F, 0x80, 0x0F, 0x00, 0x0F, 0x00, 0x0F, 0x3C, 0x1E, 0xFE,
    0x1F, 0x9F, 0x1F, 0x0F, 0x1E, 0x0F, 0x3E, 0x0F, 0x3C, 0x0F, 0x3C, 0x1F,
    0x78, 0x1E, 0x78, 0x1E, 0x78, 0x3C, 0x78, 0x3C, 0xF0, 0x78, 0xF0, 0xF0,
    0xF1, 0xE0, 0x7F, 0xC0, 0x3F, 0x00, 0x01, 0xF0, 0x3F, 0xC3, 0xCE, 0x3C,
    0xF3, 0xC7, 0x1E, 0x01, 0xE0, 0x0F, 0x00, 0xF8, 0x07, 0x80, 0x3C, 0x01,
    0xE0, 0x0F, 0x03, 0x78, 0x31, 0xE3, 0x0F, 0xF0, 0x1E, 0x00, 0x00, 0x1F,
    0xC0, 0x00, 0xF8, 0x00, 0x1F, 0x00, 0x03, 0xE0, 0x00, 0x78, 0x00, 0x0F,
    0x00, 0x03, 0xE0, 0x00, 0x7C, 0x01, 0xEF, 0x00, 0x7F, 0xE0, 0x3E, 0x7C,
    0x07, 0x8F, 0x01, 0xE1, 0xE0, 0x78, 0x3C, 0x0F, 0x0F, 0x83, 0xC1, 0xE0,
    0x78, 0x3C, 0x1E, 0x0F, 0x83, 0xC1, 0xF0, 0x78, 0x7C, 0x0F, 0x0F, 0x91,
    0xE3, 0xF6, 0x3F, 0xDF, 0x83, 0xF3, 0xE0, 0x3C, 0x38, 0x00, 0x01, 0xE0,
    0x3F, 0x83, 0xCE, 0x3C, 0x73, 0xC3, 0x9E, 0x1D, 0xE1, 0xCF, 0x1C, 0xFB,
    0xC7, 0xF8, 0x3C, 0x01, 0xE0, 0x0F, 0x02, 0x78, 0x31, 0xE3, 0x0F, 0xF0,
    0x1E, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x1D, 0xC0, 0x01, 0xCE, 0x00, 0x1C,
    0x70, 0x01, 0xE0, 0x00, 0x0F, 0x00, 0x00, 0x78, 0x00, 0x07, 0x80, 0x00,
    0x3C, 0x00, 0x0F, 0xFC, 0x00, 0x7F, 0xE0, 0x00, 0xF0, 0x00, 0x07, 0x80,
    0x00, 0x3C, 0x00, 0x03, 0xE0, 0x00, 0x1E, 0x00, 0x00, 0xF0, 0x00, 0x07,
    0x80, 0x00, 0x7C, 0x00, 0x03, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0xF0, 0x00,
    0x07, 0x80, 0x00, 0x78, 0x00, 0x03, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0xE0,
    0x00, 0x0F, 0x00, 0x0E, 0x70, 0x00, 0x77, 0x80, 0x03, 0xF8, 0x00, 0x0F,
    0x80, 0x00, 0x00, 0xFE, 0x00, 0x7F, 0xFC, 0x1F, 0x1F, 0x87, 0xC3, 0xC1,
    0xF0, 0x78, 0x3C, 0x1F, 0x07, 0x83, 0xE0, 0xF0, 0xF8, 0x0E, 0x3E, 0x01,
    0xFF, 0x80, 0x3F, 0xC0, 0x0C, 0x00, 0x03, 0xC0, 0x00, 0x7F, 0x80, 0x0F,
    0xFE, 0x00, 0x7F, 0xF0, 0x70, 0xFF, 0x1C, 0x03, 0xE3, 0x80, 0x3C, 0x70,
    0x07, 0x0F, 0x03, 0xE0, 0xFF, 0xF0, 0x07, 0xF0, 0x00, 0x1F, 0xC0, 0x03,
    0xE0, 0x00, 0xF0, 0x00, 0xF8, 0x00, 0x78, 0x00, 0x3C, 0x00, 0x1E, 0x00,
    0x1F, 0x00, 0x0F, 0x0E, 0x07, 0x9F, 0x83, 0xDF, 0xC3, 0xE9, 0xE1, 0xE8,
    0xF0, 0xF8, 0xF8, 0x7C, 0x78, 0x7C, 0x3C, 0x3E, 0x3E, 0x1E, 0x1E, 0x1F,
    0x0F, 0x0F, 0x0F, 0x87, 0x87, 0xCB, 0xC3, 0xCB, 0xE1, 0xE9, 0xE0, 0xFC,
    0xF0, 0x38, 0x00, 0x03, 0x03, 0xC1, 0xE0, 0xF0, 0x30, 0x00, 0x00, 0x00,
    0x07, 0x3F, 0x87, 0x83, 0xC1, 0xE0, 0xF0, 0xF0, 0x78, 0x3C, 0x1E, 0x1E,
    0x0F, 0x27, 0x17, 0x93, 0xF1, 0xF8, 0x70, 0x00, 0x00, 0x06, 0x00, 0x0F,
    0x00, 0x0F, 0x00, 0x0F, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x06, 0x00, 0xFE, 0x00, 0x3E, 0x00, 0x3C, 0x00, 0x3C, 0x00, 0x3C,
    0x00, 0x7C, 0x00, 0x78, 0x00, 0x78, 0x00, 0x78, 0x00, 0xF8, 0x00, 0xF0,
    0x00, 0xF0, 0x00, 0xF0, 0x01, 0xF0, 0x01, 0xE0, 0x01, 0xE0, 0x01, 0xE0,
    0x03, 0xC0, 0xE3, 0xC0, 0xE7, 0x80, 0xFF, 0x00, 0x7C, 0x00, 0x1F, 0xC0,
    0x03, 0xE0, 0x00, 0xF0, 0x00, 0x78, 0x00, 0x78, 0x00, 0x3C, 0x00, 0x1E,
    0x00, 0x1F, 0x00, 0x0F, 0x3F, 0x87, 0x87, 0x83, 0xC3, 0x03, 0xE3, 0x01,
    0xE3, 0x00, 0xF3, 0x00, 0x7B, 0x80, 0x7B, 0xC0, 0x3F, 0xE0, 0x1E, 0xF0,
    0x1F, 0x78, 0x0F, 0x1E, 0x07, 0x8F, 0x13, 0xC7, 0x93, 0xE1, 0xF9, 0xE0,
    0xF8, 0xF0, 0x38, 0x00, 0x1F, 0xC0, 0xF8, 0x1F, 0x03, 0xC0, 0x78, 0x1F,
    0x03, 0xE0, 0x78, 0x0F, 0x01, 0xE0, 0x78, 0x0F, 0x01, 0xE0, 0x3C, 0x0F,
    0x01, 0xE0, 0x3C, 0x0F, 0x81, 0xE0, 0x3C, 0x8F, 0x31, 0xEC, 0x3F, 0x07,
    0xC0, 0x70, 0x00, 0x01, 0x87, 0x07, 0x0F, 0xE7, 0xE7, 0xE0, 0xF3, 0xF9,
    0xF8, 0x3D, 0x9E, 0x9E, 0x0F, 0x47, 0xC7, 0x83, 0xE1, 0xD1, 0xE1, 0xF8,
    0xF8, 0xF0, 0x7C, 0x3C, 0x3C, 0x1F, 0x0F, 0x1F, 0x0F, 0x87, 0xC7, 0x83,
    0xE1, 0xE1, 0xE0, 0xF0, 0x78, 0x78, 0x3C, 0x1E, 0x3C, 0x1F, 0x0F, 0x0F,
    0x27, 0x83, 0xC3, 0xD1, 0xE0, 0xF0, 0xFC, 0xF8, 0x78, 0x1C, 0x00, 0x01,
    0x8F, 0x0F, 0xE7, 0xE0, 0xF3, 0xF8, 0x3C, 0x9E, 0x0F, 0x47, 0x87, 0xA3,
    0xC1, 0xE8, 0xF0, 0x7C, 0x3C, 0x1E, 0x1E, 0x0F, 0x87, 0x83, 0xE1, 0xE0,
    0xF0, 0xF8, 0x3C, 0x3C, 0x1F, 0x0F, 0x27, 0x83, 0xD1, 0xE0, 0xFC, 0x78,
    0x1C, 0x00, 0x01, 0xF0, 0x0E, 0x30, 0x38, 0x70, 0xF0, 0xF3, 0xC1, 0xE7,
    0x83, 0xDE, 0x07, 0xBC, 0x1F, 0xF8, 0x3F, 0xE0, 0x7B, 0xC0, 0xF7, 0x83,
    0xCF, 0x07, 0x9E, 0x1E, 0x1C, 0x38, 0x1C, 0xE0, 0x1F, 0x00, 0x00, 0xE3,
    0x80, 0xFD, 0xF8, 0x0F, 0xFF, 0x81, 0xE8, 0xF0, 0x3E, 0x1E, 0x07, 0x83,
    0xC0, 0xF0, 0x78, 0x3E, 0x1F, 0x07, 0x83, 0xC0, 0xF0, 0x78, 0x1E, 0x1F,
    0x07, 0x83, 0xC0, 0xF0, 0xF8, 0x1E, 0x1E, 0x03, 0xC7, 0x80, 0xFF, 0xE0,
    0x1E, 0xF0, 0x03, 0xC0, 0x00, 0xF0, 0x00, 0x1E, 0x00, 0x03, 0xC0, 0x00,
    0xF8, 0x00, 0x3F, 0xC0, 0x00, 0x01, 0xEF, 0x07, 0xFF, 0x0F, 0x1E, 0x1E,
    0x1E, 0x1E, 0x1E, 0x3C, 0x1E, 0x7C, 0x3C, 0x78, 0x3C, 0x78, 0x3C, 0xF0,
    0x7C, 0xF0, 0x78, 0xF0, 0xF8, 0xF0, 0xF8, 0xF1, 0xF0, 0xFE, 0xF0, 0x7E,
    0xF0, 0x39, 0xE0, 0x01, 0xE0, 0x01, 0xE0, 0x01, 0xE0, 0x03, 0xC0, 0x03,
    0xC0, 0x1F, 0xF8, 0x03, 0x9C, 0x7F, 0x7C, 0x3D, 0xF8, 0x7A, 0xE0, 0xF8,
    0x03, 0xE0, 0x07, 0xC0, 0x0F, 0x00, 0x3E, 0x00, 0x7C, 0x00, 0xF0, 0x01,
    0xE0, 0x07, 0xC0, 0x0F, 0x00, 0x1E, 0x00, 0x7C, 0x00, 0x07, 0x18, 0xFF,
    0xC7, 0x1C, 0x70, 0x63, 0x81, 0x1E, 0x08, 0xF8, 0x07, 0xE0, 0x1F, 0x00,
    0x7C, 0x01, 0xF0, 0x07, 0x84, 0x3C, 0x20, 0xE1, 0x87, 0x1C, 0x70, 0x9E,
    0x00, 0x00, 0x80, 0x60, 0x30, 0x1C, 0x1F, 0x1F, 0xF7, 0xFC, 0x78, 0x1E,
    0x07, 0x83, 0xC0, 0xF0, 0x3C, 0x1F, 0x07, 0x81, 0xE0, 0x79, 0x3C, 0x4F,
    0x23, 0xF0, 0xFC, 0x1C, 0x00, 0x0F, 0x0F, 0x3F, 0x87, 0x8F, 0x83, 0xC7,
    0xC1, 0xE3, 0xE1, 0xE1, 0xE0, 0xF0, 0xF0, 0x78, 0xF8, 0x78, 0x78, 0x3C,
    0x3C, 0x3E, 0x1E, 0x1F, 0x1E, 0x1F, 0x0F, 0x17, 0x97, 0x9B, 0xCB, 0xF9,
    0xF9, 0xF8, 0xF8, 0x78, 0x38, 0x00, 0x18, 0x37, 0xC3, 0xDE, 0x1E, 0x78,
    0x73, 0xC1, 0x9E, 0x08, 0xF0, 0xC7, 0x84, 0x3C, 0x41, 0xE4, 0x0F, 0x40,
    0x7C, 0x03, 0xC0, 0x1C, 0x00, 0xC0, 0x04, 0x00, 0x38, 0x10, 0xDF, 0x06,
    0x3D, 0xE0, 0xC7, 0xBC, 0x38, 0x73, 0xC7, 0x06, 0x79, 0xF0, 0x8F, 0x3E,
    0x11, 0xEB, 0xC4, 0x3F, 0x79, 0x07, 0xCF, 0x60, 0xF9, 0xE8, 0x1E, 0x3E,
    0x03, 0x87, 0x80, 0x70, 0xF0, 0x0C, 0x0C, 0x01, 0x01, 0x00, 0x03, 0x83,
    0x87, 0xF1, 0xF0, 0x3C, 0xF8, 0x0F, 0x60, 0x03, 0xD0, 0x00, 0xF8, 0x00,
    0x1E, 0x00, 0x07, 0x80, 0x01, 0xE0, 0x00, 0x78, 0x00, 0x1F, 0x00, 0x0F,
    0xC0, 0x02, 0xF1, 0x39, 0x3C, 0xCF, 0xCF, 0xE3, 0xE1, 0xF0, 0x70, 0x38,
    0x00, 0x01, 0x83, 0x07, 0xE3, 0xC1, 0xF1, 0xE0, 0x78, 0xF0, 0x3E, 0x18,
    0x1F, 0x08, 0x07, 0x84, 0x03, 0xC6, 0x01, 0xE2, 0x00, 0xFB, 0x00, 0x3D,
    0x00, 0x1F, 0x80, 0x0F, 0x80, 0x07, 0xC0, 0x03, 0xC0, 0x01, 0xE0, 0x00,
    0xE0, 0x00, 0x60, 0x00, 0x60, 0x0E, 0x60, 0x0F, 0xE0, 0x07, 0xE0, 0x01,
    0xC0, 0x00, 0x1F, 0xFC, 0x3F, 0xF8, 0x7F, 0xE1, 0x81, 0x82, 0x06, 0x00,
    0x08, 0x00, 0x20, 0x00, 0xC0, 0x03, 0x00, 0x0C, 0x00, 0x10, 0x00, 0x40,
    0x01, 0x80, 0x07, 0xC0, 0x1F, 0x86, 0x3F, 0x8E, 0xCF, 0x9C, 0x07, 0x30,
    0x03, 0xC0, 0x00, 0x1E, 0x00, 0xF8, 0x03, 0xC0, 0x0F, 0x00, 0x1E, 0x00,
    0x38, 0x00, 0xF0, 0x01, 0xE0, 0x03, 0xC0, 0x07, 0x00, 0x1E, 0x00, 0x3C,
    0x00, 0x78, 0x01, 0xE0, 0x03, 0xC0, 0x1F, 0x00, 0x7E, 0x00, 0x30, 0x00,
    0x60, 0x00, 0xE0, 0x01, 0xC0, 0x07, 0x80, 0x0F, 0x00, 0x1E, 0x00, 0x38,
    0x00, 0xF0, 0x01, 0xE0, 0x03, 0xC0, 0x07, 0x00, 0x0E, 0x00, 0x0C, 0x00,
    0x0F, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE0,
    0x00, 0xF0, 0x00, 0x70, 0x00, 0x70, 0x00, 0xE0, 0x01, 0xC0, 0x03, 0x80,
    0x07, 0x00, 0x1E, 0x00, 0x3C, 0x00, 0x78, 0x00, 0xE0, 0x03, 0xC0, 0x07,
    0x80, 0x0F, 0x00, 0x1C, 0x00, 0x18, 0x00, 0x10, 0x00, 0xF0, 0x03, 0xF0,
    0x0F, 0x00, 0x1E, 0x00, 0x38, 0x00, 0xF0, 0x01, 0xE0, 0x03, 0xC0, 0x07,
    0x00, 0x1E, 0x00, 0x3C, 0x00, 0x70, 0x01, 0xE0, 0x0F, 0x80, 0x7C, 0x00,
    0x3E, 0x00, 0x7F, 0xC6, 0xFF, 0xFF, 0x61, 0xFE, 0x00, 0x7C};

const GFXglyph FreeSerifBoldItalic18pt7bGlyphs[] PROGMEM = {
    {0, 0, 0, 9, 0, 1},          // 0x20 ' '
    {0, 11, 25, 14, 2, -23},     // 0x21 '!'
    {35, 14, 10, 19, 4, -23},    // 0x22 '"'
    {53, 20, 25, 17, -1, -24},   // 0x23 '#'
    {116, 17, 29, 18, 0, -25},   // 0x24 '$'
    {178, 27, 25, 29, 1, -23},   // 0x25 '%'
    {263, 25, 25, 27, 0, -23},   // 0x26 '&'
    {342, 5, 10, 10, 4, -23},    // 0x27 '''
    {349, 11, 30, 12, 1, -23},   // 0x28 '('
    {391, 11, 30, 12, -2, -23},  // 0x29 ')'
    {433, 13, 15, 18, 2, -23},   // 0x2A '*'
    {458, 17, 17, 20, 1, -16},   // 0x2B '+'
    {495, 7, 11, 9, -2, -4},     // 0x2C ','
    {505, 9, 4, 12, 0, -9},      // 0x2D '-'
    {510, 6, 5, 9, 0, -3},       // 0x2E '.'
    {514, 14, 25, 12, 0, -23},   // 0x2F '/'
    {558, 15, 25, 18, 1, -23},   // 0x30 '0'
    {605, 15, 25, 17, 0, -23},   // 0x31 '1'
    {652, 16, 25, 18, 0, -23},   // 0x32 '2'
    {702, 15, 25, 17, 1, -23},   // 0x33 '3'
    {749, 18, 24, 17, 0, -23},   // 0x34 '4'
    {803, 17, 25, 18, 0, -23},   // 0x35 '5'
    {857, 17, 25, 18, 1, -23},   // 0x36 '6'
    {911, 16, 24, 17, 3, -23},   // 0x37 '7'
    {959, 17, 25, 18, 0, -23},   // 0x38 '8'
    {1013, 17, 25, 18, 0, -23},  // 0x39 '9'
    {1067, 10, 17, 9, 0, -15},   // 0x3A ':'
    {1089, 11, 22, 9, -1, -15},  // 0x3B ';'
    {1120, 18, 19, 20, 1, -18},  // 0x3C '<'
    {1163, 18, 10, 20, 2, -13},  // 0x3D '='
    {1186, 18, 19, 20, 2, -18},  // 0x3E '>'
    {1229, 13, 25, 17, 3, -23},  // 0x3F '?'
    {1270, 25, 25, 29, 2, -23},  // 0x40 '@'
    {1349, 23, 25, 24, 0, -23},  // 0x41 'A'
    {1421, 24, 25, 22, 0, -23},  // 0x42 'B'
    {1496, 23, 25, 22, 1, -23},  // 0x43 'C'
    {1568, 26, 25, 25, 0, -23},  // 0x44 'D'
    {1650, 23, 25, 22, 0, -23},  // 0x45 'E'
    {1722, 23, 25, 21, 0, -23},  // 0x46 'F'
    {1794, 24, 25, 25, 2, -23},  // 0x47 'G'
    {1869, 29, 25, 26, 0, -23},  // 0x48 'H'
    {1960, 15, 25, 13, 0, -23},  // 0x49 'I'
    {2007, 20, 27, 17, 0, -23},  // 0x4A 'J'
    {2075, 25, 25, 23, 0, -23},  // 0x4B 'K'
    {2154, 22, 25, 21, 0, -23},  // 0x4C 'L'
    {2223, 33, 25, 31, 0, -23},  // 0x4D 'M'
    {2327, 27, 25, 25, 0, -23},  // 0x4E 'N'
    {2412, 23, 25, 24, 1, -23},  // 0x4F 'O'
    {2484, 23, 25, 21, 0, -23},  // 0x50 'P'
    {2556, 23, 31, 24, 1, -23},  // 0x51 'Q'
    {2646, 24, 25, 23, 0, -23},  // 0x52 'R'
    {2721, 18, 25, 18, 0, -23},  // 0x53 'S'
    {2778, 21, 25, 21, 3, -23},  // 0x54 'T'
    {2844, 24, 25, 25, 4, -23},  // 0x55 'U'
    {2919, 24, 25, 25, 4, -23},  // 0x56 'V'
    {2994, 31, 25, 32, 4, -23},  // 0x57 'W'
    {3091, 25, 25, 24, 0, -23},  // 0x58 'X'
    {3170, 21, 25, 22, 4, -23},  // 0x59 'Y'
    {3236, 21, 25, 20, 0, -23},  // 0x5A 'Z'
    {3302, 14, 30, 12, -1, -23}, // 0x5B '['
    {3355, 10, 25, 14, 4, -23},  // 0x5C '\'
    {3387, 14, 30, 12, -2, -23}, // 0x5D ']'
    {3440, 16, 13, 20, 2, -23},  // 0x5E '^'
    {3466, 18, 3, 17, 0, 3},     // 0x5F '_'
    {3473, 7, 6, 12, 3, -23},    // 0x60 '`'
    {3479, 18, 17, 18, 0, -15},  // 0x61 'a'
    {3518, 16, 26, 17, 1, -24},  // 0x62 'b'
    {3570, 13, 17, 15, 1, -15},  // 0x63 'c'
    {3598, 19, 25, 18, 1, -23},  // 0x64 'd'
    {3658, 13, 17, 15, 1, -15},  // 0x65 'e'
    {3686, 21, 32, 17, -3, -24}, // 0x66 'f'
    {3770, 19, 23, 17, -1, -15}, // 0x67 'g'
    {3825, 17, 25, 19, 1, -23},  // 0x68 'h'
    {3879, 9, 25, 10, 1, -23},   // 0x69 'i'
    {3908, 16, 31, 12, -3, -23}, // 0x6A 'j'
    {3970, 17, 25, 18, 1, -23},  // 0x6B 'k'
    {4024, 11, 25, 10, 1, -23},  // 0x6C 'l'
    {4059, 26, 17, 27, 0, -15},  // 0x6D 'm'
    {4115, 18, 17, 18, 0, -15},  // 0x6E 'n'
    {4154, 15, 17, 17, 1, -15},  // 0x6F 'o'
    {4186, 19, 23, 17, -2, -15}, // 0x70 'p'
    {4241, 16, 23, 17, 1, -15},  // 0x71 'q'
    {4287, 15, 16, 14, 0, -15},  // 0x72 'r'
    {4317, 13, 17, 12, 0, -15},  // 0x73 's'
    {4345, 10, 22, 10, 1, -20},  // 0x74 't'
    {4373, 17, 17, 19, 1, -15},  // 0x75 'u'
    {4410, 13, 16, 15, 2, -15},  // 0x76 'v'
    {4436, 19, 16, 23, 3, -15},  // 0x77 'w'
    {4474, 18, 17, 17, -1, -15}, // 0x78 'x'
    {4513, 17, 23, 15, -2, -15}, // 0x79 'y'
    {4562, 15, 19, 14, 0, -15},  // 0x7A 'z'
    {4598, 15, 32, 12, 0, -24},  // 0x7B '{'
    {4658, 3, 25, 9, 4, -23},    // 0x7C '|'
    {4668, 15, 32, 12, -5, -24}, // 0x7D '}'
    {4728, 16, 5, 20, 2, -11}};  // 0x7E '~'

const GFXfont FreeSerifBoldItalic18pt7b PROGMEM = {
    (uint8_t *)FreeSerifBoldItalic18pt7bBitmaps,
    (GFXglyph *)FreeSerifBoldItalic18pt7bGlyphs, 0x20, 0x7E, 42};

// Approx. 5410 bytes

#include "ble_manager.h"
#include <Arduino.h>
bool motorState = false;
// 回调类实现
void BLEServerCallbacksImpl::onConnect(BLEServer* pServer) {
    manager.setConnected(true);
    Serial.printf("BLE Device Connected");
}

void BLEServerCallbacksImpl::onDisconnect(BLEServer* pServer) {
    manager.setConnected(false);
    Serial.println("BLE Device Disconnected");
    pServer->startAdvertising();
}

void BLECharacteristicCallbacksImpl::onWrite(BLECharacteristic* pCharacteristic) {
    std::string value = pCharacteristic->getValue();
    if (value.length() > 0) {
        Serial.print("Received Value: ");
        for (int i = 0; i < value.length(); i++) {
            Serial.print(value[i]);
        }
        Serial.println();
        
        if (value == "LED_ON") {
            Serial.println("Turning LED ON");
            motorState = !motorState;
        }
    }
}

BLEManager& BLEManager::getInstance() {
    static BLEManager instance;
    return instance;
}

BLEManager::BLEManager() 
    : pServer(nullptr), pService(nullptr), pCharacteristic(nullptr), 
      deviceConnected(false),
      serverCallbacks(*this),
      charCallbacks(*this) {
}

void BLEManager::setConnected(bool connected) {
    deviceConnected = connected;
}

void BLEManager::init() {
    BLEDevice::init(BLE_DEVICE_NAME);
    setupBLEServer();
    setupBLEService();
    setupBLECharacteristic();
}

void BLEManager::start() {
    pService->start();
    
    BLEAdvertising* pAdvertising = BLEDevice::getAdvertising();
    pAdvertising->addServiceUUID(SERVICE_UUID);
    pAdvertising->setScanResponse(true);
    pAdvertising->start();
}

bool BLEManager::isConnected() const {
    return deviceConnected;
}

void BLEManager::sendNotification(const std::string& value) {
    if (deviceConnected && pCharacteristic) {
        pCharacteristic->setValue(value);
        pCharacteristic->notify();
    }
}

void BLEManager::setupBLEServer() {
    pServer = BLEDevice::createServer();
    pServer->setCallbacks(&serverCallbacks);
}

void BLEManager::setupBLEService() {
    pService = pServer->createService(SERVICE_UUID);
}

void BLEManager::setupBLECharacteristic() {
    pCharacteristic = pService->createCharacteristic(
        CHARACTERISTIC_UUID,
        NIMBLE_PROPERTY::READ |
        NIMBLE_PROPERTY::WRITE |
        NIMBLE_PROPERTY::NOTIFY
    );
    pCharacteristic->setCallbacks(&charCallbacks);
    pCharacteristic->setValue("Hello World");
}

void bleTask(void* parameter) {
    BLEManager& bleManager = BLEManager::getInstance();
    bleManager.init();
    bleManager.start();

    while (true) {
        // BLE 任务主循环
        delay(1000);
    }
}
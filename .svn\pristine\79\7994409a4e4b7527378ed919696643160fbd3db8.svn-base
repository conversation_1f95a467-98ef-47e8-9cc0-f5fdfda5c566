#include <SPI.h>
#include "oled.h"
#include "driver/spi_master.h"

int oled_counter = 0;
int mode_show = 0;
uint16_t tle_angle = 2;
float angle_date = 0;
spi_device_handle_t oled_SPI = NULL;
extern float qma6100_x;


void OLED_Init_SPI(void)
{
  esp_err_t ret;

  // Initialize SPI config safely
  spi_device_interface_config_t devcfg;
  memset(&devcfg, 0, sizeof(devcfg));
  
  // Only set essential parameters:
  devcfg.mode = 3;                // TLE5012B requires SPI mode 1 (CPOL=0, CPHA=1)
  // Limit to max 8MHz per TLE5012B specs
  devcfg.clock_speed_hz = std::min(static_cast<int>(1000000), 8000000);
  devcfg.spics_io_num = -1;       // Manual CS control
  devcfg.queue_size = 7;          // Transaction queue size

  // 将TLE5012B外设与SPI总线关联
  ret=spi_bus_add_device(SPI3_HOST, &devcfg, &oled_SPI);
  ESP_ERROR_CHECK(ret);

}





void OLED_Init_GPIO(void)
{
    
	pinMode(OLED_RST_PIN, OUTPUT);
	pinMode(OLED_DC_PIN, OUTPUT);
	pinMode(OLED_CS_PIN, OUTPUT);

    OLED_CS_PIN_Set();
    OLED_RST_PIN_Set();
    OLED_DC_PIN_Set();

    OLED_Init_SPI();
}

void OLED_Init() {
  
  // 硬件复位
  OLED_RST_PIN_Clr();
  vTaskDelay(200 / portTICK_PERIOD_MS);
  OLED_RST_PIN_Set();
  vTaskDelay(200 / portTICK_PERIOD_MS);


  // 显示启动画面
    OLED_Write_Byte(0xAE,OLED_CMD);	/*display off*/
	OLED_Write_Byte(0x00,OLED_CMD);	/*set lower column address*/
	OLED_Write_Byte(0x10,OLED_CMD);	/*set higher column address*/
	OLED_Write_Byte(0x40,OLED_CMD);	/*set display start line*/
	OLED_Write_Byte(0xB0,OLED_CMD);	/*set page address*/
	OLED_Write_Byte(0x81,OLED_CMD);	/*contract control*/
	OLED_Write_Byte(0xFF,OLED_CMD);	/*128*/
	OLED_Write_Byte(0xA1,OLED_CMD);	/*set segment remap*/
	OLED_Write_Byte(0xA6,OLED_CMD);	/*normal / reverse*/
	OLED_Write_Byte(0xA8,OLED_CMD);	/*multiplex ratio*/
	OLED_Write_Byte(0x3F,OLED_CMD);	/*duty = 1/64*/
	OLED_Write_Byte(0xC8,OLED_CMD);	/*Com scan direction*/
	OLED_Write_Byte(0xD3,OLED_CMD);	/*set display offset*/
	OLED_Write_Byte(0x00,OLED_CMD);
	OLED_Write_Byte(0xD5,OLED_CMD);	/*set osc division*/
	OLED_Write_Byte(0x80,OLED_CMD);
	OLED_Write_Byte(0xD9,OLED_CMD);	/*set pre-charge period*/
	OLED_Write_Byte(0XF1,OLED_CMD);
	OLED_Write_Byte(0xDA,OLED_CMD);	/*set COM pins*/
	OLED_Write_Byte(0x12,OLED_CMD);
	OLED_Write_Byte(0xDB,OLED_CMD);	/*set vcomh*/
	OLED_Write_Byte(0x30,OLED_CMD);
	OLED_Write_Byte(0x8D,OLED_CMD);	/*set charge pump disable*/
	OLED_Write_Byte(0x14,OLED_CMD);
	OLED_Write_Byte(0xAF,OLED_CMD);	/*display ON*/

    // 清屏
  OLED_Clear(0x00);
  

//   if (qma6100_x > 0)
//     OLED_Set_Flip(1); // 倒过来显示
// else
//     OLED_Set_Flip(0); // 正常显示
  OLED_DrawBMP(0, 0, 128, 64, epd_bitmap_logo, 1);
  OLED_Display();
  // display.display();
  vTaskDelay(2000 / portTICK_PERIOD_MS);
  OLED_Clear(0x00);
  // display.setTextColor(SSD1306_WHITE);
}

void OLED_Set_Flip(int flip) {
    if (flip) {
        // 正常显示
        OLED_Write_Byte(0xA0, 0);  // Segment remap
        OLED_Write_Byte(0xC0, 0);  // COM scan normal

    } else {
        // 倒置显示
        OLED_Write_Byte(0xA1, 0);  // Segment remap
        OLED_Write_Byte(0xC8, 0);  // COM scan reverse
    }
}

/*******************************************************************
 * @name       :void OLED_Clear(unsigned dat)
 * @date       :2022-06-22
 * @function   :清除OLED屏幕显示
 * @parameters :dat:0-显示全黑
                    1-显示全白
 * @retvalue   :无
********************************************************************/
void OLED_Clear(uint8_t dat)
{
	if(dat)
	{
		memset(OLED_buffer,0xff,sizeof(OLED_buffer));
	}
	else
	{
		memset(OLED_buffer,0,sizeof(OLED_buffer));
	}
	OLED_Display();
}

/*******************************************************************
* @name       :void OLED_Display(void)
* @date       :2022-06-22
* @function   :在OLED屏幕中显示
* @parameters :无
* @retvalue   :无
********************************************************************/ 
void OLED_Display(void)
{
 uint8_t i,n;
 for(i=0;i<PAGE_SIZE;i++)  
 {  
   OLED_Write_Byte (YLevel+i,OLED_CMD);			//设置页地址（0~7）
   OLED_Write_Byte (XLevelL,OLED_CMD);				//设置显示位置—列低地址
   OLED_Write_Byte (XLevelH,OLED_CMD);				//设置显示位置—列高地址
   for(n=0;n<WIDTH;n++)
   {
     OLED_Write_Byte(OLED_buffer[i*WIDTH+n],OLED_DATA);
   }
 }		//更新显示
}



/*******************************************************************
 * @name       :void OLED_Set_Pixel(unsigned char x, unsigned char y,unsigned char color)
 * @date       :2022-06-22
 * @function   :将像素值设置为RAM
 * @parameters :x:像素的x坐标
                y:像素的y坐标
								color:像素点的颜色值
								      1-白色
											0-黑色
 * @retvalue   :无
********************************************************************/ 
void OLED_Set_Pixel(unsigned char x, unsigned char y,unsigned char color)
{
	if(color)
	{
		OLED_buffer[(y/PAGE_SIZE)*WIDTH+x]|= (1<<(y%PAGE_SIZE))&0xff;
	}
	else
	{
		OLED_buffer[(y/PAGE_SIZE)*WIDTH+x]&= ~((1<<(y%PAGE_SIZE))&0xff);
	}
}


/*******************************************************************
 * @name       :void OLED_Reset(void)
 * @date       :2022-06-22
 * @function   :重置OLED屏幕显示
 * @parameters :dat:0-显示全黑
                    1-显示全白
 * @retvalue   :无
********************************************************************/
void OLED_Reset(void)
{
	OLED_RST_PIN_Set();
	vTaskDelay(100 / portTICK_PERIOD_MS);
	OLED_RST_PIN_Clr();
	vTaskDelay(100 / portTICK_PERIOD_MS);
	OLED_RST_PIN_Set();
}


/*******************************************************************
 * @name       :void OLED_Set_Pos(unsigned char x, unsigned char y)
 * @date       :2022-06-22
 * @function   :在OLED屏幕中设置坐标
 * @parameters :x:x坐标
                y:y坐标
 * @retvalue   :无
********************************************************************/
void OLED_Set_Pos(uint8_t x, uint8_t y) 
{
    // OLED_Write_Byte(0xb0 + y, OLED_CMD);     // 设置页地址
    // OLED_Write_Byte(((x & 0xf0) >> 4) | 0x10, OLED_CMD); // 设置列地址高4位
    // OLED_Write_Byte((x & 0x0f), OLED_CMD);   // 设置列地址低4位
	OLED_Write_Byte(YLevel+y/PAGE_SIZE,OLED_CMD);
	OLED_Write_Byte((((x+2)&0xf0)>>4)|0x10,OLED_CMD);
	OLED_Write_Byte(((x+2)&0x0f),OLED_CMD);
}


/*******************************************************************
 * @name       :void OLED_Display_Off(void)
 * @date       :2022-06-22
 * @function   :关闭OLED显示
 * @parameters :无
 * @retvalue   :无
********************************************************************/ 
void OLED_Display_Off(void)
{
	OLED_Write_Byte(0X8D,OLED_CMD);		//SET DCDC命令
	OLED_Write_Byte(0X10,OLED_CMD);		//DCDC OFF
	OLED_Write_Byte(0XAE,OLED_CMD);		//DISPLAY OFF
}



/*******************************************************************
 * @name       :void OLED_Display_On(void)
 * @date       :2022-06-22
 * @function   :打开OLED显示
 * @parameters :无
 * @retvalue   :无
********************************************************************/ 	
void OLED_Display_On(void)
{
    OLED_Write_Byte(0X8D,OLED_CMD);		//SET DCDC命令
	OLED_Write_Byte(0X14,OLED_CMD);		//DCDC ON
	OLED_Write_Byte(0XAF,OLED_CMD);		//DISPLAY ON
}


/*******************************************************************
 * @name       :void OLED_Write_Byte(unsigned dat,unsigned cmd)
 * @date       :2022-06-22
 * @function   :将一个字节的内容写入OLED屏幕
 * @parameters :dat:要编写的内容
                cmd:0-写入命令
								    1-写入数据
 * @retvalue   :无
********************************************************************/
void OLED_Write_Byte(uint8_t dat, uint8_t cmd)
{
    esp_err_t ret;
    spi_transaction_t t;
    memset(&t, 0, sizeof(t));

    if(cmd) {
        OLED_DC_PIN_Set();
    } else {
        OLED_DC_PIN_Clr();
    }

    t.length = 8;
    t.tx_buffer = &dat;
    t.user = (void*)0;

    OLED_CS_PIN_Clr();
    ret = spi_device_polling_transmit(oled_SPI, &t);
    assert(ret == ESP_OK);
    OLED_CS_PIN_Set();
}


/*******************************************************************
 * @name       :void OLED_DrawPoint(uint8_t x,uint8_t y,uint8_t color)
 * @date       :2022-06-22
 * @function   :在OLED屏幕中绘制点
 * @parameters :x:点的x坐标
                y:点的y坐标
								color:点的颜色值
								      1-白色
											0-黑色
 * @retvalue   :无
********************************************************************/
void OLED_DrawPoint(uint8_t x,uint8_t y,uint8_t color)
{
	OLED_Set_Pixel(x,y,color);
	// OLED_Display();
}


/*******************************************************************
 * @name       :void OLED_Fill(uint8_t sx,uint8_t sy,uint8_t ex,uint8_t ey,uint8_t color)
 * @date       :2022-06-22
 * @function   :填充指定区域
 * @parameters :sx:指定填充区域的起始x坐标
                sy:指定填充区域的起始y坐标
								ex:指定填充区域的结束x坐标
								ey:指定填充区域的结束y坐标
								color:指定区域的颜色值
								      1-白色
											0-黑色
 * @retvalue   :无
********************************************************************/
void OLED_Fill(uint8_t sx,uint8_t sy,uint8_t ex,uint8_t ey,uint8_t color)
{
	uint8_t i,j;
	uint8_t width=ex-sx+1;			//得到填充的宽度
	uint8_t height=ey-sy+1;		//高度
	for(i=0;i<height;i++)
	{
		for(j=0;j<width;j++)
		{
				OLED_Set_Pixel(sx+j, sy+i,color);
		}
	}
	// OLED_Display();
}


/*******************************************************************
 * @name       :void OLED_DrawLine(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2,uint8_t color)
 * @date       :2022-06-22
 * @function   :在两点之间画一条线
 * @parameters :x1:线的起始x点坐标
                y1:线的起始y点坐标
								x2:线的结束x点坐标
								y2:线的结束y点坐标
								color:线条的颜色值
								      1-白色
											0-黑色
 * @retvalue   :无
********************************************************************/
void OLED_DrawLine(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2,uint8_t color)
{
	uint16_t t;
	int xerr=0,yerr=0,delta_x,delta_y,distance;
	int incx,incy,uRow,uCol;
	
	delta_x=x2-x1; //计算坐标增量
	delta_y=y2-y1;
	uRow=x1;
	uCol=y1;
	if(delta_x>0)incx=1; //设置单步方向
	else if(delta_x==0)incx=0;//垂直线
	else {incx=-1;delta_x=-delta_x;}
	if(delta_y>0)incy=1;
	else if(delta_y==0)incy=0;//水平线
	else{incy=-1;delta_y=-delta_y;}
	if( delta_x>delta_y)distance=delta_x; //选取基本增量坐标轴
	else distance=delta_y;
	for(t=0;t<=distance+1;t++ )//画线输出
	{
		OLED_Set_Pixel(uRow,uCol,color);
		xerr+=delta_x ;
		yerr+=delta_y ;
		if(xerr>distance)
		{
			xerr-=distance;
			uRow+=incx;
		}
		if(yerr>distance)
		{
			yerr-=distance;
			uCol+=incy;
		}
	}
	// OLED_Display();
}



/*****************************************************************************
 * @name       :void OLED_DrawRectangle(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2,uint8_t color)
 * @date       :2022-06-22
 * @function   :绘制矩形
 * @parameters :x1:矩形的起始x坐标
                y1:矩形的起始y坐标
								x2:矩形的结束x坐标
								y2:矩形的结束y坐标
								color:线条的颜色值
								      1-白色
											0-黑色
 * @retvalue   :无
******************************************************************************/
void OLED_DrawRectangle(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2,uint8_t color)
{
	OLED_DrawLine(x1,y1,x2,y1,color);
	OLED_DrawLine(x1,y1,x1,y2,color);
	OLED_DrawLine(x1,y2,x2,y2,color);
	OLED_DrawLine(x2,y1,x2,y2,color);
}


/*****************************************************************************
 * @name       :void OLED_FillRectangle(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2,uint8_t color)
 * @date       :2022-06-22
 * @function   :填充矩形
 * @parameters :x1:填充矩形的起始x坐标
                y1:填充矩形的起始y坐标
								x2:填充矩形的结束x坐标
								y2:填充矩形的结束y坐标
								color:矩形的颜色值
								      1-白色
											0-黑色
 * @retvalue   :无
******************************************************************************/ 
void OLED_FillRectangle(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2,uint8_t color)
{
	OLED_Fill(x1,y1,x2,y2,color);
}



static void _draw_circle_8(uint8_t xc, uint8_t yc, uint8_t x, uint8_t y, uint8_t color)
{
	OLED_Set_Pixel(xc + x, yc + y, color);
	OLED_Set_Pixel(xc - x, yc + y, color);
	OLED_Set_Pixel(xc + x, yc - y, color);
	OLED_Set_Pixel(xc - x, yc - y, color);
	OLED_Set_Pixel(xc + y, yc + x, color);
	OLED_Set_Pixel(xc - y, yc + x, color);
	OLED_Set_Pixel(xc + y, yc - x, color);
	OLED_Set_Pixel(xc - y, yc - x, color);
}
/*****************************************************************************
 * @name       :void OLED_DrawCircle(uint8_t xc, uint8_t yc, uint8_t color, uint8_t r)
 * @date       :2022-06-22
 * @function   :在指定位置绘制指定大小的圆
 * @parameters :xc:圆心的x坐标
                yc:圆心的y坐标
								r:圆形半径
								color:圆的颜色值
								      1-白色
											0-黑色
 * @retvalue   :无
******************************************************************************/
void OLED_DrawCircle(uint8_t xc, uint8_t yc, uint8_t color, uint8_t r)
{
	int x = 0, y = r,d;
	d = 3 - 2 * r;
	while (x <= y)
	{
		_draw_circle_8(xc, yc, x, y, color);
		if (d < 0)
		{
				d = d + 4 * x + 6;
		}
		else
		{
				d = d + 4 * (x - y) + 10;
				y--;
		}
		x++;
	}
	// OLED_Display();
}


/*****************************************************************************
 * @name       :void OLED_FillCircle(uint8_t xc, uint8_t yc, uint8_t color, uint8_t r)
 * @date       :2022-06-22
 * @function   :在指定位置填充指定大小的圆
 * @parameters :xc:圆心的x坐标
                yc:圆心的y坐标
								r:圆形半径
								color:圆的颜色值
								      1-白色
											0-黑色
 * @retvalue   :无
******************************************************************************/
void OLED_FillCircle(uint8_t xc, uint8_t yc, uint8_t color, uint8_t r)
{
	int x = 0, y = r, yi, d;
	d = 3 - 2 * r;
	while (x <= y)
	{
			for (yi = x; yi <= y; yi++)
			{
				_draw_circle_8(xc, yc, x, yi, color);
			}
			if (d < 0)
			{
				d = d + 4 * x + 6;
			}
			else
			{
				d = d + 4 * (x - y) + 10;
				y--;
			}
			x++;
	}
	// OLED_Display();
}


/**********************************************************************************
 * @name       :void OLED_DrawTriangel(uint8_t x0,uint8_t y0,uint8_t x1,uint8_t y1,uint8_t x2,uint8_t y2,uint8_t color)
 * @date       :2022-06-22 
 * @function   :在指定位置绘制三角形
 * @parameters :x0:三角形的第一点x坐标
                y0:三角形的第一点y坐标
								x1:三角形的第二点x坐标
								y1:三角形的第二点y坐标
								x2:三角形的第三点x坐标
								y2:三角形的第三点y坐标
								color:三角形的颜色值
								      1-白色
											0-黑色
 * @retvalue   :无
***********************************************************************************/
void OLED_DrawTriangel(uint8_t x0,uint8_t y0,uint8_t x1,uint8_t y1,uint8_t x2,uint8_t y2,uint8_t color)
{
	OLED_DrawLine(x0,y0,x1,y1,color);
	OLED_DrawLine(x1,y1,x2,y2,color);
	OLED_DrawLine(x2,y2,x0,y0,color);
}



/*****************************************************************************
 * @name       :static void _swap(uint8_t *a, uint8_t *b)
 * @date       :2022-06-22
 * @function   :交换两个号码（内部通话）
 * @parameters :a:第一个号码的地址
								b:第二个号码的地址
 * @retvalue   :无
******************************************************************************/
static void _swap(uint8_t *a, uint8_t *b)
{
	uint16_t tmp;
  tmp = *a;
	*a = *b;
	*b = tmp;
}

/*****************************************************************************
 * @name       :static void _draw_h_line(uint8_t x0,uint8_t x1,uint8_t y,uint8_t color)
 * @date       :2022-06-22
 * @function   :在显示屏中画一条水平线（内部调用）
 * @parameters :x0:水平线的起始x坐标
                x1:水平线的结束x坐标
								y:水平线的y坐标
								color:线的颜色值
								      1-白色
											0-黑色
 * @retvalue   :无
******************************************************************************/
static void _draw_h_line(uint8_t x0,uint8_t x1,uint8_t y,uint8_t color)
{
	uint8_t i=0;
	for(i=x0;i<=x1;i++)
	{
		OLED_Set_Pixel(i, y, color);
	}
}

/*****************************************************************************
 * @name       :void OLED_FillTriangel(uint8_t x0,uint8_t y0,uint8_t x1,uint8_t y1,uint8_t x2,uint8_t y2,uint8_t color)
 * @date       :2022-06-22
 * @function   :在指定位置填充三角形
 * @parameters :x0:三角形的第一点x坐标
                y0:三角形的第一点y坐标
								x1:三角形的第二点x坐标
								y1:三角形的第二点y坐标
								x2:三角形的第三点x坐标
								y2:三角形的第三点y坐标
								color:三角形的颜色值
								      1-白色
											0-黑色
 * @retvalue   :无
******************************************************************************/
void OLED_FillTriangel(uint8_t x0,uint8_t y0,uint8_t x1,uint8_t y1,uint8_t x2,uint8_t y2,uint8_t color)
{
	uint8_t a, b, y, last;
	int dx01, dy01, dx02, dy02, dx12, dy12;
	long sa = 0;
	long sb = 0;
 	if (y0 > y1)
	{
    _swap(&y0,&y1);
		_swap(&x0,&x1);
 	}
 	if (y1 > y2)
	{
    _swap(&y2,&y1);
		_swap(&x2,&x1);
 	}
  if (y0 > y1)
	{
    _swap(&y0,&y1);
		_swap(&x0,&x1);
  }
	if(y0 == y2)
	{
		a = b = x0;
		if(x1 < a)
    {
			a = x1;
    }
    else if(x1 > b)
    {
			b = x1;
    }
    if(x2 < a)
    {
			a = x2;
    }
		else if(x2 > b)
    {
			b = x2;
    }
		_draw_h_line(a,b,y0,color);
    return;
	}
	dx01 = x1 - x0;
	dy01 = y1 - y0;
	dx02 = x2 - x0;
	dy02 = y2 - y0;
	dx12 = x2 - x1;
	dy12 = y2 - y1;
	
	if(y1 == y2)
	{
		last = y1;
	}
  else
	{
		last = y1-1;
	}
	for(y=y0; y<=last; y++)
	{
		a = x0 + sa / dy01;
		b = x0 + sb / dy02;
		sa += dx01;
    sb += dx02;
    if(a > b)
    {
			_swap(&a,&b);
		}
		_draw_h_line(a,b,y,color);
	}
	sa = dx12 * (y - y1);
	sb = dx02 * (y - y0);
	for(; y<=y2; y++)
	{
		a = x1 + sa / dy12;
		b = x0 + sb / dy02;
		sa += dx12;
		sb += dx02;
		if(a > b)
		{
			_swap(&a,&b);
		}
		_draw_h_line(a,b,y,color);
	}
	// OLED_Display();
}



/*****************************************************************************
 * @name       :void OLED_ShowChar(uint8_t x,uint8_t y,uint8_t chr,uint8_t Char_Size,uint8_t mode)
 * @date       :2022-06-22
 * @function   :显示单个英文字符
 * @parameters :x:字符显示位置的起始x坐标
                y:字符显示位置的起始y坐标
								chr:显示字符的ascii码（0～94）
								Char_Size:显示字符的大小（8,16）
								mode:0-白底黑字
								     1-黑底白字
 * @retvalue   :无
******************************************************************************/
void OLED_ShowChar(uint8_t x,uint8_t y,uint8_t chr,uint8_t Char_Size,uint8_t mode)
{
	unsigned char c=0,i=0,tmp,j=0;
	c=chr-' ';											//得到偏移后的值
	if(x>WIDTH-1){x=0;y=y+2;}
	if(Char_Size ==16)
	{
		for(i=0;i<16;i++)
		{
			if(mode)
			{
				tmp = F8X16[c*16+i];
			}
			else
			{
				tmp = ~(F8X16[c*16+i]);
			}
			for(j=0;j<8;j++)
			{
				if(tmp&(0x80>>j))
				{
					OLED_Set_Pixel(x+j, y+i,1);
				}
				else
				{
					OLED_Set_Pixel(x+j, y+i,0);
				}
			}
		}
	}
	else if(Char_Size==8)
	{
		for(i=0;i<8;i++)
		{
			if(mode)
			{
				tmp = F6x8[c][i];
			}
			else
			{
				tmp = ~(F6x8[c][i]);
			}
			for(j=0;j<8;j++)
			{
				if(tmp&(0x80>>j))
				{
					OLED_Set_Pixel(x+j, y+i,1);
				}
				else
				{
					OLED_Set_Pixel(x+j, y+i,0);
				}
			}
		}
	}
	else
	{
		return;
	}
	// OLED_Display();
}



/*****************************************************************************
 * @name       :uint32_t mypow(uint8_t m,uint8_t n)
 * @date       :2022-06-22 
 * @function   :获取m的n次方（内部调用）
 * @parameters :m:the multiplier
                n:the power
 * @retvalue   :m的n次方
******************************************************************************/
static uint32_t mypow(uint8_t m,uint8_t n)
{
	uint32_t result=1;
	while(n--)result*=m;
	return result;
}

/*****************************************************************************
 * @name       :void OLED_ShowNum(uint8_t x,uint8_t y,uint32_t num,uint8_t len,uint8_t Size,uint8_t mode)
 * @date       :2022-06-22 
 * @function   :显示数字
 * @parameters :x:数字的起始x坐标
                y:数字的起始y坐标
								num:数字（0～4294967295）
								len:显示数字的长度
								Size:显示数字的大小
								mode:0-白底黑字
								     1-黑底白字
 * @retvalue   :无
******************************************************************************/
// void OLED_ShowNum(uint8_t x,uint8_t y,uint32_t num,uint8_t len,uint8_t Size,uint8_t mode)
// {
// 	uint8_t t,temp;
// 	uint8_t enshow=0,csize;
//   if(Size == 16)
//   {
// 	  csize = Size/2;
// 	}
//   else if(Size == 8)
//   {
// 	  csize = Size/2+2;
// 	}
// 	else
// 	{
// 		return;
// 	}
// 	for(t=0;t<len;t++)
// 	{
// 		temp=(num/mypow(10,len-t-1))%10;
// 		if(enshow==0&&t<(len-1))
// 		{
// 			if(temp==0)
// 			{
// 				OLED_ShowChar(x+csize*t,y,' ',Size,mode);
// 				continue;
// 			}else enshow=1;
			
// 		}
// 	 	OLED_ShowChar(x+csize*t,y,temp+'0',Size,mode);
// 	}
// }
// 添加静态变量存储上次显示状态
static uint32_t last_num = 0;
static uint8_t last_digits[10] = {0}; // 最大支持10位数字
static uint8_t last_len = 0;

void OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, uint8_t Size, uint8_t mode) 
{
    // 参数校验
    if(Size != 8 && Size != 16) return;
    if(len > 10) len = 10; // 最大支持10位数字
    
    // 计算字符尺寸
    const uint8_t char_width = (Size == 16) ? 8 : 6;
    const uint8_t char_height = Size;
    const uint8_t csize = (Size == 16) ? Size/2 : Size/2+2;
    
    // 数字未变化时直接返回
    if(num == last_num && len == last_len) return;
    
    // 临时存储当前数字各数位
    uint8_t current_digits[10] = {0};
    uint32_t temp_num = num;
    
    // 分解数字到数组（从高位到低位）
    for(int8_t i = len-1; i >= 0; i--) {
        current_digits[i] = temp_num % 10;
        temp_num /= 10;
    }
    
    // 差异化刷新逻辑
    uint8_t enshow = 0;
    for(uint8_t t = 0; t < len; t++) {
        const uint8_t temp = current_digits[t];
        
        // 前导零处理
        if(!enshow && t < (len-1)) {
            if(temp == 0) {
                // 需要清除旧内容的情况
                if(last_digits[t] != 0xFF) { 
                    OLED_Fill(x + t*csize, y, x + t*csize + char_width-1, y + char_height-1, !mode);
                    last_digits[t] = 0xFF; // 标记为空
                }
                continue;
            } else {
                enshow = 1;
            }
        }
        
        // 仅更新变化的数位
        if(current_digits[t] != last_digits[t] || mode != (last_num>>31)) {
            // 清除旧内容
            if(last_digits[t] != 0xFF) {
                OLED_Fill(x + t*csize, y, x + t*csize + char_width-1, y + char_height-1, !mode);
            }
            
            // 绘制新数字
            OLED_ShowChar(x + t*csize, y, temp + '0', Size, mode);
            
            // 更新记录
            last_digits[t] = current_digits[t];
        }
    }
    
    // 处理缩短位数时的残留（如从1000变成999）
    for(uint8_t t = len; t < last_len; t++) {
        OLED_Fill(x + t*csize, y, x + t*csize + char_width-1, y + char_height-1, !mode);
        last_digits[t] = 0xFF;
    }
    
    // 保存当前状态
    last_num = num;
    last_len = len;
    last_num = (last_num & 0x7FFFFFFF) | (mode << 31); // 用最高位存储mode
}





/*****************************************************************************
 * @name       :void OLED_ShowString(uint8_t x,uint8_t y,uint8_t *chr,uint8_t Char_Size,uint8_t mode)
 * @date       :2022-06-22 
 * @function   :显示英文字符串
 * @parameters :x:英语字符串的起始x坐标
                y:英语字符串的起始y坐标
								chr:英文字符串的起始地址
								Char_Size:显示字符的大小
								mode:0-白底黑字
								     1-黑底白字
 * @retvalue   :无
******************************************************************************/
void OLED_ShowString(uint8_t x, uint8_t y,const char *chr, uint8_t Char_Size, uint8_t mode)
{
	unsigned char j=0,csize;
	if(Char_Size == 16)
  {
	  csize = Char_Size/2;
	}
  else if(Char_Size == 8)
  {
	  csize = Char_Size/2+2;
	}
	else
	{
		return;
	}
	while (chr[j]!='\0')
	{
		OLED_ShowChar(x,y,chr[j],Char_Size,mode);
		x+=csize;
		if(x>120)
		{
			x=0;
			y+=Char_Size;
		}
		j++;
	}
}



/*****************************************************************************
 * @name       :void OLED_DrawBMP(uint8_t x,uint8_t y,uint8_t width, uint8_t height, uint8_t BMP[], uint8_t mode)
 * @date       :2022-06-22
 * @function   :显示BMP单色图片
 * @parameters :x:BMP单色图片的起始x坐标
                y:BMP单色图片的起始y坐标
								width:BMP单色图片的宽度
								height:BMP单色图片的高度
								BMP:BMP单色图像阵列的起始地址
								mode:0-白底黑字
								     1-黑底白字
 * @retvalue   :无
******************************************************************************/
void OLED_DrawBMP(uint8_t x,uint8_t y,uint8_t width, uint8_t height, uint8_t BMP[], uint8_t mode)
{
 uint8_t i,j,k;
 uint8_t tmp;
 for(i=0;i<height;i++)
 {
		for(j=0;j<(width+7)/8;j++)
		{
			if(mode)
			{
				tmp = BMP[i*((width+7)/8)+j];
			}
			else
			{
				tmp = ~BMP[i*((width+7)/8)+j];
			}
			for(k=0;k<8;k++)
			{
				if(tmp&(0x80>>k))
				{
					OLED_Set_Pixel(x+j*8+k, y+i,1);
				}
				else
				{
					OLED_Set_Pixel(x+j*8+k, y+i,0);
				}
			}
		}
	}
	// OLED_Display();
}







void oled_test() {
  // 清屏
  OLED_Clear(0x00);
  
  // // 显示文本
  // display.setTextSize(1);
  // display.setCursor(0,0);
  // display.print("SPI 速度: ");
  // display.print(40);
  // display.println(" MHz");
  
  // // 绘制图形
  // display.drawLine(0, 15, 128, 15, SSD1306_WHITE);
  // display.fillCircle(64, 40, 10, SSD1306_WHITE);
  
  // // 动态显示计数器
  // display.setTextSize(2);
  // display.setCursor(40, 25);
  // display.print(oled_counter++);
  // if(oled_counter > 9999) oled_counter = 0;
  
  // display.display();
  // delay(25);
}

void reset_oled_counter() {
  oled_counter = 0;
}

void show_start()
{

  // 清屏
  OLED_DrawBMP(0, 0, 128, 64, epd_bitmap_sellectmode, 1);
  OLED_Display();
}



void show_stellar_mode()
{

   OLED_Clear(0x00);
   // 显示文本
   OLED_ShowString(0,0,"      mode    ",16,1);
   
   OLED_ShowString(2,25,"Stellar mode ",8,1);
 
   OLED_ShowString(2,40,"sun mode ",8,1);
 
   OLED_ShowString(2,55,"moon mode ",8,1);
   

   OLED_DrawLine(0, 23, 0, 33, 1);
   OLED_DrawLine(0, 23, 128, 23, 1);
   OLED_DrawLine(0, 33, 128, 33, 1);
   OLED_DrawLine(127, 23, 127, 33, 1);
   OLED_Display();


}

void show_sun_mode()
{

   // 清屏
   OLED_Clear(0x00);
  
  // 显示文本
  OLED_ShowString(0,0,"      mode    ",16,1);
  
  OLED_ShowString(2,25,"Stellar mode ",8,1);

  OLED_ShowString(2,40,"sun mode ",8,1);

  OLED_ShowString(2,55,"moon mode ",8,1);

//   OLED_ShowNum(80,40,tle_angle,10,8,1);

  

  OLED_DrawLine(0, 38, 0, 48, 1);
  OLED_DrawLine(0, 38, 128, 38, 1);
  OLED_DrawLine(0, 48, 128, 48, 1);
  OLED_DrawLine(127, 38, 127, 48, 1);
  OLED_Display();
}



void show_moon_mode()
{

  // 清屏
  OLED_Clear(0x00);
  
  // 显示文本
  OLED_ShowString(0,0,"      mode    ",16,1);
  
  OLED_ShowString(2,25,"Stellar mode ",8,1);

  OLED_ShowString(2,40,"sun mode ",8,1);

  OLED_ShowString(2,55,"moon mode ",8,1);

//   OLED_ShowNum(80,40,tle_angle,10,8,1);

  OLED_DrawLine(0, 53, 0, 63, 1);
  OLED_DrawLine(0, 53, 128, 53, 1);
  OLED_DrawLine(0, 63, 128, 63, 1);
  OLED_DrawLine(127, 53, 127, 63, 1);
  OLED_Display();
}

void show_stop_mode()
{

  // 清屏
  OLED_Clear(0x00);
  
  // 显示文本
  OLED_ShowString(0,0,"      mode    ",16,1);
  
  OLED_ShowString(2,25,"Stellar mode ",8,1);

  OLED_ShowString(2,40,"sun mode ",8,1);

  OLED_ShowString(2,55,"moon mode ",8,1);
  OLED_Display();

//   OLED_ShowNum(80,40,tle_angle,10,8,1);

}


void change_mode()
{
  if(mode_show == 1)
  {
    mode_show = 2;
  }
  else if(mode_show == 2)
  {
    mode_show = 3;   
  }
  else if(mode_show == 3)
  {
    mode_show = 4;   
  }
  else if(mode_show == 4)
  {
    mode_show = 5;   
  }
  else
  {
    mode_show = 2;
  }

}
